import type { LoginData, LoginRes } from '~/types/login'

enum URL {
  login = '/backend/auth/login',
  logout = '/backend/auth/logout',
}

export const login = async (data: LoginData): Promise<any> => {
  const api = useApi()
  return api<LoginRes>(URL.login, { method: 'POST', body: data }) as any
}

export const logout = async (): Promise<any> => {
  const api = useApi()
  return api<any>(URL.logout, { method: 'POST' })
}

