<script lang="ts" setup>
definePageMeta({
  authRequired: true
})
import ScheduledRides from './components/ScheduledRides.vue'
import CompletedRides from './components/CompletedRides.vue'

const activeTab = ref<'scheduled' | 'completed'>('scheduled')

/* 戻るボタンハンドラー */
const handleBack = (): void => {
  window.history.back()
}


</script>
<template>
  <div class="appointment-confirm-change">
    <div class="page-title-header">
      <button class="back-button" @click="handleBack">
        <img src="~/assets/image/Icon.png" alt="戻る" class="back-icon" />
      </button>
      <h1 class="page-title">予約確認・変更・キャンセル</h1>
    </div>

    <div class="content-area">
      <div class="tab-container">
        <div class="custom-tab-toggle">
          <button
            class="tab-button"
            :class="{ 'tab-active': activeTab === 'scheduled' }"
            @click="activeTab = 'scheduled'"
          >
            乗車予定
          </button>
          <button
            class="tab-button"
            :class="{ 'tab-active': activeTab === 'completed' }"
            @click="activeTab = 'completed'"
          >
            乗車済・キャンセル済
          </button>
        </div>
      </div>

      <div class="tab-content">
        <ScheduledRides v-if="activeTab === 'scheduled'" />
        <CompletedRides v-if="activeTab === 'completed'" />
      </div>
    </div>
  </div>
</template>
<style scoped>
.appointment-confirm-change {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  padding-bottom: 90px;
}

.tab-container {
  padding: 8px 16px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-tab-toggle {
  width: 343px;
  height: 40px;
  display: flex;
  align-items: stretch;
}

.tab-button {
  flex: 1;
  height: 40px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #272727;
  background-color: #ffffff;
  border: 1px solid #dfdfdf;
  padding: 6px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  outline: none;
  border-radius: 0;
}

.tab-button:first-child {
  border-radius: 4px 0 0 4px;
  border-right: 1px solid #dfdfdf;
}

.tab-button:last-child {
  border-radius: 0 4px 4px 0;
  border-left: 0;
  font-size: 13px;
}

.tab-button.tab-active {
  background-color: #acd1ed;
  color: #000000;
  font-weight: 500;
  border: 1px solid #dfdfdf;
}

.tab-button:hover:not(.tab-active) {
  background-color: #f5f5f5;
}

.tab-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(172, 209, 237, 0.3);
}

.tab-content {
  background-color: #ffffff;
}

.footer-copyright {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 90px;
  background-color: #9cbcd4;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 22px 0 51px;
}

.border-b {
  border-bottom: 0.5px solid #dfdfdf;
  box-shadow: 0px 3px 10px 0px rgba(0, 0, 0, 0.15);
}
.page-title-header {
  background-color: #ffffff;
  padding: 12px 16px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 16px;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: rgba(38, 73, 157, 0.1);
}

.back-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  transform: rotate(-90deg);
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}
</style>
