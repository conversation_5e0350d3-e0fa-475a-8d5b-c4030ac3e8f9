import { defineStore } from 'pinia'
import type { EventsSpot } from '~/types/special-events'

/* ルート検索tabs用ストア */
export const useSpecialAppStore = defineStore('special-app', {
  state: () => ({
    savedTripType: 'oneway' as 'oneway' | 'roundtrip'
  }),
  actions: {
    setSavedTripType(type: 'oneway' | 'roundtrip') {
      this.savedTripType = type
    }
  }
})

/* 経由地管理用ストア */
export const useSpecialWaypointStore = defineStore('special-waypoint', {
  state: () => ({
    waypoints: [] as EventsSpot[]
  }),
  getters: {
    getAllWaypoints: (state) => {
      return [...state.waypoints]
    },
    getWaypointCount: (state) => state.waypoints.length
  },
  actions: {
    addWaypoint(data: EventsSpot) {
      const exists = this.waypoints.some(
        (waypoint) => waypoint.id === data.id && waypoint.name === data.name
      )

      if (!exists) {
        this.waypoints.push({ ...data })
      }
    },

    updateWaypoint(index: number, data: EventsSpot) {
      if (index >= 0 && index < this.waypoints.length) {
        this.waypoints[index] = { ...data }
      }
    },

    removeWaypoint(index: number) {
      if (index >= 0 && index < this.waypoints.length) {
        this.waypoints.splice(index, 1)
      }
    },

    clearAllWaypoints() {
      this.waypoints = []
    },

    setWaypoints(waypoints: EventsSpot[]) {
      this.waypoints = waypoints.map((item) => ({ ...item }))
    }
  }
})

/* 始発地管理用ストア */
export const useSpecialOriginStore = defineStore('special-origin', {
  state: () => ({
    currentOriginData: null as EventsSpot | null
  }),
  getters: {
    getSelectedOrigin: (state) => {
      return state.currentOriginData
    }
  },
  actions: {
    setCurrentOriginData(data: EventsSpot) {
      this.currentOriginData = { ...data }
    },
    clearCurrentOriginData() {
      this.currentOriginData = null
    }
  }
})

/* 着地管理用ストア */
export const useSpecialSpotStore = defineStore('special-tourist-facilities', {
  state: () => ({
    currentTouristData: null as EventsSpot | null
  }),
  getters: {
    getSelectedSpot: (state) => {
      return state.currentTouristData
    }
  },
  actions: {
    setCurrentTouristData(data: EventsSpot) {
      this.currentTouristData = { ...data }
    },
    clearCurrentTouristData() {
      this.currentTouristData = null
    }
  }
})

// time
export const useTimeStore = defineStore('time', {
  state: () => ({
    chosenDate: '' as string,
    chosenTime: '' as string,
    timeDirection: 'departure' as 'departure' | 'arrival'
  }),
  actions: {
    setTimeData(date: string, time: string) {
      this.chosenDate = date;
      this.chosenTime = time;
    },
    setTimeDirection(direction: 'departure' | 'arrival') {
      this.timeDirection = direction;
    },
    clearTimeData() {
      this.chosenDate = '';
      this.chosenTime = '';
      this.timeDirection = 'departure';
    }
  }
});
