<script lang="ts" setup> 
import { useRouter } from 'vue-router'
const router = useRouter()

/* 前のページに戻る処理 */
function handleBack() {
  window.history.back()
}

/* 会員情報変更ページへ遷移 */
function navigateToMemberAlter() {
  router.push({
    path: `/member-alter`
  })
}

/* パスワード変更ページへ遷移 */
function navigateToPasswordChange() {
  router.push({
    path: `/password-change`
  })
}
</script>
<template>
  <div class="">
    <v-container class="touristPage" fluid>
      <div class="regionHeaderTop">
        <button class="regionSelectorBackBtn" @click="handleBack">
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path
              d="M10 4L6 8l4 4"
              stroke="#26499D"
              stroke-width="1.5"
              fill="none"
            />
          </svg>
        </button>
        <h3 class="regionSelectorTitle">会員情報</h3>
      </div>

      <div class="withdrawal">
        <v-btn @click="navigateToMemberAlter">会員情報変更・退会</v-btn>
      </div>
      <div class="withdrawal">
        <v-btn @click="navigateToPasswordChange">パスワード変更</v-btn>
      </div>
    </v-container>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-btn) {
  background: #e7f2fa !important;
  border: 1px solid #9cbcd4;
  width: 90% !important;
  color: #26499d !important;
  height: 50px !important;
}

.withdrawal {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 16px;
}
</style>
