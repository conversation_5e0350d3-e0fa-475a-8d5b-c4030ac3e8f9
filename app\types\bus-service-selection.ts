export interface Itinerary {
  date: string;
  time: string;
  direction: string;
}

export interface LocalFormData {
  date: string;
  outbound: Itinerary | null;
  return: Itinerary | null;
}

export interface TransferCountItem {
  id: number
  title: string
}

export interface TransferCountItemions {
  id: number
  title: string
}

export interface FlightSelectProps {
  departureCity: string
  arrivalCity: string
  departureTime: string
  localFormData: LocalFormData
  transferCount?: string
  transferCondition?: string
}

export interface ProductItem {
  id: number;
  route: string[];
  title: string;
  description: string;
  origin: string;
  destination: string;
  image: string;
  duration: number;
  transfers: number;
  price: number;
  date: string;
}

export type DatePickerType = '' | 'oneway' | 'outbound' | 'return';




export interface RoutePoint {
  departure: string;
  isFavorite: boolean;
}


export interface TransferInfo {
  isVacant?: boolean;
  arrival: string;
  departure: string;
}


export interface ScheduleSegment {
  date: string;
  departureTime: string;
  transfers?: TransferInfo[];
  arrivalTime: string;
  isVacant?: boolean;
}

export interface TransportRoute {
  id: number;
  departure: string;
  destination: string;
  departureTime: string;
  arrivalTime: string;
  price: number;
  statdate: string;
  entdate: string;
  transferCount: string | number;
  duration: string;
  discountRate?: number;
  originalPrice?: number;
  isFavorite: boolean;
  vacant: string;
  tags: string[];
  schedule?: ScheduleSegment[];
  routePoints?: RoutePoint[];
}


export interface TabItem {
  name: string;
  id: number;
}

export interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
  totalPages: number;
}


export interface ParsedDuration {
  hours: string;
  minutes: string;
}



export interface ProductItem {
    id: number
    route: string[]
    title: string
    description: string
    origin: string
    destination: string
    image: string
    duration: number
    transfers: number
    price: number
    date: string
}
