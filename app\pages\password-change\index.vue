<script lang="ts" setup>
/* ページ設定 */
definePageMeta({
  layout: 'default'
})

// const { changePassword } = usePassword()

/* フォーム参照とバリデーションルール */
const formRef = ref<any>(null)

const validationRules = {
  currentPassword: [
    (v: string) => !!v || '現在のパスワードを入力してください',
    (v: string) => v.length >= 8 || 'パスワードは8文字以上で入力してください'
  ],
  newPassword: [
    (v: string) => !!v || '新しいパスワードを入力してください',
    (v: string) => v.length >= 8 || 'パスワードは8文字以上で入力してください',
    (v: string) => {
      const pattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z0-9]+$/
      return pattern.test(v) || '半角英数字8文字で入力してください'
    },
    (v: string) => {
      return v !== currentPassword.value || '現在のパスワードと異なるパスワードを入力してください'
    }
  ],
  confirmPassword: [
    (v: string) => !!v || '新しいパスワード（確認用）を入力してください',
    (v: string) => v === newPassword.value || 'パスワードが一致しません'
  ]
}

/* ナビゲーション関数 */
const handleBack = (): void => {
  window.history.back()
}

/* フォームバリデーション関数 */
const validateForm = async () => {
  if (!formRef.value) return { valid: false }
  return await formRef.value.validate()
}

/* リアクティブ変数 */
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const errorMessage = ref('')
const hasError = ref(false)
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)
const showCompleteDialog = ref(false)


const errorMessageRef = ref<HTMLElement | null>(null)

/* フォーム有効性の計算プロパティ */
const isFormValid = computed(() => {
  const hasBasicInput =
    currentPassword.value.length >= 8 &&
    newPassword.value.length >= 8 &&
    confirmPassword.value.length >= 8

  const passwordPattern = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z0-9]+$/
  const isNewPasswordValid = passwordPattern.test(newPassword.value)

  const isDifferentFromCurrent = newPassword.value !== currentPassword.value

  const isPasswordMatch = newPassword.value === confirmPassword.value

  return hasBasicInput && isNewPasswordValid && isDifferentFromCurrent && isPasswordMatch
})

/* エラーハンドリング関数 */
const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

const scrollToError = () => {
  nextTick(() => {
    if (errorMessageRef.value) {
      errorMessageRef.value.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  })
}

/* 入力値変更時のウォッチャー */
watch([currentPassword, newPassword, confirmPassword], () => {
  if (hasError.value) {
    clearError()
  }
})

/* パスワード変更処理関数 */
const handlePasswordChange = async () => {
  const validationResult = await validateForm()
  if (!validationResult.valid) {
    hasError.value = true
    errorMessage.value = '入力内容に誤りがあります。再確認してください。'
    scrollToError()
    return
  }

  try {
    clearError()

    // TODO: API実装後に有効化
    // const response = await changePassword(
    //   currentPassword.value,
    //   newPassword.value
    // )
    
    showCompleteDialog.value = true
    
  } catch (error: any) {
    console.error('パスワード変更エラー:', error)
    hasError.value = true
    errorMessage.value =
      error.data?.message ||
      'パスワード変更に失敗しました。再確認してください。'
    scrollToError()
  }
}

/* ダイアログ完了後のTOPページ遷移関数 */
const handleGoToTop = (): void => {
  showCompleteDialog.value = false
  navigateTo('/')
}
</script>
<template>
  <div class="password-change-page">
    <div class="page-title-header">
      <button class="back-button" @click="handleBack">
        <img src="~/assets/image/Icon.png" alt="戻る" class="back-icon" />
      </button>
      <h1 class="page-title">パスワード変更</h1>
    </div>

    <div class="password-change-content">
      <div class="password-change-form-container">
        <v-form ref="formRef" class="password-change-form">
          <div v-if="hasError" ref="errorMessageRef" class="error-message">
            <div class="error-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <path d="M16 3L29.856 26H2.144L16 3Z" fill="#D00000" />
                <path
                  d="M16 12V18"
                  stroke="#FFFFFF"
                  stroke-width="2"
                  stroke-linecap="round"
                />
                <circle cx="16" cy="22" r="1" fill="#FFFFFF" />
              </svg>
            </div>
            <span class="error-text">{{ errorMessage }}</span>
          </div>

          <div class="input-group">
            <div class="input-label">
              <span class="label-text">現在のパスワード</span>
              <span class="required-mark">*</span>
            </div>
            <div class="input-field">
              <v-text-field
                v-model="currentPassword"
                :type="showCurrentPassword ? 'text' : 'password'"
                :rules="validationRules.currentPassword"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
                class="custom-text-field"
                :append-inner-icon="
                  showCurrentPassword ? 'mdi-eye' : 'mdi-eye-off'
                "
                @click:append-inner="showCurrentPassword = !showCurrentPassword"
              />
            </div>
          </div>

          <div class="input-group">
            <div class="input-label">
              <span class="label-text">新しいパスワード</span>
              <span class="required-mark">*</span>
            </div>
            <div class="input-field">
              <v-text-field
                v-model="newPassword"
                :type="showNewPassword ? 'text' : 'password'"
                :rules="validationRules.newPassword"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
                class="custom-text-field"
                :append-inner-icon="showNewPassword ? 'mdi-eye' : 'mdi-eye-off'"
                @click:append-inner="showNewPassword = !showNewPassword"
              />
            </div>
            <div class="input-rule">半角英数字8文字で入力してください</div>
          </div>

          <div class="input-group">
            <div class="input-label">
              <span class="label-text">新しいパスワード（確認用）</span>
              <span class="required-mark">*</span>
            </div>
            <div class="input-field">
              <v-text-field
                v-model="confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                :rules="validationRules.confirmPassword"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
                class="custom-text-field"
                :append-inner-icon="
                  showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'
                "
                @click:append-inner="showConfirmPassword = !showConfirmPassword"
              />
            </div>
          </div>

          <div class="button-section">
            <button
              type="button"
              :disabled="!isFormValid"
              :class="[
                'change-password-btn',
                { active: isFormValid }
              ]"
              @click="handlePasswordChange"
            >
              <span>パスワードを変更する</span>
            </button>
          </div>
        </v-form>
      </div>
    </div>

    <!-- パスワード変更完了ダイアログ -->
    <v-dialog
      v-model="showCompleteDialog"
      persistent
      max-width="343"
      class="password-complete-dialog"
    >
      <v-card class="password-complete-card">
        <div class="dialog-content">
          <!-- タイトル -->
          <h2 class="dialog-title">パスワード変更完了</h2>

          <!-- メッセージ -->
          <div class="message-section">
            <div class="message-container">
              <p class="message-text">
                パスワードの変更が完了しました。
                <br />
                パスワード変更完了メールをお送りしましたので、ご確認ください。
              </p>
            </div>
          </div>

          <!-- ボタン -->
          <div class="button-section-dialog">
            <div class="buttons-container">
              <button class="top-button" @click="handleGoToTop">TOPへ</button>
            </div>
          </div>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>
<style scoped>
.password-change-page {
  width: 375px;
  min-height: calc(100vh - 50px);
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.page-title-header {
  background-color: #ffffff;
  padding: 12px 16px;
  text-align: center;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}

.password-change-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.password-change-form-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}

.password-change-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.label-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
}

.required-mark {
  font-size: 12px;
  font-weight: 500;
  color: #d00000;
}

.input-field {
  width: 100%;
}

.input-rule {
  font-size: 12px;
  font-weight: 400;
  color: #7d7d7d;
  margin-top: 4px;
}

.button-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.change-password-btn {
  width: 343px;
  height: 50px;
  background-color: #fbe4df;
  border: none;
  border-radius: 35px;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.change-password-btn:disabled {
  background-color: #fbe4df;
  opacity: 0.6;
  cursor: not-allowed;
}

.change-password-btn.active {
  background-color: #ed785f;
  color: #ffffff;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.change-password-btn.active:hover {
  background-color: #d66b4a;
}

.change-password-btn.loading {
  background-color: #ed785f;
  color: #ffffff;
  cursor: not-allowed;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 8px;
  animation: fadeInError 0.3s ease-in-out;
}

@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-text {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #d00000;
  line-height: 1.375;
}

:deep(.custom-text-field .v-field) {
  border: 2px solid #dfdfdf;
  border-radius: 2px;
  background-color: #ffffff;
  height: 48px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #7d7d7d;
}

:deep(.custom-text-field .v-field--focused) {
  border-color: #26499d;
}

:deep(.custom-text-field .v-field--error) {
  border-color: #d00000;
}

:deep(.custom-text-field .v-field__input) {
  padding: 10px 18px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #7d7d7d;
  min-height: auto;
}

:deep(.custom-text-field .v-field__outline) {
  display: none;
}

:deep(.custom-text-field .v-messages) {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 12px;
  color: #d00000;
  margin-top: 4px;
}

:deep(.custom-text-field .v-field__append-inner) {
  padding-right: 13px;
}

:deep(.custom-text-field .v-icon) {
  color: #26499d;
  font-size: 24px;
}

@media (max-width: 375px) {
  .password-change-page {
    width: 100%;
    max-width: 375px;
    min-height: calc(100vh - 50px);
  }

  .change-password-btn {
    width: 100%;
    max-width: 343px;
  }

  .error-message {
    margin: 0 -4px 8px -4px;
    padding: 16px 20px;
  }
}
.back-button {
  position: absolute;
  left: 16px;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: rgba(38, 73, 157, 0.1);
}
.back-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  transform: rotate(-90deg);
}

/* パスワード変更完了ダイアログのスタイル */
:deep(.password-complete-dialog .v-overlay__content) {
  margin: 0;
  max-width: 343px;
  width: 343px;
}

.password-complete-card {
  width: 343px;
  height: 320px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  height: 100%;
}

.dialog-title {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  text-align: left;
  width: 311px;
  margin: 0;
}

.message-section {
  width: 311px;
  height: 152px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 311px;
}

.message-text {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714285714;
  color: #000000;
  text-align: left;
  margin: 0;
  width: 100%;
}

.button-section-dialog {
  position: absolute;
  bottom: 16px;
  left: 16px;
  width: 311px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.buttons-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding-top: 24px;
  width: 311px;
}

.top-button {
  width: 311px;
  height: 48px;
  background-color: #26499d;
  border: none;
  border-radius: 4px;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 14px 13.38px;
  transition: background-color 0.2s ease;
}

.top-button:hover {
  background-color: #1e3a7a;
}

.top-button:active {
  background-color: #162d5f;
}
</style>
