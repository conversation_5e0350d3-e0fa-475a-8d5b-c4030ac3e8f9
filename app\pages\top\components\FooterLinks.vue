<script lang="ts" setup>
import { useRouter } from 'vue-router'

/* *
 * ルーターの初期化
 * */
const router = useRouter()

/* *
 * フッターリンクの定義
 * */
const links = [
  {
    text: 'お問い合わせ',
    path: '/contact',
    type: 'internal' as const
  },
  {
    text: '利用規約',
    path: '/terms',
    type: 'internal' as const
  },
  {
    text: 'プライバシーポリシー',
    path: '/privacy',
    type: 'internal' as const
  },
  {
    text: 'サイトマップ',
    path: '/sitemap',
    type: 'internal' as const
  },
  {
    text: '会社情報',
    path: 'https://example.com/company',
    type: 'external' as const
  }
]

/* *
 * リンククリック時の処理
 * */
const handleLinkClick = (linkText: string, event: Event): void => {
  event.preventDefault()

  const linkConfig = links.find((link) => link.text === linkText)

  if (!linkConfig) {
    console.warn(`Link config not found for: ${linkText}`)
    return
  }

  if (linkConfig.type === 'external') {
    handleExternalLink(linkConfig.path)
  } else {
    navigateToPage(linkConfig.path)
  }
}

/* *
 * 内部リンクのナビゲーション処理
 * */
const navigateToPage = (path: string): void => {
  router.push(path)
}

/* *
 * 外部リンクの処理
 * */
const handleExternalLink = (url: string): void => {
  window.open(url, '_blank', 'noopener,noreferrer')
}
</script>
<template>
  <footer>
    <!-- モバイル用背景画像 -->
    <div class="footer-bg footer-bg-mobile">
      <img
        src="~/assets/image/footerLinkImage.png"
        alt="フッター背景画像"
        loading="lazy"
        class="footer-bg-image"
      />
    </div>

    <!-- デスクトップ用背景画像 -->
    <div class="footer-bg footer-bg-desktop">
      <img
        src="~/assets/image/footerLinkImage.png"
        alt="フッター背景画像"
        class="footer-bg-image"
      />
    </div>

    <!-- フッターナビゲーション -->
    <div>
      <div class="footer-nav">
        <ul class="footer-links">
          <li v-for="link in links" :key="link.text" class="footer-item">
            <a
              :href="link.type === 'external' ? link.path : '#'"
              class="footer-link"
              :aria-label="link.text"
              :target="link.type === 'external' ? '_blank' : '_self'"
              :rel="link.type === 'external' ? 'noopener noreferrer' : ''"
              @click="handleLinkClick(link.text, $event)"
            >
              {{ link.text }}
            </a>
          </li>
        </ul>
      </div>
    </div>
  </footer>
</template>
<style scoped lang="scss">
.footer-bg-image,
.footer-bg-images {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.footer-nav {
  background-color: #9cbcd4;
  width: 100%;
  margin: 0 auto;
}

.footer-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.footer-item {
  margin: 0;
  position: relative;
}

.footer-item:not(:last-child)::after {
  content: '';
  display: none;
  width: 1px;
  height: 16px;
  background-color: #ffffff;
  vertical-align: middle;
  margin: 0 16px;
}

.footer-link {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: inline-block;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:visited {
    color: #ffffff;
  }
}

@media (max-width: 320px) {
  .footer-section {
    min-height: 160px;
  }

  .footer-container {
    padding: 50px 12px 25px;
  }

  .footer-links {
    gap: 10px;
  }

  .footer-link {
    font-size: 12px;
    padding: 6px 10px;
  }
}

@media (min-width: 321px) and (max-width: 567px) {
  .footer-bg-desktop {
    display: none;
  }

  .footer-section {
    min-height: 180px;
  }

  .footer-link {
    font-size: 13px;
    padding: 8px 12px;
  }

  .footer-bg-mobile .footer-bg-image {
    object-position: center bottom;
    height: 120px;
  }
}

@media (min-width: 768px) {
  .footer-bg-mobile {
    display: none;
  }

  .footer-bg-desktop {
    display: block;
  }

  .footer-container {
    padding: 70px 40px 45px;
  }

  .footer-links {
    flex-direction: row;
    justify-content: center;
    gap: 0;
    flex-wrap: wrap;
  }

  .footer-link {
    font-size: 16px;
    padding: 12px 0;
  }

  .footer-bg-desktop .footer-bg-image {
    object-position: center center;
  }

  .footer-item:not(:last-child)::after {
    display: inline-block;
  }
}

@media (min-width: 1024px) and (max-width: 1919px) {
  .footer-bg-desktop {
    z-index: 9;
  }

  .footer-container {
    padding: 0;
    height: 232px;
  }

  .footer-bg-images {
    background-color: #9cbcd4;
  }

  .footer-bg-mobile {
    display: none;
  }

  .footer-links {
    width: 100%;
    background-color: #9cbcd4;
  }

  .footer-link {
    font-size: 17px;
  }

  .footer-item:not(:last-child)::after {
    margin: 0 20px;
    height: 18px;
  }
}

@media (min-width: 1920px) {
  .footer-bg-desktop {
    z-index: 9;
  }

  .footer-container {
    padding: 0;
    height: 232px;
  }

  .footer-bg-images {
    background-color: #9cbcd4;
  }

  .footer-bg-mobile {
    display: none;
  }

  .footer-links {
    width: 100%;
    background-color: #9cbcd4;
  }

  .footer-link {
    font-size: 17px;
  }

  .footer-item:not(:last-child)::after {
    display: inline-block;
    height: 18px;
    margin: 0 20px;
  }
}
</style>
