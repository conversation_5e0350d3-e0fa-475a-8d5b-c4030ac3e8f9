<script lang="ts" setup>
	import {
		ref,
		computed,
		onMounted,
		watch
	} from 'vue'
	import DatePicker from '~/components/DatePicker.vue'
	import {
		useRouter
	} from 'vue-router'
	import {
		useRouteStore
	} from '~/stores/bus-service-selection'
	import {
		useTabStore
	} from '~/stores/tabStore'




	const router = useRouter()
	const tabStore = useTabStore()
	const routeStore = useRouteStore()
  
	// 类型定义
	interface Itinerary {
		date: string
		time: string
		direction: 'departure' | 'arrival'
	}

	type DatePickerType = 'oneway' | 'outbound' | 'return' | ''

	interface TransferCountItem {
		title: string
		value: string
	}

	interface FormData {
		date: string
		outbound ? : Itinerary
		return ?: Itinerary
	}

	// 状态管理
	const dialog = ref(false)
	const datePickerOpen = ref(false)
	const currentDateType = ref < DatePickerType > ('')
	const transferCountSelect = ref < string > ('direct')
	const transferConditionSelect = ref < string > ('10')
	const data = ref(getAllTouristData());
	
	// 从 routeStore 获取统一的路由数据
	const routeData = computed(() => routeStore.routeSelected.routeList);

	// 本地表单数据
	const localFormData = ref < FormData > ({
		date: '',
		outbound: {
			date: '',
			time: '',
			direction: 'departure'
		},
		return: {
			date: '',
			time: '',
			direction: 'departure'
		}
	})

	// 筛选条件数据
	const transferCounts: TransferCountItem[] = [{
			title: '直航',
			value: 'direct'
		},
		{
			title: '1回以内',
			value: '1'
		},
		{
			title: '2回以内',
			value: '2'
		}
	]

	const transferConditions: TransferCountItem[] = [{
			title: '徒步10分以内',
			value: '10'
		},
		{
			title: '徒步20分以内',
			value: '20'
		},
		{
			title: '徒步30分以内',
			value: '30'
		}
	]

	// 预订步骤数据
	const bookingSteps = [{
			number: '1',
			description: '便選択'
		},
		{
			number: '2',
			description: 'お客様情報入力'
		},
		{
			number: '3',
			description: '決済'
		},
		{
			number: '4',
			description: '予約完了'
		}
	]

	// alert对话框
	const handleAlertClick = () => {
		dialog.value = true
	}

	// 日期选择器控制
	const openDatePicker = (type: DatePickerType) => {
		currentDateType.value = type
		datePickerOpen.value = true
	}

	const closeDatePicker = () => {
		datePickerOpen.value = false
		currentDateType.value = ''
	}

	// 日期选择处理
	const handleDateSelected = (date: string) => {
		if (!date) return;

		switch (currentDateType.value) {
			case 'oneway':
				// 设置片道日期时，更新 outbound
				localFormData.value.date = date
				localFormData.value.outbound!.date = date
				data.value.dateTime.selected.date = date
				data.value.dateTime.outbound.date = date
				// 直接更新 routeList 的 outbound
				routeStore.routeSelected.routeList.outbound.date = date
				break

			case 'outbound':
				// 设置往復去程日期
				localFormData.value.outbound!.date = date
				data.value.dateTime.outbound.date = date
				routeStore.routeSelected.routeList.outbound.date = date
				break

			case 'return':
				// 设置往復返程日期
				localFormData.value.return!.date = date
				data.value.dateTime.return.date = date
				routeStore.routeSelected.routeList.inbound.date = date
				break
		}
		closeDatePicker()
	}


	// 选中的日期
	const currentSelectedDate = computed(() => {
		switch (currentDateType.value) {
			case 'oneway':
				return localFormData.value.date || routeData.value.outbound.date
			case 'outbound':
				return localFormData.value.outbound ?.date || routeData.value.outbound.date
			case 'return':
				return localFormData.value.return ?.date || routeData.value.inbound.date
			default:
				return ''
		}
	})

	// 条件重置
	const handleConditionReset = () => {
		router.push('/bus-service-selection/bus-searchFiltersReset')
	}

	// 换乘次数变更
	const handleTransferCountChange = (value: string) => {
		transferCountSelect.value = value
		// 直接更新 outbound 中的 transferCount
		routeStore.routeSelected.routeList.outbound.transferCount = value
		// 如果是往复，也更新 inbound 中的 transferCount
		if (routeStore.routeSelected.routeList.tripType === 'roundtrip') {
			routeStore.routeSelected.routeList.inbound.transferCount = value
		}
	}

	// 换乘条件变更
	const handleTransferConditionChange = (value: string) => {
		transferConditionSelect.value = value
		// 直接更新 outbound 中的 transferCondition
		routeStore.routeSelected.routeList.outbound.transferCondition = value
		// 如果是往复，也更新 inbound 中的 transferCondition
		if (routeStore.routeSelected.routeList.tripType === 'roundtrip') {
			routeStore.routeSelected.routeList.inbound.transferCondition = value
		}
	}

	// 生成默认日期（今天）
	const getDefaultDate = () => {
		const today = new Date()
		return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`
	}

	onMounted(() => {
		const defaultDate = getDefaultDate()
		// 从 routeStore 获取初始值
		console.log('BusHeader onMounted - routeData.value:', routeData.value)
		
		let initialDate = routeData.value.outbound.date || ''
		let initialOutboundDate = routeData.value.outbound.date || ''
		let initialTime = routeData.value.outbound.time || ''
		let initialRecipTime = routeData.value.inbound.time || ''

		console.log('BusHeader onMounted - 初始值:', {
			initialDate,
			initialOutboundDate,
			initialTime,
			initialRecipTime
		})

		// 如果都没有，给个默认值
		if (!initialDate) {
			initialDate = defaultDate
			initialOutboundDate = defaultDate
		}

		console.log('BusHeader onMounted - 最终值:', {
			initialDate,
			initialOutboundDate,
			initialTime,
			initialRecipTime
		})

		// 本地表单同步
		localFormData.value.date = initialDate
		localFormData.value.outbound!.date = initialOutboundDate

		// 数据源同步
		data.value.dateTime.selected.date = initialDate
		data.value.dateTime.outbound.date = initialOutboundDate
		data.value.dateTime.selected.time = initialTime
		data.value.dateTime.outbound.time = initialRecipTime
	})


	// 监听tab切换，确保切换到往復时inbound正确显示
	watch(() => tabStore.activeTab, (newTab) => {
		if (newTab === '往復' && !routeData.value.inbound.date) {
			// 如果切换到往復但没有日期，设置默认日期
			const defaultDate = getDefaultDate()
			routeStore.routeSelected.routeList.inbound.date = defaultDate
			localFormData.value.outbound!.date = defaultDate
			data.value.dateTime.outbound.date = defaultDate
		}
	})

	// 监听 routeData 变化，同步到本地显示
	watch(() => routeData.value.outbound.date, (newDate) => {
		if (newDate) {
			data.value.dateTime.selected.date = newDate
			data.value.dateTime.outbound.date = newDate
			localFormData.value.date = newDate
			localFormData.value.outbound!.date = newDate
		}
	})

	watch(() => routeData.value.inbound.date, (newDate) => {
		if (newDate) {
			data.value.dateTime.return.date = newDate
			localFormData.value.return!.date = newDate
		}
	})

	watch(() => routeData.value.outbound.time, (newTime) => {
		if (newTime) {
			data.value.dateTime.selected.time = newTime
			data.value.dateTime.outbound.time = newTime
		}
	})

	watch(() => routeData.value.inbound.time, (newTime) => {
		if (newTime) {
			data.value.dateTime.return.time = newTime
		}
	})

	// 监听时间变化
	watch(() => data.value.dateTime.selected.time, (newTime) => {
		routeStore.routeSelected.routeList.outbound.time = newTime
	})

	// 监听返程时间变化
	watch(() => data.value.dateTime.outbound.time, (newTime) => {
		routeStore.routeSelected.routeList.outbound.time = newTime
	})

	// 监听返程时间变化
	watch(() => data.value.dateTime.return.time, (newTime) => {
		routeStore.routeSelected.routeList.inbound.time = newTime
	})

	// 监听本地日期变化
	watch(() => localFormData.value.date, (newDate) => {
		newDate && (routeStore.routeSelected.routeList.outbound.date = newDate)
	})

	// 监听往復日期变化
	watch(() => localFormData.value.outbound?.date,(newDate) => {
		if (newDate) {
			data.value.dateTime.outbound.date = newDate
			routeStore.routeSelected.routeList.outbound.date = newDate
		}
	})

	// 监听数据源中outbound日期变化
	watch(() => data.value.dateTime.outbound.date, (newDate) => {
		newDate && (routeStore.routeSelected.routeList.outbound.date = newDate)
	})

	watch(() => localFormData.value.return?.date,(newDate) => {
		newDate && (routeStore.routeSelected.routeList.inbound.date = newDate)
	})



</script>

<template>
	<div class="flightSearch">
		<BaseHeader title="便選択" :showBack="true" :showRightIcon="true" @right-click="handleAlertClick" />

		<!-- 出发地和目的地 -->
		<div class="locationContainer">
			<div class="locationInput">
				<span class="mdi mdi-magnify"></span>
				<span class="routeText" style="display: inline-flex; align-items: center; gap: 8px">
					{{ routeData.outbound.departureName }}
					<span class="mdi mdi-arrow-right arrow-icon"></span>
					{{ routeData.outbound.destinationName }}
				</span>
				<div class="additionInfo" @click="handleConditionReset">条件再設定</div>
				<span class="mdi mdi-plus-circle" @click="handleConditionReset"></span>
			</div>
		</div>

		<!-- 往路/復路的判定表示 -->
		<div v-if="routeStore.currentSelectedRouteDirection === 'inbound' && tabStore.activeTab === '往復'">
			<div class="listDate">
				<span class="inbound">往路</span>
				<span class="selected">選択中のルート</span>
			</div>
		</div>

		<!-- 出发日和时间 -->
		<div class="flexContainer">
			<div class="departureDateText">
				<span class="departureTime" v-if="tabStore.activeTab === '片道' && routeData.outbound.date">
					出発日 {{ routeData.outbound.date }}
				</span>
				<!-- 往復页面显示往復日期 -->
				<span class="departureTime" v-if="tabStore.activeTab === '往復' && routeData.outbound.date">
					出発日 {{ routeData.outbound.date }}
				</span>
			</div>

			<div class="datePickerContainer">
				<v-text-field readonly variant="outlined" density="comfortable" hide-details="auto" class="dateInputField" value="最安値"
				 @click="openDatePicker(tabStore.activeTab === '片道' ? 'oneway' : 'outbound')">
					<template #prepend-inner>
						<v-icon color="primary">mdi-calendar</v-icon>
					</template>
				</v-text-field>

				<v-overlay v-model="datePickerOpen" class="dateOverlay align-center justify-center" @click.self="datePickerOpen = false"
				 transition="fade">
					<DatePicker :selectedDateValue="currentSelectedDate" @date-selected="handleDateSelected" @close="closeDatePicker" />
				</v-overlay>
			</div>
		</div>

		<!-- 候选时间 -->
		<div class="candidateTimeContainer">
			<div class="candidateBtn prevCandidate">前の候補</div>
			<div class="departureTimeDisplay">
				<span class="mdi mdi-timer-outline"></span>
				<span class="departureTimeText" v-if="tabStore.activeTab === '片道'">
					出発時間：{{ routeData.outbound.time }}
				</span>
				<span class="departureTimeText" v-if="tabStore.activeTab === '往復'"> 出発時間： {{
          routeData.outbound.time}}</span>
			</div>
			<div class="candidateBtn nextCandidate">次の候補</div>
		</div>

		<!-- 筛选信息 -->
		<div class="flightCard">
			<div class="flightIcons flex">
				<v-icon>mdi-tune</v-icon>
			</div>
			<div class="flightInfo">
				<span class="mdi mdi-airplane flightIcon"></span>
				<div class="flightDetails">
					<div class="airlineInfo">
						乗継回数：
						<v-select dense variant="outlined" class="select-field" placeholder="指定なし" clearable v-model="transferCountSelect"
						 :items="transferCounts" item-title="title" item-value="value" @update:model-value="handleTransferCountChange" />
					</div>

					<div class="airlineInfo">
						乗継条件：
						<v-select dense variant="outlined" class="select-field" placeholder="徒步10分以内" clearable v-model="transferConditionSelect"
						 :items="transferConditions" item-title="title" item-value="value" @update:model-value="handleTransferConditionChange" />
					</div>
				</div>
			</div>
		</div>

		<!-- 对话框 -->
		<v-dialog v-model="dialog" max-width="500" :scrollable="true">
			<v-card title="予約ステップ">
				<v-card-text>
					<v-list v-for="(item, index) in bookingSteps" :key="index">
						<div class="itemNumber">{{ item.number }}</div>
						<div class="itemDescr">{{ item.description }}</div>
					</v-list>
				</v-card-text>

				<v-card-actions class="justify-end pa-4">
					<v-btn class="close-dialog-btn" color="error" variant="flat" @click="dialog = false">
						閉じる
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>





<style lang="scss" scoped>
	.listDate {
		.inbound {
			color: #26499D;
			font-weight: bold;
		}

		.selected {
			color: #7D7D7D;
			margin-left: 10px;
		}

		.routeInfoWithBorder {
			margin-top: 5px;

			.routeInfo {
				display: flex;
				align-items: center;

				.waypoint {
					color: #000000;
					font-size: 16px;
				}
			}
		}

	}

	.listBox {
		display: flex;
		align-items: center;
		padding: 10px 20px;
	}

	.checkIcon {
		width: 20px;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 8px;
	}

	.listTitle {
		flex: 1;
	}

	.mdi {
		color: #26499d;
	}

	.itemNumber {
		color: #000;
		font-size: 16px;
		font-weight: bold;
	}

	.itemDescr {
		color: #000;
		font-size: 16px;
	}

	.close-dialog-btn {
		width: 100% !important;
		height: 48px !important;
		background-color: #e7f2fa !important;
		border: 1px solid #9cbcd4 !important;
		border-radius: 8px !important;
		font-weight: 500 !important;
		font-size: 16px !important;
		line-height: 1.2 !important;
		color: #26499d !important;
		text-transform: none !important;
		box-shadow: none !important;
	}

	.flexContainer {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.departureDateText {
			color: #000;
			font-size: 14px;

			.departureTime {
				font-weight: bold;
				font-size: 16px;
				margin-left: 10px;
			}
		}
	}

	.arrow-icon {
		color: #aebcc5;
		width: 20px;
		height: 20px;
		line-height: 20px;
		text-align: center;
	}

	.locationContainer {
		padding: 0 0 20px 0;
		background: white;
	}

	.locationInput {
		display: flex;
		align-items: center;
		padding: 12px 16px;
		border: 2px solid #26499d;
		border-radius: 25px;
		background: white;
		gap: 12px;

		.routeText {
			flex: 1;
			font-size: 16px;
			color: #1f2937;
			font-weight: 500;
		}

		.additionInfo {
			font-size: 12px;
			color: #666;
		}
	}

	.candidateTimeContainer {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20px;

		.candidateBtn {
			border: 1px solid #ccc;
			border-radius: 4px;
			font-size: 14px;
		}

		.prevCandidate {
			display: inline-block;
			background-color: #224591;
			color: white;
			position: relative;
			margin: 0;
			height: 39px;
			line-height: 39px;
			width: 80px;
			text-align: center;
		}

		.prevCandidate::after {
			content: '';
			position: absolute;
			top: 0;
			left: -19px;
			width: 0;
			height: 37px;
			border-right: 20px solid #224591;
			border-top: 16px solid transparent;
			border-bottom: 16px solid transparent;
		}

		.nextCandidate {
			display: inline-block;
			background-color: #224591;
			color: white;
			position: relative;
			margin: 0;
			height: 39px;
			line-height: 39px;
			width: 80px;
			text-align: center;
		}

		.nextCandidate::after {
			content: '';
			position: absolute;
			top: 0;
			right: -19px;
			width: 0;
			height: 37px;
			border-left: 20px solid #224591;
			border-top: 16px solid transparent;
			border-bottom: 16px solid transparent;
		}

		.departureTimeDisplay {
			display: flex;
			align-items: center;
			padding: 6px 12px;
			border: 1px solid #ccc;
			border-radius: 4px;
			background-color: #fff;
			font-size: 14px;
			width: 48%;

			.mdi-clock {
				margin-right: 6px;
			}

			.departureTimeText {
				color: #333;
				padding-left: 5px;
			}
		}
	}

	.flightCard {
		margin: 0 0 20px 0;
		background: white;
		display: flex;
		align-items: center;

		.flightIcons {
			margin-right: 10px;
			border: 1px solid #e5e7eb;
			width: 50px;
			height: 88px;
			border-radius: 4px;
		}

		.flightInfo {
			border-radius: 4px;
			padding-left: 10px;
			display: flex;
			align-items: center;
			flex: 1;
			border: 1px solid #e5e7eb;

			.flightIcon {
				color: #26499d;
				font-size: 20px;
				margin-right: 10px;
			}

			.flightDetails {
				flex: 1;

				.airlineInfo {
					font-size: 14px;
					display: flex;
					align-items: center;
					margin: 5px 0;
					padding: 0 10px 0 0;

					span {
						color: #4e4f85;
					}
				}
			}
		}
	}

	:deep(.v-field) {
		--v-field-input-padding-top: 0 !important;
		padding: 6px 0 6px 10px !important;
		width: 104px;
	}

	:deep(.v-field__input) {
		--v-field-padding-top: -20px !important;
		padding-bottom: -0 !important;
	}

	:deep(.v-input__details) {
		grid-area: initial !important;
		padding-inline: 0px !important;
	}

	:deep(.v-select__selection-text) {
		color: #4651aa;
		font-weight: bold;
	}
</style>
