<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

interface PassengerCounts {
  adult: { male: number; female: number }
  child: { male: number; female: number }
  student: { male: number; female: number }
  disabilityAdult: { male: number; female: number }
  disabilityChild: { male: number; female: number }
  disability: { male: number; female: number }
}

interface Props {
  modelValue: boolean
  passengerCounts?: PassengerCounts
}

const props = withDefaults(defineProps<Props>(), {
  passengerCounts: () => ({
    adult: { male: 1, female: 1 },
    child: { male: 1, female: 1 },
    student: { male: 0, female: 0 },
    disabilityAdult: { male: 0, female: 0 },
    disabilityChild: { male: 0, female: 0 },
    disability: { male: 0, female: 0 }
  })
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'seats-selected': [seats: string[]]
}>()

const selectedSeats = ref<string[]>([])

/* 座席状態のサンプルデータ */
const seatStatuses = ref<Record<string, 'available' | 'occupied' | 'selected'>>(
  {
    A2: 'occupied',
    A4: 'occupied',
    A5: 'occupied',
    A9: 'occupied',
    A12: 'occupied',
    B2: 'occupied',
    B4: 'occupied',
    B9: 'occupied',
    B12: 'occupied',
    C2: 'occupied',
    C3: 'occupied',
    C8: 'occupied',
    C9: 'occupied',
    D4: 'occupied',
    D6: 'occupied',
    D8: 'occupied',
    D9: 'occupied',
    D12: 'occupied'
  }
)

const selectedSeatsDisplay = computed(() => {
  if (selectedSeats.value.length === 0) {
    return ''
  }
  return selectedSeats.value.join('、')
})

/* 総乗客数を計算 */
const totalPassengerCount = computed(() => {
  if (!props.passengerCounts) {
    return 0
  }

  const counts = props.passengerCounts
  return (
    (counts.adult?.male || 0) +
    (counts.adult?.female || 0) +
    (counts.child?.male || 0) +
    (counts.child?.female || 0) +
    (counts.student?.male || 0) +
    (counts.student?.female || 0) +
    (counts.disabilityAdult?.male || 0) +
    (counts.disabilityAdult?.female || 0) +
    (counts.disabilityChild?.male || 0) +
    (counts.disabilityChild?.female || 0) +
    (counts.disability?.male || 0) +
    (counts.disability?.female || 0)
  )
})

const unselectedCount = computed(() => {
  return totalPassengerCount.value - selectedSeats.value.length
})

/* 性別詳細を生成するヘルパー関数 */
const createGenderDetails = (male: number, female: number): string[] => {
  const details: string[] = []
  if (male > 0) details.push(`男性 ${male} 名`)
  if (female > 0) details.push(`女性 ${female} 名`)
  return details
}

/* 乗客カテゴリ情報を生成するヘルパー関数 */
const createPassengerInfo = (
  category: string,
  male: number,
  female: number
): { category: string; detail: string } | null => {
  const total = male + female
  if (total === 0) return null

  const details = createGenderDetails(male, female)
  return { category, detail: details.join('、') }
}

/* 乗客情報リストを動的生成 */
const passengerInfoList = computed(() => {
  if (!props.passengerCounts) {
    return []
  }

  const counts = props.passengerCounts
  const info: Array<{ category: string; detail: string }> = []

  // 各カテゴリの情報を生成
  const categories = [
    { key: 'adult', label: '大人' },
    { key: 'child', label: '子供' },
    { key: 'student', label: '学生' },
    { key: 'disabilityAdult', label: '早割引' },
    { key: 'disabilityChild', label: '学生割引' },
    { key: 'disability', label: '障がい者割引' }
  ]

  categories.forEach(({ key, label }) => {
    const categoryData = counts[key as keyof PassengerCounts]
    if (categoryData) {
      const passengerInfo = createPassengerInfo(
        label,
        categoryData.male || 0,
        categoryData.female || 0
      )
      if (passengerInfo) {
        info.push(passengerInfo)
      }
    }
  })

  return info
})

const getSeatStatus = (row: string, col: number): string => {
  const seatId = `${row}${col}`
  if (selectedSeats.value.includes(seatId)) {
    return 'selected'
  }
  return seatStatuses.value[seatId] || 'available'
}

const getSeatClass = (row: string, col: number): string => {
  const status = getSeatStatus(row, col)
  return `seat ${status}`
}

/* 座席選択切り替え処理 */
const toggleSeat = (row: string, col: number) => {
  const seatId = `${row}${col}`

  if (seatStatuses.value[seatId] === 'occupied') {
    return
  }

  const index = selectedSeats.value.indexOf(seatId)
  if (index > -1) {
    selectedSeats.value.splice(index, 1)
  } else {
    if (selectedSeats.value.length >= totalPassengerCount.value) {
      return
    }
    selectedSeats.value.push(seatId)
  }
}

const closeDialog = () => {
  emit('update:modelValue', false)
}

const confirmSelection = () => {
  emit('seats-selected', selectedSeats.value)
  closeDialog()
}

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      selectedSeats.value = []
    }
  }
)
</script>

<template>
  <v-overlay
    :model-value="modelValue"
    class="seat-selection-overlay"
    @click:outside="closeDialog"
  >
    <div class="seat-selection-dialog">
      <div class="dialog-header">
        <v-btn icon variant="text" @click="closeDialog" class="back-button">
          <v-icon color="#26499D" size="24">mdi-arrow-left</v-icon>
        </v-btn>
        <div class="header-title">座席選択</div>
      </div>

      <div class="dialog-content">
        <div class="passenger-info-section">
          <div
            v-for="info in passengerInfoList"
            :key="info.category"
            class="passenger-info-item"
          >
            <div class="passenger-category">{{ info.category }}</div>
            <div class="passenger-detail">{{ info.detail }}</div>
          </div>
        </div>

        <div class="seat-legend-section">
          <div class="legend-item">
            <div class="legend-seat available">
              <v-icon
                icon="mdi-circle-outline"
                size="16"
                color="#000000"
              ></v-icon>
            </div>
            <span class="legend-text">選択可</span>
          </div>
          <div class="legend-item">
            <div class="legend-seat selected">
              <v-icon
                icon="mdi-circle-outline"
                size="16"
                color="#000000"
              ></v-icon>
            </div>
            <span class="legend-text">選択中</span>
          </div>
          <div class="legend-item">
            <div class="legend-seat occupied">
              <v-icon size="16" color="#000000">mdi-close</v-icon>
            </div>
            <span class="legend-text">予約済</span>
          </div>
        </div>

        <div class="seat-selection-area">
          <div class="seat-grid">
            <div class="seat-grid-header">
              <div class="corner-cell"></div>
              <div class="row-header-item">A</div>
              <div class="row-header-item">B</div>
              <div class="aisle-vertical"></div>
              <div class="row-header-item">C</div>
              <div class="row-header-item">D</div>
            </div>

            <div v-for="col in 12" :key="col" class="seat-row">
              <div class="col-label">{{ col }}</div>

              <div
                :class="getSeatClass('A', col)"
                @click="toggleSeat('A', col)"
              >
                <v-icon
                  v-if="getSeatStatus('A', col) === 'occupied'"
                  size="16"
                  color="#000000"
                >
                  mdi-close
                </v-icon>
                <v-icon
                  v-else
                  icon="mdi-circle-outline"
                  size="16"
                  color="#000000"
                ></v-icon>
              </div>

              <div
                :class="getSeatClass('B', col)"
                @click="toggleSeat('B', col)"
              >
                <v-icon
                  v-if="getSeatStatus('B', col) === 'occupied'"
                  size="16"
                  color="#000000"
                >
                  mdi-close
                </v-icon>
                <v-icon
                  v-else
                  icon="mdi-circle-outline"
                  size="16"
                  color="#000000"
                ></v-icon>
              </div>

              <div class="aisle-vertical"></div>

              <div v-if="col === 12" class="wc-label">WC</div>
              <div
                v-else
                :class="getSeatClass('C', col)"
                @click="toggleSeat('C', col)"
              >
                <v-icon
                  v-if="getSeatStatus('C', col) === 'occupied'"
                  size="16"
                  color="#000000"
                >
                  mdi-close
                </v-icon>
                <v-icon
                  v-else
                  icon="mdi-circle-outline"
                  size="16"
                  color="#000000"
                ></v-icon>
              </div>

              <div v-if="col === 12" class="wc-spacer"></div>
              <div
                v-else
                :class="getSeatClass('D', col)"
                @click="toggleSeat('D', col)"
              >
                <v-icon
                  v-if="getSeatStatus('D', col) === 'occupied'"
                  size="16"
                  color="#000000"
                >
                  mdi-close
                </v-icon>
                <v-icon
                  v-else
                  icon="mdi-circle-outline"
                  size="16"
                  color="#000000"
                ></v-icon>
              </div>
            </div>
          </div>
        </div>

        <div class="selected-seats-section">
          <div class="selected-seats-title">選択中の座席</div>
          <div style="padding: 0 16px">
            未選択 {{ unselectedCount }}/{{ totalPassengerCount }}
          </div>
        </div>

        <div class="dialog-actions">
          <v-btn
            class="confirm-button"
            :disabled="
              selectedSeats.length === 0 ||
              selectedSeats.length !== totalPassengerCount
            "
            @click="confirmSelection"
            block
          >
            座席決定 ({{ selectedSeats.length }}/{{ totalPassengerCount }})
          </v-btn>
        </div>
      </div>
    </div>
  </v-overlay>
</template>

<style scoped>
.seat-selection-overlay {
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.seat-selection-dialog {
  background-color: white;
  border-radius: 8px;
  width: 90vw;
  max-width: 375px;
  height: 90vh;
  max-height: 900px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin: 20px;
  display: flex;
  flex-direction: column;
}

/* ヘッダー */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 12px 16px;
  border-bottom: 1px solid #dfdfdf;
  background-color: white;
}

.back-button {
  position: absolute;
  left: 16px;
  width: 24px;
  height: 24px;
  min-width: 24px;
}

.header-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  text-align: center;
}

/* コンテンツ */
.dialog-content {
  padding: 12px 16px 16px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 乗客情報セクション */
.passenger-info-section {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.passenger-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.passenger-category {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  min-width: 40px;
}

.passenger-detail {
  font-size: 16px;
  font-weight: 400;
  color: #000000;
}

/* 座席状態凡例 */
.seat-legend-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  margin-bottom: 16px;
  padding: 10px;
}

.legend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.legend-seat {
  width: 48px;
  height: 48px;
  border: 1px solid #000000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.legend-seat.available {
  background-color: #ffffff;
}

.legend-seat.selected {
  background-color: #ed785f;
}

.legend-seat.occupied {
  background-color: #e0e0e0;
}

.legend-text {
  font-size: 10px;
  font-weight: 400;
  color: #000000;
  text-align: center;
}

/* 座席選択エリア */
.seat-selection-area {
  margin-bottom: 16px;
  overflow-x: hidden;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.seat-grid {
  display: flex;
  flex-direction: column;
  gap: 0;
  max-width: 341px;
}

.seat-grid-header {
  display: grid;
  grid-template-columns: 48px 48px 48px 24px 48px 48px;
  gap: 1px;
  margin-bottom: 0;
  width: 100%;
}

.corner-cell {
  width: 48px;
  height: 48px;
  background-color: #fbfeff;
  border: 1px solid #7d7d7d;
}

.row-header-item {
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 400;
  color: #5b5b5b;
  background-color: #fbfeff;
  border: 1px solid #7d7d7d;
}

.seat-row {
  display: grid;
  grid-template-columns: 48px 48px 48px 24px 48px 48px;
  gap: 1px;
  width: 100%;
}

.col-label {
  width: 48px;
  height: 48px;
  background-color: #fbfeff;
  border: 1px solid #7d7d7d;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 400;
  color: #5b5b5b;
}

.seat {
  width: 48px;
  height: 48px;
  border: 1px solid #000000;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.seat.available {
  background-color: #ffffff;
  border-color: #000000;
}

.seat.available:hover {
  background-color: #f0f8ff;
}

.seat.selected {
  background-color: #ed785f;
  border-color: #000000;
}

.seat.occupied {
  background-color: #e0e0e0;
  border-color: #000000;
  cursor: not-allowed;
}

.aisle-vertical {
  width: 24px;
  height: 48px;
  background-color: #c7d4da;
  border: 1px solid #c7d4da;
  display: flex;
  align-items: center;
  justify-content: center;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  font-size: 12px;
  color: #666666;
}

.aisle-space {
  width: 24px;
  height: 48px;
  background-color: transparent;
}

.wc-row {
  display: grid;
  grid-template-columns: 48px 48px 48px 24px 48px 48px;
  gap: 1px;
  width: 100%;
  margin-top: 0;
}

.wc-spacer {
  width: 48px;
  height: 48px;
  background-color: transparent;
}

.wc-label {
  width: 97px;
  height: 48px;
  background-color: #000000;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 400;
  border: 1px solid #000000;
}

/* 選択中の座席情報 */
.selected-seats-section {
  background-color: white;
  border: 1px solid #dfdfdf;
  border-left: none;
  border-right: none;
  padding: 16px 0;
  margin: 0 -16px;
  margin-bottom: 16px;
}

.selected-seats-title {
  font-size: 16px;
  font-weight: 500;
  color: #7d7d7d;
  margin-bottom: 8px;
  padding: 0 16px;
}

.selected-seats-list {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  padding: 0 16px;
}

/* 確認ボタン */
.dialog-actions {
  padding: 16px 0;
}

.confirm-button {
  height: 48px !important;
  background-color: #26499d !important;
  color: white !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  border-radius: 4px !important;
  text-transform: none !important;
  box-shadow: none !important;
}

.confirm-button:hover:not(:disabled) {
  background-color: #1e3a8a !important;
}

.confirm-button:disabled {
  background-color: #94a3b8 !important;
  color: #f8fafc !important;
}

/* レスポンシブ対応 */
@media (max-width: 480px) {
  .seat-selection-dialog {
    width: 95vw;
    height: 95vh;
    margin: 10px;
  }
}

@media (max-width: 375px) {
  .seat-selection-dialog {
    width: 100vw;
    height: 100vh;
    margin: 0;
    border-radius: 0;
  }

  .seat-grid {
    max-width: 325px;
  }

  .seat-grid-header {
    grid-template-columns: 25px 25px 25px 16px 25px 25px;
  }

  .seat-row {
    grid-template-columns: 25px 25px 25px 16px 25px 25px;
  }

  .wc-row {
    grid-template-columns: 25px 25px 25px 16px 25px 25px;
  }

  .corner-cell,
  .row-header-item,
  .col-label {
    width: 25px;
    height: 25px;
    font-size: 16px;
  }

  .seat {
    width: 25px;
    height: 25px;
  }

  .aisle-vertical,
  .aisle-space {
    width: 16px;
    height: 25px;
  }

  .wc-spacer {
    width: 25px;
    height: 25px;
  }

  .wc-label {
    width: 42px;
    height: 25px;
    font-size: 14px;
  }

  .legend-seat {
    width: 36px;
    height: 36px;
  }

  .seat-legend-section {
    gap: 16px;
  }
}
</style>
