<script lang="ts" setup>
import { ref } from 'vue'

const coupons = ref([
  {
    id: 1,
    title: '【乗継応援】10％OFFクーポン',
    description: '乗継ぎ便限定で10%割引',
    content:
      ' 説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説…',
    usageLimit: 10,
    validUntil: '2025年12月24日（水）'
  },
  {
    id: 2,
    title: '平日限定・早期便割引クーポン',
    description: '平日6時出発便が500円引き',
    content:
      ' 説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説…',
    usageLimit: 10,
    validUntil: '2025年6月30日（水）'
  },
  {
    id: 3,
    title: '【リポーター向け】2回目予約特典',
    description: '300円OFF（2回目の予約のみ）',
    content:
      ' 説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説…',
    usageLimit: 10,
    validUntil: '2025年6月30日（水）'
  },
  {
    id: 4,
    title: '学生応援クーポン',
    description: '学生証提示で最大15%OFF',
    content:
      ' 説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説…',
    usageLimit: 10,
    validUntil: '2025年10月31日（水）'
  },
  {
    id: 5,
    title: '友達紹介クーポン',
    description: '紹介された方・紹介者に500円引き',
    content:
      ' 説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説明説…',
    usageLimit: 10,
    validUntil: '2025年9月30日（水）'
  }
])
const fetchCoupons = async () => {
  try {
    // const response = await fetch('/api/coupons')
    // const data = await response.json()
    // coupons.value = data
  } catch (error) {
    console.error('Error fetching coupons:', error)
  }
}

const viewCouponDetails = async (id: number) => {
  await navigateTo({
    path: `/coupon-list/edit/${id}`
  })
}
function handleBack() {
  window.history.back()
}

onMounted(async () => {
  await fetchCoupons()
})
</script>

<template>
  <div class="couponHeaderTop createPadding">
    <v-icon color="#26499d" size="30px" class="pl-1" @click="handleBack">
      mdi-chevron-left
    </v-icon>
    <h3 class="couponSelectorTitle">クーポン一覧</h3>
  </div>
  <div class="coupon-list">
    <v-card v-for="(coupon, index) in coupons" :key="index" class="coupon-card">
      <v-card-title class="coupon-header">
        <span class="coupon-name">{{ coupon.title }}</span>
      </v-card-title>

      <v-card-text>
        <v-row no-gutters>
          <v-col cols="12">
            <span class="coupon-description">{{ coupon.description }}</span>
          </v-col>
          <v-col cols="12">
            <span class="coupon-usage">{{ coupon.content }}</span>
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-actions class="coupon-actions">
        <span class="usage-limit">利用可能回数：{{ coupon.usageLimit }}回</span>
        <span text class="view-details" @click="viewCouponDetails(coupon.id)">
          詳細を見る
        </span>
      </v-card-actions>
      <div class="coupon-validUntil">
        <span class="valid-until">{{ coupon.validUntil }}</span>
        <span class="coupon-usage">まで有効</span>
      </div>
    </v-card>
  </div>
</template>

<style scoped>
.couponHeaderTop {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 48px;
}

.couponSelectorBackBtn {
  background: none;
  border: none;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.couponSelectorTitle {
  color: #26499d;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  font-weight: 400;
}

.coupon-list {
  font-size: 12px;
}

.coupon-header {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
  background-color: #e7f2fa;
  padding: 5px 0px 0px 12px;
  height: 35px;
  border-bottom: 1px solid #9cbcd4;
}

.coupon-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #9cbcd4;
  margin: 16px;
}

.v-card-text {
  padding: 0.5rem;
}

.coupon-title {
  font-size: 12px;
  font-weight: bold;
}

.coupon-description {
  margin-bottom: 8px;
  color: #050505;
}

.coupon-usage {
  color: #888;
  font-size: 12px;
}

.valid-until {
  display: inline-block;
  text-align: center;
  color: #050505;
  font-weight: bold;
}

.coupon-validUntil {
  text-align: center;
  border-top: 1px solid #9cbcd4;
  line-height: 30px;
}

.view-details {
  text-decoration: underline;
  color: #26499d;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
}
.coupon-info {
  display: flex;
  justify-content: space-between; /* This ensures the content is spaced out on both ends */
  align-items: center; /* This ensures items are vertically aligned */
}
.usage-limit {
  line-height: 35px;
  justify-content: space-between;
  color: #050505;
}
.coupon-actions {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>
