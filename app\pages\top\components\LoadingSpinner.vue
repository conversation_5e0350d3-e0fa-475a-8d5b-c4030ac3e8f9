<script lang="ts" setup>
const props = defineProps<{
  isActive: boolean
}>()

const localActive = ref(props.isActive)

const emit = defineEmits<(e: 'close') => void>()

const dotCount = 12
const radius = 30
const dotSize = 10

const getDotStyle = (i: number) => {
  const angle = ((2 * Math.PI) / dotCount) * (i - 1)
  const x = radius * Math.cos(angle)
  const y = radius * Math.sin(angle)
  const scale = 0.3 + (i / dotCount) * 0.7
  const opacity = i / dotCount

  return {
    transform: `translate(${x}px, ${y}px) scale(${scale})`,
    opacity: opacity,
    backgroundColor: '#009fe8'
  }
}

const handleClose = () => {
  localActive.value = false
  emit('close')
}

watch(
  () => props.isActive,
  (val) => {
    localActive.value = val
  }
)
</script>

<template>
  <div>
    <v-dialog v-model="localActive" max-width="500">
      <template v-slot:default="{ localActive }">
        <v-card>
          <div slot="title" class="textCenter">
            便候補が出るまで
            <br />
            しばらくお待ちください。
          </div>

          <div class="loading flex">
            <div class="loadingSpinner">
              <div
                v-for="i in dotCount"
                :key="i"
                class="dot"
                :style="getDotStyle(i)"
              ></div>
            </div>
          </div>

          <div class="titCenter">
            「便選択」画面では、
            <br />
            同じ乗り継ぎルートでも
            <br />
            発車時間違いでも候補が
            <br />
            表示されます。
            <br />
            並び替えのタブで切り替え
            <br />
            要望に合うプレートを
            <br />
            選択してください。
            <br />
          </div>
          <v-card-actions class="justify-center">
            <v-btn class="close" @click="handleClose">閉じる</v-btn>
          </v-card-actions>
        </v-card>
      </template>
    </v-dialog>
  </div>
</template>

<style scoped lang="scss">
.loadingSpinner {
  position: relative;
  width: 80px;
  height: 80px;
  animation: spin 1.2s linear infinite;
  transform-origin: 50% 50%;
}

.dot {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform-origin: 50% 50%;
  margin-top: -5px;
  margin-left: -5px;
}

.textCenter {
  padding-top: 30px;
  text-align: center;
  font-weight: bold;
}

.loading {
  padding: 20px 0 30px 0;
}

.titCenter {
  padding-top: 30px;
  text-align: center;
  color: #e95513;
}

.close {
  color: #000 !important;
  border: 1px solid #99b7cd;
  background-color: #e5eff7;
  width: 8rem;
  border-radius: 10px !important;
  margin: 30px 0 20px 0;
  height: 40px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
