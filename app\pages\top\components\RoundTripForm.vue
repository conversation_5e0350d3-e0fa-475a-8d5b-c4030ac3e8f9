<script lang="ts" setup>
import {
  computed,
  inject,
  watch,
  onMounted,
  nextTick
} from 'vue'
import { useDisplay } from 'vuetify'
import { useRouter, useRoute } from 'vue-router'
import { useFormValidation } from '../../../composables/business/useFormValidation'
import { useDateTimeHandler } from '../../../composables/business/useDateTimeHandler'
import { useLocationSwap, type Waypoint } from '../../../composables/business/useLocationSwap'
import { useWaypoints } from '../../../composables/business/useWaypoints'
import replaceImage from '~/assets/image/replace.png'
import {
  useSpotStore,
  useOriginStore,
  useWaypointStore,
  useDateTimeStore
} from '~/stores/tourist-facilities';
import { useTabStore } from '~/stores/tabStore';
import { useRouteStore } from '~/stores/bus-service-selection';

const tabStore = useTabStore();
const router = useRouter()
const spotStore = useSpotStore();
const originStore = useOriginStore();
const waypointStore = useWaypointStore();
const dateTimeStore = useDateTimeStore();
const routeStore = useRouteStore(); 

interface TripInfo {
  date: string
  time: string
  direction: 'departure' | 'arrival'
}

interface RoundTripFormData {
  departure: string
  destination: string
  outbound: TripInfo
  return: TripInfo
  waypoints: Waypoint[]
}

interface SearchData {
  type: string
  departure: string
  destination: string
  waypoints: string[]
  outbound: TripInfo
  return: TripInfo
  departureId?: string
  departureType?: string
  destinationId?: string
  destinationType?: string
  waypointDetails?: Array<{
    id: string
    location: string
    locationId: string
    locationType: string
  }>
}

interface LocationSwapData {
  departure: string
  destination: string
  waypoints: string[]
}

interface DateTimeHandler {
  datePickerOpen: any
  timePickerOpen: any
  currentDateType: any
  currentTimeType: any
  currentIndex: any
  openDatePicker: (type: string, index?: number) => void
  openTimePicker: (type: string, index?: number) => void
}

interface LocationData {
  name: string
  id: string | number
  type?: 'prefecture' | 'area' | 'busStop'
  coordinates?: { lat: number; lng: number }
  busStopInfo?: string
  parentId?: string | number
  level?: number
  children?: LocationData[]
  hasChildren?: boolean
}

const props = defineProps<{
  modelValue: RoundTripFormData
  tripType?: 'oneway' | 'roundtrip'
}>()

const emit = defineEmits<{
  'update:modelValue': [value: RoundTripFormData]
  search: [data: SearchData]
  locationSwap: [data: LocationSwapData]
}>()

const { mobile } = useDisplay()
const { formRef, validationRules, validateForm } = useFormValidation()
const { formatDate, formatTime } = useDateTimeHandler()
const { swapLocations } = useLocationSwap()
const {
  waypoints,
  showWaypoints,
  addWaypoint,
  removeWaypoint,
  toggleWaypoint,
  canAddMoreWaypoints,
  waypointButtonText,
  getValidWaypoints
} = useWaypoints()

const dateTimeHandler = inject<DateTimeHandler>('dateTimeHandler')
if (!dateTimeHandler) {
  throw new Error('dateTimeHandler not provided')
}
const { openDatePicker, openTimePicker } = dateTimeHandler

const currentTripType = computed(() => {
  if (props.tripType) {
    return props.tripType;
  }

  try {
    const route = useRoute();
    if (route?.query?.tripType) {
      return route.query.tripType as 'oneway' | 'roundtrip';
    }

    if (route?.path && (route.path.includes('roundtrip') || route.name === 'roundtrip')) {
      return 'roundtrip';
    }
  } catch (error) {
    console.warn('useRoute() not available:', error);
  }

  return 'roundtrip';
})

let isUpdatingForm = false;

const localData = computed<RoundTripFormData>({
  get(): RoundTripFormData {
    return props.modelValue
  },
  set(value: RoundTripFormData): void {
    emit('update:modelValue', value)
  }
})

class LocationTypeDetector {
  static detectLocationType(location: LocationData | null): '' | 'prefecture' | 'area' | 'busStop' {
    if (!location) return '';
    if (location.type) return location.type;

    if (this.isBusStop(location)) return 'busStop';
    if (this.isArea(location)) return 'area';
    return 'prefecture';
  }

  private static isBusStop(location: LocationData): boolean {
    return !!(
      location.busStopInfo ||
      this.hasStopKeywords(location.name) ||
      (!location.hasChildren && location.level && location.level >= 3)
    );
  }

  private static isArea(location: LocationData): boolean {
    return !!(
      (location.hasChildren && location.parentId) ||
      (location.level === 2) ||
      this.hasAreaKeywords(location.name)
    );
  }

  private static hasStopKeywords(name: string): boolean {
    return /駅|站|バス停|停留所|乗り場|前|口/.test(name);
  }

  private static hasAreaKeywords(name: string): boolean {
    return /区|區|町|市|村|郡|地区|地區/.test(name);
  }
}

const waypointsCount = computed(() => waypointStore.getWaypointCount);
const currentWaypoints = computed(() => waypointStore.getAllWaypoints);
const selectedSpot = computed(() => spotStore.getSelectedSpot);

const syncWaypointsToForm = () => {
  if (isUpdatingForm) return;

  const waypoints_data = currentWaypoints.value;

  if (!waypoints_data || waypoints_data.length === 0) {
    if (waypoints.value.length === 0) return;
    waypoints.value = [];
    return;
  }

  const currentFormWaypoints = waypoints.value;
  if (currentFormWaypoints.length === waypoints_data.length) {
    const needsUpdate = waypoints_data.some((waypoint, index) => {
      const formWaypoint = currentFormWaypoints[index];
      return !formWaypoint ||
        formWaypoint.id !== waypoint.id?.toString() ||
        formWaypoint.location !== waypoint.name;
    });

    if (!needsUpdate) return;
  }

  const formattedWaypoints = waypoints_data.map((waypoint, index) => ({
    id: waypoint.id?.toString() || index.toString(),
    location: waypoint.name || '',
  }));

  isUpdatingForm = true;
  waypoints.value = formattedWaypoints;
  if (!showWaypoints.value && formattedWaypoints.length > 0) {
    showWaypoints.value = true;
  }

  nextTick(() => {
    isUpdatingForm = false;
  });
}

const updateLocationData = (location: LocationData | null, isDestination = false) => {
  if (isUpdatingForm) return;

  if (!location || !location.name) {
    if (isDestination) {
      localData.value.destination = '';
    } else {
      localData.value.departure = '';
    }
    return;
  }

  if (isDestination) {
    localData.value.destination = location.name;
  } else {
    localData.value.departure = location.name;
  }
}

watch(() => originStore.currentOriginData, (newValue) => {
  updateLocationData(newValue, false);
}, { immediate: true })

watch(() => selectedSpot.value, (newValue) => {
  updateLocationData(newValue, true);
}, { immediate: true })

watch([waypointsCount, currentWaypoints], () => {
  if (isUpdatingForm) return;

  nextTick(() => {
    syncWaypointsToForm();
  });
}, { deep: true, immediate: true })

watch(
  waypoints,
  (newWaypoints: Waypoint[]): void => {
    if (!isUpdatingForm) {
      localData.value.waypoints = [...newWaypoints]
    }
  },
  { deep: true }
)

// Storeからコンポーネントに同期（往路）
watch(
  () => [
    dateTimeStore.outbound.date,
    dateTimeStore.outbound.time,
    dateTimeStore.outbound.direction
  ],
  ([newDate, newTime, newDirection]) => {
    if (!isUpdatingForm) {
      localData.value.outbound = {
        ...localData.value.outbound,
        date: newDate ?? '',
        time: newTime ?? '',
        direction: (newDirection as 'departure' | 'arrival') || 'departure'
      }
    }
  },
  { immediate: true }
)

// コンポーネントからStoreに同期（往路）
watch(
  () => localData.value.outbound,
  (newOutbound) => {
    if (!isUpdatingForm) {
      dateTimeStore.setOutboundDateTime({
        date: newOutbound.date,
        time: newOutbound.time,
        direction: newOutbound.direction
      });
    }
  },
  { deep: true }
)

// Storeからコンポーネントに同期（復路）
watch(
  () => [
    dateTimeStore.return.date,
    dateTimeStore.return.time,
    dateTimeStore.return.direction
  ],
  ([newDate, newTime, newDirection]) => {
    if (!isUpdatingForm) {
      localData.value.return = {
        ...localData.value.return,
        date: newDate ?? '',
        time: newTime ?? '',
        direction: (newDirection as 'departure' | 'arrival') || 'departure'
      }
    }
  },
  { immediate: true }
)

// コンポーネントからStoreに同期（復路）
watch(
  () => localData.value.return,
  (newReturn) => {
    if (!isUpdatingForm) {
      dateTimeStore.setReturnDateTime({
        date: newReturn.date,
        time: newReturn.time,
        direction: newReturn.direction
      });
    }
  },
  { deep: true }
)

// タブ切り替えを監視し、directionをクリアすることを保証
watch(
  () => tabStore.activeTab,
  (newVal) => {
    isUpdatingForm = true;
    const update = { ...localData.value };

    // 往路情報をリセット
    update.outbound = {
      date: '',
      time: '',
      direction: 'departure'
    };

    // 復路情報をリセット
    update.return = {
      date: '',
      time: '',
      direction: 'departure'
    };

    localData.value = update;
    
    dateTimeStore.clearDateTime();
    
    nextTick(() => {
      isUpdatingForm = false;
    });
  },
  { immediate: true }
);

const handleWaypoint = () => {
  router.push({
    path: '/transit-point',
    query: {
      mode: 'waypoint'
    }
  });
}

const handleDeparture = () => {
  router.push({
    path: '/target-location',
    query: {
      mode: 'departure'
    }
  });
}

const handleDestination = () => {
  router.push({
    path: '/origin-location',
    query: {
      mode: 'destination'
    }
  });
}

const isSearchDisabled = computed(() => {
  return (
    !localData.value.departure ||
    !localData.value.destination ||
    !localData.value.outbound.date ||
    !localData.value.outbound.time 
  )
})

const handleSwapLocations = (): void => {
  const result = swapLocations(
    localData.value.departure,
    localData.value.destination,
    waypoints.value
  )

  localData.value.departure = result.departure
  localData.value.destination = result.destination

  if (result.waypoints) {
    waypoints.value = result.waypoints
  }

  emit('locationSwap', {
    departure: result.departure,
    destination: result.destination,
    waypoints: getValidWaypoints()
  })
}

const dynamicDateRules = computed(() => [
  (v: string) => !!v || '日付を選択してください'
])

const dynamicTimeRules = computed(() => [
  (v: string) => !!v || '時間を選択してください'
])

const handleSearch = async (): Promise<{ valid: boolean; searchData?: SearchData }> => {
  const { valid } = await validateForm()

  if (!valid) {
    return { valid: false }
  }

  // 调试信息
  console.log('RoundTripForm handleSearch - localData:', localData.value)
  console.log('RoundTripForm handleSearch - originStore.currentOriginData:', originStore.currentOriginData)
  console.log('RoundTripForm handleSearch - selectedSpot.value:', selectedSpot.value)

  const searchData: SearchData = {
    type: currentTripType.value,
    departure: localData.value.departure,
    destination: localData.value.destination,
    waypoints: getValidWaypoints(),
    outbound: {
      date: localData.value.outbound.date,
      time: localData.value.outbound.time,
      direction: localData.value.outbound.direction
    },
    return: {
      date: localData.value.return.date,
      time: localData.value.return.time,
      direction: localData.value.return.direction
    },

    departureId: originStore.currentOriginData?.id?.toString() || '',
    departureType: LocationTypeDetector.detectLocationType(originStore.currentOriginData),
    destinationId: selectedSpot.value?.id?.toString() || '',
    destinationType: LocationTypeDetector.detectLocationType(selectedSpot.value),
    waypointDetails: waypoints.value.filter(wp => wp.location).map((wp, index) => {
      const storeWaypoint = currentWaypoints.value[index];
      return {
        id: wp.id.toString(),
        location: wp.location,
        locationId: storeWaypoint?.id?.toString() || '',
        locationType: LocationTypeDetector.detectLocationType(storeWaypoint)
      }
    })
  }

  console.log('RoundTripForm handleSearch - searchData:', searchData)

  // 将数据同步到 route store
  routeStore.setFromRoundTripForm(searchData)

  return { valid: true, searchData }
}

defineExpose({
  handleSearch
})

onMounted(() => {
  nextTick(() => {
    syncWaypointsToForm();
  });
})
</script>

<template>
  <v-form ref="formRef">
    <div v-if="!mobile" class="pc-horizontal-layout">
      <div class="location-group">
        <v-text-field v-model="localData.departure" :rules="validationRules.departure" class="location-input mb-4"
          placeholder="出発地を選択" variant="outlined" density="comfortable" hide-details="auto" readonly
          @click="handleDeparture" />

        <div class="controls-section mb-4">
          <v-btn variant="outlined" color="#26499D" class="waypoint-button" prepend-icon="mdi-plus"
            :disabled="showWaypoints && !canAddMoreWaypoints" @click="
              showWaypoints
                ? addWaypoint()
                : toggleWaypoint()
              ">
            {{ waypointButtonText }}
          </v-btn>

          <v-btn icon variant="outlined" color="primary" size="small" class="swap-button" @click="handleSwapLocations">
            <img :src="replaceImage" alt="入れ替え" class="replace-tivImage" />
          </v-btn>
        </div>

        <div v-if="showWaypoints && waypoints.length > 0" class="waypoints-container mb-4">
          <v-fade-transition group>
            <div v-for="(waypoint, index) in waypoints" :key="waypoint.id" class="waypoint-item mb-3">
              <v-row no-gutters align="center">
                <v-col cols="10">
                  <v-text-field v-model="waypoint.location" hide-details class="location-input"
                    :placeholder="`経由地${index + 1}を選択`" variant="outlined" density="comfortable" readonly
                    @click="handleWaypoint" />
                </v-col>
                <v-col cols="2" class="pl-2">
                  <v-btn icon="mdi-close" variant="outlined" color="error" size="small"
                    @click="removeWaypoint(waypoint.id)" />
                </v-col>
              </v-row>
            </div>
          </v-fade-transition>
        </div>

        <v-text-field v-model="localData.destination" :rules="validationRules.destination" class="location-input"
          placeholder="到着地を選択" variant="outlined" density="comfortable" hide-details="auto" readonly
          @click="handleDestination" />
      </div>

      <div class="vertical-divider"></div>

      <div class="datetime-group">
        <div class="trip-section mb-6">
          <p class="leave">行き</p>
          <div class="section-headers">
            <span class="section-title">日付</span>
            <span class="section-title">時間指定（任意）</span>
          </div>

          <div class="date-time-inputs mb-4">
            <v-text-field v-model="localData.outbound.date" :value="formatDate(localData.outbound.date)"
              :rules="dynamicDateRules" variant="outlined" density="comfortable" readonly class="date-display-pc"
              placeholder="日付を選択" hide-details="auto" @click="openDatePicker('outbound')">
              <template #append-inner>
                <v-icon color="primary">mdi-calendar</v-icon>
              </template>
            </v-text-field>

            <v-text-field v-model="localData.outbound.time" :rules="dynamicTimeRules" variant="outlined"
              density="comfortable" readonly class="time-display-pc" placeholder="指定なし" hide-details="auto"
              @click="openTimePicker('outbound')">
              <template #append-inner>
                <v-icon color="primary">mdi-clock</v-icon>
              </template>
            </v-text-field>
          </div>

          <v-radio-group v-model="localData.outbound.direction" class="direction-radio-pc" hide-details>
            <v-radio label="出発" value="departure" color="primary" class="radio-item" />
            <v-radio label="到着" value="arrival" color="primary" class="radio-item" />
          </v-radio-group>
        </div>

        <div class="trip-section">
          <p class="leave">帰り</p>
          <div class="section-headers">
            <span class="section-title">日付</span>
            <span class="section-title">時間指定（任意）</span>
          </div>

          <div class="date-time-inputs mb-4">
            <v-text-field v-model="localData.return.date" :value="formatDate(localData.return.date)"
              :rules="dynamicDateRules" variant="outlined" density="comfortable" readonly class="date-display-pc"
              placeholder="日付を選択" hide-details="auto" @click="openDatePicker('return')">
              <template #append-inner>
                <v-icon color="primary">mdi-calendar</v-icon>
              </template>
            </v-text-field>

            <v-text-field v-model="localData.return.time" :rules="dynamicTimeRules" variant="outlined"
              density="comfortable" readonly class="time-display-pc" placeholder="指定なし" hide-details="auto"
              @click="openTimePicker('return')">
              <template #append-inner>
                <v-icon color="primary">mdi-clock</v-icon>
              </template>
            </v-text-field>
          </div>

          <v-radio-group v-model="localData.return.direction" class="direction-radio-pc" hide-details>
            <v-radio label="出発" value="departure" color="primary" class="radio-item" />
            <v-radio label="到着" value="arrival" color="primary" class="radio-item" />
          </v-radio-group>
        </div>
      </div>
    </div>

    <div v-else class="mobile-vertical-layout">
      <v-text-field v-model="localData.departure" :rules="validationRules.departure" class="location-input mb-4"
        placeholder="出発地を選択" variant="outlined" density="comfortable" hide-details="auto" readonly
        @click="handleDeparture" />

      <div class="controls-section mb-4">
        <v-btn variant="outlined" color="#26499D" class="waypoint-button" prepend-icon="mdi-plus"
          :disabled="showWaypoints && !canAddMoreWaypoints" @click="
            showWaypoints ? addWaypoint() : toggleWaypoint()
            ">
          {{ waypointButtonText }}
        </v-btn>

        <v-btn icon variant="outlined" color="primary" size="small" class="swap-button" @click="handleSwapLocations">
          <img :src="replaceImage" alt="入れ替え" class="replace-tivImage" />
        </v-btn>
      </div>

      <div v-if="showWaypoints && waypoints.length > 0" class="waypoints-container mb-4">
        <v-fade-transition group>
          <div v-for="(waypoint, index) in waypoints" :key="waypoint.id" class="waypoint-item mb-3">
            <v-row no-gutters align="center">
              <v-col cols="10">
                <v-text-field v-model="waypoint.location" hide-details class="location-input"
                  :placeholder="`経由地${index + 1}を選択`" variant="outlined" density="comfortable" readonly
                  @click="handleWaypoint" />
              </v-col>
              <v-col cols="2" class="pl-2">
                <v-btn color="#7D7D7D" density="compact" icon="mdi-close" @click="removeWaypoint(waypoint.id)" />
              </v-col>
            </v-row>
          </div>
        </v-fade-transition>
      </div>

      <v-text-field v-model="localData.destination" :rules="validationRules.destination" class="location-input mb-4"
        placeholder="到着地を選択" variant="outlined" density="comfortable" hide-details="auto" readonly
        @click="handleDestination" />

      <p class="leave">行き</p>
      <div class="date-section mb-4">
        <v-row no-gutters>
          <v-col cols="7">
            <v-text-field v-model="localData.outbound.date" :value="formatDate(localData.outbound.date)"
              :rules="dynamicDateRules" variant="outlined" density="comfortable" readonly class="date-display"
              placeholder="日付を選択" hide-details="auto" @click="openDatePicker('outbound')">
              <template #append-inner>
                <v-icon color="primary">mdi-calendar</v-icon>
              </template>
            </v-text-field>
          </v-col>

          <v-col cols="5" class="pl-1">
            <v-text-field v-model="localData.outbound.time" :rules="dynamicTimeRules" variant="outlined"
              density="comfortable" readonly class="time-display" placeholder="時間を選択" hide-details="auto"
              @click="openTimePicker('outbound')">
              <template #append-inner>
                <v-icon color="primary">mdi-clock</v-icon>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
      </div>

      <v-radio-group v-model="localData.outbound.direction" inline class="direction-radio mb-6" hide-details>
        <v-radio label="出発" value="departure" color="primary" class="radio-item" />
        <v-radio label="到着" value="arrival" color="primary" class="radio-item" />
      </v-radio-group>

      <p class="leave">帰り</p>
      <div class="date-section mb-4">
        <v-row no-gutters>
          <v-col cols="7">
            <v-text-field v-model="localData.return.date" :value="formatDate(localData.return.date)"
              :rules="dynamicDateRules" variant="outlined" density="comfortable" readonly class="date-display"
              placeholder="日付を選択" hide-details="auto" @click="openDatePicker('return')">
              <template #append-inner>
                <v-icon color="primary">mdi-calendar</v-icon>
              </template>
            </v-text-field>
          </v-col>

          <v-col cols="5" class="pl-1">
            <v-text-field v-model="localData.return.time" :rules="dynamicTimeRules" variant="outlined"
              density="comfortable" readonly class="time-display" placeholder="時間を選択" hide-details="auto"
              @click="openTimePicker('return')">
              <template #append-inner>
                <v-icon color="primary">mdi-clock</v-icon>
              </template>
            </v-text-field>
          </v-col>
        </v-row>
      </div>

      <v-radio-group v-model="localData.return.direction" inline class="direction-radio mb-6" hide-details>
        <v-radio label="出発" value="departure" color="primary" class="radio-item" />
        <v-radio label="到着" value="arrival" color="primary" class="radio-item" />
      </v-radio-group>
    </div>

  </v-form>
</template>

<style scoped>
.leave {
  color: #26499d;
  margin: 0 0 8px 0;
}

.location-input :deep(.v-field) {
  border-radius: 8px;
}

.location-input :deep(.v-field__input) {
  padding: 16px;
  min-height: 56px;
}

.location-input :deep(.v-label) {
  color: #9aa0a6;
  font-size: 16px;
}

.controls-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.swap-button {
  border: 0px #e0e4e7 !important;
}

.replace-tivImage {
  width: 40px;
  height: 40px;
}

.waypoint-button {
  border: 0px dashed #c1c7cd !important;
  color: #666 !important;
  font-size: 13px;
}

.waypoint-button :deep(.v-btn__prepend) {
  margin-inline-end: 8px;
}

.waypoint-button :deep(.v-btn__prepend .v-icon) {
  width: 20px;
  height: 20px;
  background: #26499d;
  color: white;
  border-radius: 50%;
  font-size: 14px;
}

.date-display :deep(.v-field) {
  border-radius: 8px;
}

.date-display :deep(.v-field__input) {
  padding: 16px;
  cursor: pointer;
}

.time-display :deep(.v-field) {
  border-radius: 8px;
}

.time-display :deep(.v-field__input) {
  padding: 16px;
  cursor: pointer;
}

.direction-radio {
  padding: 16px 0;
}

.direction-radio :deep(.v-selection-control-group) {
  gap: 32px;
}

.radio-item :deep(.v-label) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-left: 8px;
}

.radio-item :deep(.v-selection-control__wrapper) {
  width: 18px;
  height: 18px;
}

.radio-item :deep(.v-radio .v-selection-control__input) {
  width: 18px;
  height: 18px;
}

.search-button {
  width: 330px !important;
  border-radius: 24px !important;
  height: 50px !important;
  font-size: 16px;
  color: #fff!important;
}

.search-button :deep(.v-btn__prepend) {
  margin-inline-end: 8px;
}

:deep(.v-field--variant-outlined .v-field__outline) {
  color: #e0e4e7;
}

@media (min-width: 960px) {
  .pc-horizontal-layout {
    display: flex;
    align-items: flex-start;
    gap: 32px;
    margin-bottom: 32px;
  }

  .location-group {
    flex: 1;
    min-width: 0;
    padding-top: 20px;
  }

  .vertical-divider {
    width: 1px;
    height: 400px;
    background-color: #e0e4e7;
    margin-top: 20px;
  }

  .datetime-group {
    flex: 1;
    min-width: 0;
  }

  .trip-section {
    margin-bottom: 24px;
  }

  .section-headers {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
  }

  .section-title {
    flex: 1;
    font-size: 14px;
    color: #666;
    font-weight: 500;
    text-align: center;
  }

  .date-time-inputs {
    display: flex;
    gap: 16px;
  }

  .date-display-pc,
  .time-display-pc {
    flex: 1;
  }

  .date-display-pc :deep(.v-field),
  .time-display-pc :deep(.v-field) {
    border-radius: 8px;
  }

  .date-display-pc :deep(.v-field__input),
  .time-display-pc :deep(.v-field__input) {
    padding: 16px;
    cursor: pointer;
  }

  .direction-radio-pc {
    padding: 16px 0;
  }

  .direction-radio-pc :deep(.v-selection-control-group) {
    gap: 32px;
    flex-direction: row;
  }

  .direction-radio-pc :deep(.v-selection-control) {
    flex: none !important;
  }

  .direction-radio-pc .radio-item {
    margin-bottom: 8px;
  }
}

@media (max-width: 959px) {
  .search-button {
    width: 330px !important;
    margin: 0 auto;
  }
}
</style>
