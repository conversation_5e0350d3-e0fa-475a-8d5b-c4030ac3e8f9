<script lang="ts" setup>
import { ref, watch } from 'vue'

// プロパティ定義
interface Props {
  modelValue: boolean
  stationName?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  stationName: ''
})

// イベント定義
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'go-back': []
  'close-all': []
}>()

// ダイアログの表示状態
const isVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 観光地データ（モックデータ）
const touristData = ref({
  id: 1,
  name: props.stationName || '東京タワー',
  description:
    '東京タワーは、東京都港区にある通信と観光を目的としたタワーです。1958年に完成し、高さ333メートルです。東京のシンボル的存在として知られ、夜間はライトアップされ美しい景観を楽しめます。展望台からは東京の街並みを一望することができ、特に夜間の眺めは絶景として人気です。周辺には芝公園や增上寺などの観光スポットもあり、観光客に人気のエリアとなっています。',
  address: '東京都港区芝公園4丁目2-8',
  homepage: 'https://www.tokyotower.co.jp',
  facilities: 'エレベーター、展望台、レストラン、お土産ショップ、駐車場完備',
  images: [
    'https://picsum.photos/400/300?random=5',
    'https://picsum.photos/400/300?random=3',
    'https://picsum.photos/400/300?random=4'
  ]
})

// 現在の轮播索引
const currentSlide = ref<number>(0)

// 轮播定时器
let carouselTimer: NodeJS.Timeout | null = null

// 跳转到指定轮播
const goToSlide = (index: number): void => {
  currentSlide.value = index
  resetTimer()
}

// 下一张轮播
const nextSlide = (): void => {
  currentSlide.value =
    (currentSlide.value + 1) % touristData.value.images.length
}

// 启动自动轮播
const startAutoPlay = (): void => {
  carouselTimer = setInterval(nextSlide, 3000) // 3秒自动切换
}

// 停止自动轮播
const stopAutoPlay = (): void => {
  if (carouselTimer) {
    clearInterval(carouselTimer)
    carouselTimer = null
  }
}

// 重置定时器
const resetTimer = (): void => {
  stopAutoPlay()
  startAutoPlay()
}

// 戻るボタンのクリック処理（現在のダイアログのみ閉じる）
const handleGoBack = (): void => {
  isVisible.value = false
  emit('go-back')
}

// 閉じるボタンのクリック処理（すべてのダイアログを閉じる）
const closeDialog = () => {
  isVisible.value = false
  emit('close-all')
}

// 监听站点名称变化，更新数据
watch(
  () => props.stationName,
  (newName) => {
    if (newName) {
      touristData.value.name = newName
    }
  }
)
</script>
<template>
  <v-dialog v-model="isVisible" max-width="343" persistent>
    <v-card class="station-detail-dialog">
      <div class="station-dialog-content">
        <!-- ヘッダー -->
        <div class="station-header">
          <!-- 戻るボタン -->
          <img
            src="~/assets/image/Icon.png"
            alt="戻る"
            class="nextTripIcon"
            @click="handleGoBack"
          />
          <div class="station-title">停留所の詳細</div>
        </div>

        <div class="tourist-attraction-card">
          <div class="card-content">
            <h2 class="card-title">{{ touristData.name }}</h2>

            <div class="facilities-section">
              <h3 class="section-title">設備</h3>
              <p class="facilities-description">
                {{ touristData.facilities }}
              </p>
            </div>

            <div class="homepage-section">
              <h3 class="section-title">ホームページ</h3>
              <a :href="touristData.homepage" class="homepage-link">
                {{ touristData.homepage }}
              </a>
            </div>
          </div>
        </div>

        <div class="station-dialog-buttons">
          <v-btn class="close-station-btn" @click="closeDialog" block>
            閉じる
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>
<style scoped>
.station-detail-dialog {
  border-radius: 10px !important;
  background-color: #ffffff !important;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.station-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px 16px;
  width: 343px;
  max-height: 80vh;
  overflow-x: hidden;
}

.station-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
}

.back-button {
  position: absolute !important;
  left: 0 !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  color: #26499d !important;
  transition: all 0.3s ease !important;
}

.back-button:hover {
  background-color: rgba(38, 73, 157, 0.1) !important;
  color: #1a3570 !important;
}

.back-button:active {
  background-color: rgba(38, 73, 157, 0.2) !important;
}

@media (max-width: 480px) {
  .back-button {
    width: 44px !important;
    height: 44px !important;
    min-width: 44px !important;
  }

  .station-title {
    font-size: 18px;
  }
}

.station-title {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.2;
  color: #000000;
  flex: 1;
  text-align: center;
  margin-left: -20px;
}

/* 観光地カードのスタイル */
.tourist-attraction-card {
  background: white;
  border-radius: 4px;
  overflow-y: auto;
  width: 100%;
}

.card-content {
  padding: 20px;
  line-height: 1.6;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
  padding: 0;
}

.facilities-section,
.homepage-section {
  margin-bottom: 20px;
}

.facilities-section:last-child,
.homepage-section:last-child {
  margin-bottom: 0;
}

.section-title {
  color: #0066cc;
  font-weight: normal;
  background-color: #e7f2fa;
  width: 88px;
  height: 22px;
  text-align: center;
  font-size: 12px;
  margin-bottom: 4px;
  line-height: 22px;
}

.facilities-description {
  font-size: 12px;
  color: #333;
  line-height: 1.5;
  margin: 0;
}

.homepage-link {
  font-size: 13px;
  color: #0066cc;
  text-decoration: none;
}

.homepage-link:hover {
  text-decoration: underline;
}

.station-dialog-buttons {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  margin-top: 16px;
}

.close-station-btn {
  width: 100% !important;
  height: 48px !important;
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  color: #26499d !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.close-station-btn:hover {
  background-color: #d6e9f5 !important;
}

/* スクロールバーのスタイル */
.station-dialog-content::-webkit-scrollbar {
  width: 6px;
}

.station-dialog-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.station-dialog-content::-webkit-scrollbar-thumb {
  background: #9cbcd4;
  border-radius: 3px;
}

.station-dialog-content::-webkit-scrollbar-thumb:hover {
  background: #7ba3c4;
}

.nextTripIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(-90deg);
  transition: transform 0.3s ease;
}
</style>
