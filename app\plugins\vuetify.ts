import { ja } from 'vuetify/locale'
import { VDateInput } from 'vuetify/labs/VDateInput'
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.hook('vuetify:configuration', ({ vuetifyOptions }) => {
    vuetifyOptions.theme = {
      defaultTheme: 'BlueTheme',
      themes: {
        BlueTheme: {
          variables: {
            'border-color': '#2F55A6',
            'carousel-control-size': 10
          },
          colors: {
            primary: '#2F55A6'
          }
        }
      }
    }
    vuetifyOptions.icons = {
      defaultSet: 'mdi'
    }
    vuetifyOptions.locale = {
      locale: 'ja',
      messages: { ja }
    }
    vuetifyOptions.components = {
      VDateInput
    }

    vuetifyOptions.defaults = {
      VInput: {
        variant: 'outlined',
        density: 'comfortable'
      },
      VSelect: {
        variant: 'outlined'
      },
      VCheckbox: {
        variant: 'outlined'
      },
      VRadio: {
        variant: 'outlined'
      },
      VTextField: {
        variant: 'outlined'
      },
      VDateInput: {
        clearable: true,
      }
    }
  })
})
