<script lang="ts" setup>
import type { VerificationCodeResponse } from '~/types/email-change'

definePageMeta({
  layout: 'default',
  ssr: false
})

const route = useRoute()
const formRef = ref<any>(null)

// リアクティブデータ
const verificationCode = ref('')
const hasCodeError = ref(false)
const errorMessage = ref('')
const timeLeft = ref(300) // カウントダウンタイマー（秒）
const email = ref((route.query.email as string) || '')

let timer: NodeJS.Timeout | null = null // タイマーインスタンス

const validationRules = {
  code: [
    (v: string) => !!v || '確認コードを入力してください',
    (v: string) => /^\d+$/.test(v) || '数字のみ入力してください'
  ]
}

const isFormValid = computed(() => {
  return (
    verificationCode.value.length > 0 && /^\d+$/.test(verificationCode.value)
  )
})

// 秒数を分:秒形式にフォーマット
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// カウントダウンタイマーを開始
const startTimer = () => {
  if (timer) clearInterval(timer)

  timer = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--
    }
    if (timeLeft.value === 0 && timer) {
      clearInterval(timer)
    }
  }, 1000)
}

const onCodeInput = () => {
  hasCodeError.value = false
  errorMessage.value = ''

  // 数字以外の文字を削除
  verificationCode.value = verificationCode.value.replace(/\D/g, '')
}

const goBack = () => {
  if (timer) clearInterval(timer)
  navigateTo('/email-change')
}

// 確認コード再送信処理
const handleResend = async () => {
  if (timeLeft.value > 0) return

  try {
    const response = await $fetch<VerificationCodeResponse>(
      '/api/email-change/resend-code',
      {
        method: 'POST',
        body: {
          email: email.value
        }
      }
    )

    if (response.success) {
      timeLeft.value = 300 // タイマーをリセット
      startTimer()

      console.log('確認コード再送信成功')
    } else {
      throw new Error(response.message || '再送信に失敗しました')
    }
  } catch (error) {
    console.error('確認コード再送信エラー:', error)
    hasCodeError.value = true
    errorMessage.value =
      '再送信に失敗しました。しばらく後に再試行してください。'
  }
}

// 確認コード送信・検証処理
const handleSubmit = async () => {
  if (!isFormValid.value) {
    hasCodeError.value = true
    errorMessage.value = '正しい確認コードを入力してください'
    return
  }

  try {
    hasCodeError.value = false
    errorMessage.value = ''

    const response = await $fetch<VerificationCodeResponse>(
      '/api/email-change/verify-code',
      {
        method: 'POST',
        body: {
          email: email.value,
          verificationCode: verificationCode.value
        }
      }
    )

    if (response.success) {
      console.log('メールアドレス変更成功')

      if (timer) clearInterval(timer) // タイマーを停止

      navigateTo('/email-change/success')
    } else {
      throw new Error(response.message || '確認コードが正しくありません')
    }
  } catch (error) {
    navigateTo('/email-change/success')
    console.error('確認コード検証エラー:', error)
    hasCodeError.value = true
    errorMessage.value = '確認コードが正しくありません。再度お試しください。'
  }
}

// コンポーネントマウント時の初期化処理
onMounted(() => {
  console.log(email.value)
  if (!email.value) {
    navigateTo('/email-change')
    return
  }

  startTimer()
})

// コンポーネント破棄時のクリーンアップ
onUnmounted(() => {
  if (timer) clearInterval(timer)
})
</script>
<template>
  <div class="verification-page">
    <div class="page-header">
      <div class="header-content">
        <button class="back-button" @click="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M7.05 12L13.6 5.45L12.19 4.04L4.23 12L12.19 19.96L13.6 18.55L7.05 12Z"
              fill="#26499D"
            />
          </svg>
        </button>
        <h1 class="page-title">確認コード入力</h1>
        <div class="header-spacer"></div>
      </div>
    </div>

    <div class="main-content">
      <div class="form-container">
        <p class="description-text">
          登録されたメールアドレスに送信された確認コードを入力してください。
        </p>

        <v-form ref="formRef" class="verification-form">
          <div class="input-group">
            <div class="input-label">
              <span class="label-text">確認コード</span>
              <span class="timer-text">
                有効時間: {{ formatTime(timeLeft) }}
              </span>
            </div>
            <div class="input-field">
              <v-text-field
                v-model="verificationCode"
                type="text"
                :rules="validationRules.code"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
                class="custom-text-field"
                :class="{ 'error-field': hasCodeError }"
                placeholder="確認コードを入力"
                @input="onCodeInput"
              />
            </div>

            <div v-if="hasCodeError" class="error-message-text">
              {{ errorMessage }}
            </div>
          </div>

          <div class="button-section">
            <div class="resend-section">
              <button
                :disabled="timeLeft > 0"
                :class="['resend-link', { active: timeLeft <= 0 }]"
                @click="handleResend"
              >
                再送信
              </button>
            </div>

            <button
              type="button"
              :disabled="!isFormValid"
              :class="['submit-btn', { active: isFormValid }]"
              @click="handleSubmit"
            >
              確認変更
            </button>
          </div>
        </v-form>
      </div>
    </div>
  </div>
</template>
<style scoped>
.verification-page {
  width: 375px;
  height: 672px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  overflow: hidden;
}

.page-header {
  background-color: #ffffff;
  padding: 12px 16px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  width: 343px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
  text-align: center;
}

.header-spacer {
  width: 24px;
}

.main-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.form-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.description-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #000000;
  margin: 0;
  text-align: left;
  width: 100%;
}

.verification-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 343px;
}

.input-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
}

.label-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
}

.timer-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #ed785f;
}

.input-field {
  width: 100%;
}

.error-message-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #d00000;
  margin-top: 4px;
}

.button-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.resend-section {
  width: 343px;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
}

.resend-link {
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 400;
  color: #7d7d7d;
  cursor: not-allowed;
  text-decoration: none;
  padding: 0;
  transition: all 0.3s ease;
}

.resend-link.active {
  color: #26499d;
  cursor: pointer;
  text-decoration: underline;
}

.resend-link.active:hover {
  color: #1e3a7a;
}

.submit-btn {
  width: 343px;
  height: 48px;
  background-color: #dfdfdf;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  color: #7d7d7d;
  cursor: not-allowed;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background-color: #ed785f;
  color: #ffffff;
  cursor: pointer;
}

.submit-btn.active:hover {
  background-color: #d66b4a;
}

:deep(.custom-text-field .v-field) {
  border: 2px solid #dfdfdf;
  border-radius: 2px;
  background-color: #ffffff;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

:deep(.custom-text-field .v-field--focused) {
  border-color: #26499d;
}

:deep(.custom-text-field.error-field .v-field) {
  border-color: #d00000;
}

:deep(.custom-text-field .v-field__input) {
  padding: 13px 18px;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  min-height: auto;
  text-align: center;
  letter-spacing: 0.2em;
}

:deep(.custom-text-field .v-field__outline) {
  display: none;
}

:deep(.custom-text-field .v-messages) {
  display: none;
}

@media (max-width: 375px) {
  .verification-page {
    width: 100%;
    max-width: 375px;
  }

  .input-group,
  .resend-section,
  .submit-btn {
    width: 100%;
    max-width: 343px;
  }
}
</style>
