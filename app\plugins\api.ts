import { ofetch } from 'ofetch'
import { useGLStore } from '~/stores/globalLoading'
import { useCsrf } from '~/composables/api/useCsrf'
import { useUserStore } from '~/stores/login'

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()
  const baseURL = config.public?.apiBase || '/api'

  const globalLoading = useGLStore()

  const { ensureCsrf, refreshCsrf, applyCsrf } = useCsrf()
  const store = useUserStore()

  function redirectToLogin() {
    const current = typeof window !== 'undefined'
      ? `${window.location.pathname}${window.location.search}`
      : '/'
    const loginUrl = '/login?redirect=' + encodeURIComponent(current)
    navigateTo(loginUrl)
  }

  async function handle401() {
    try { store.resetInfo() } catch {}
    redirectToLogin()
  }

  async function tryCsrfRetry(request: RequestInfo, options: any, response: any): Promise<boolean> {
    try {
      const data: any = await response._data
      const code = data?.code
      const hasRetried = options._csrfRetry
      if ((code === 4031 || code === 4032 || !hasRetried) && !hasRetried) {
        await refreshCsrf()
        options._csrfRetry = true
        const headers = new Headers(options.headers as HeadersInit)
        const headerObj: Record<string, any> = {}
        applyCsrf(headerObj)
        Object.entries(headerObj).forEach(([k, v]) => headers.set(k, String(v)))
        options.headers = headers
        try { await api(request as string, options) } catch {}
        return true
      }
    } catch {}
    return false
  }

  const api = ofetch.create({
    baseURL,
    credentials: 'include',
    timeout: 5000, // 10 second timeout
    async onRequest({ request, options }) {
      if (!globalLoading.isOverlayDisabled) globalLoading.startLoading()

      const method = (options.method || 'GET').toUpperCase()
      const headers = new Headers(options.headers as HeadersInit)
      headers.set('X-Requested-With', 'XMLHttpRequest')

      if (!['GET', 'HEAD', 'OPTIONS'].includes(method)) {
        try {
          await ensureCsrf()
          const headerObj: Record<string, any> = {}
          applyCsrf(headerObj)
          Object.entries(headerObj).forEach(([k, v]) => headers.set(k, String(v)))
        } catch (error) {
          console.error('CSRF token setup failed:', error)
        }
      }

      options.headers = headers
    },
    onRequestError({ error }) {
      if (!globalLoading.isOverlayDisabled) globalLoading.endLoading()
      console.error('Request error:', error)
    },
    onResponse() {
      if (!globalLoading.isOverlayDisabled) globalLoading.endLoading()
    },
    onResponseError: async ({ request, options, response, error }) => {
      if (!globalLoading.isOverlayDisabled) globalLoading.endLoading()
      
      // Handle network errors (e.g., timeout, no backend)
      if (!response) {
        console.error('Network error or timeout:', error)
      }

      const status = response?.status

      if (status === 401) { await handle401(); return }
      if (status === 403) { if (await tryCsrfRetry(request, options as any, response)) return }
      if (status === 402) { navigateTo('/expired'); return }

      throw response
    }
  })

  return {
    provide: { api }
  }
})
