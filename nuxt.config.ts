// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from 'nuxt/config'

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  ssr: true,
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || '/api',
      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY || ''
    }
  },
  css: ['vuetify/styles', '~/assets/styles/index.scss'],
  app: {
    head: {
      link: [
        {
          rel: 'preload',
          href: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap',
          as: 'style'
        },
        {
          rel: 'stylesheet',
          href: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;700&display=swap'
        }
      ]
    }
  },
  build: {
    transpile: ['vuetify']
  },
  modules: [
    '@nuxt/eslint',
    '@nuxt/test-utils',
    '@nuxt/image',
    'vuetify-nuxt-module',
    '@pinia/nuxt'
  ],
  vuetify: {
    moduleOptions: {
      ssrClientHints: {
        viewportSize: true,
        prefersColorScheme: true,
        prefersReducedMotion: true
      }
    },
    vuetifyOptions: {
      ssr: {
        clientWidth: 1200,
        clientHeight: 800
      },
      display: {
        mobileBreakpoint: 'sm'
      }
    }
  },
  vite: {
    build: { sourcemap: false },
    server: {
      fs: {
        allow: [
          './'
        ]
      },
      proxy: {
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          secure: false
        }
      }
    }
  }
})
