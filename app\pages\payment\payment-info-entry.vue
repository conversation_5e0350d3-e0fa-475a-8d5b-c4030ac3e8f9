<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ConfirmPayment from './components/ConfirmPayment.vue'
import PaymentComplete from './components/PaymentComplete.vue'

// 受け取りパラメータ
const route = useRoute()
const reservationNo = ref(String(route.query.reservationNo || ''))
const paymentAmt = ref(Number(route.query.paymentAmt || 0))

// APIから取得するデータ
const coupons = ref<{ id: number; name: string; discount: number }[]>([])
const cards = ref<{ id: number; maskedNo: string; expiry: string }[]>([])
const couponDiscountsAmt = ref<number>(0)
// ラジオの選択肢
const cardOption = ref<'saved' | 'new'>('saved') // 新しいカード入力
const newCard = ref({
  name: '',
  number: '',
  expMonth: '',
  expYear: '',
  cvc: ''
})

const saveCard = ref(false)

// 選択状態
const selectedCoupon = ref<number>(0)
const selectedCard = ref<string>('card1')
const cardForm = ref()
const isFormValid = ref<boolean>(false)
const showConfirmDialog = ref(false)
const showPaymentCompleteDialog = ref(false)

// 確認用ダイアログのデータ
const paymentInfo = ref({
  reservationNo: reservationNo.value,
  amount: '0',
  coupon: '',
  cardHolder: '',
  cardNumber: '',
  cardExpiry: ''
})

// API呼び出し（ダミー実装）
const fetchCoupons = async () => {
  // 本来は API 呼び出し
  coupons.value = [
    { id: 0, name: '利用しない', discount: 0 },
    { id: 1, name: '新規会員登録 5%OFFクーポン', discount: 0.05 },
    { id: 2, name: 'GW特別キャンペーン 3%OFFクーポン', discount: 0.03 }
  ]
}
const fetchCards = async () => {
  // 本来は API 呼び出し
  cards.value = [
    { id: 1, maskedNo: 'XXXX - XXXX - XXXX - 1234', expiry: '12/30' },
    { id: 2, maskedNo: 'XXXX - XXXX - XXXX - 5678', expiry: '11/30' }
  ]
}

onMounted(async () => {
  await fetchCoupons()
  await fetchCards()
})

const handleCancel = async () => {
  await navigateTo({
    path: '/'
  })
}

const errorMessage = ref('')

// 登録済カードのサンプル
const savedCards = ref([
  {
    id: 'card1',
    name: '山田 太郎',
    maskedNumber: 'XXXX - XXXX - XXXX - 1234',
    expiry: '12/30'
  },
  {
    id: 'card2',
    name: '山田 花子',
    maskedNumber: 'XXXX - XXXX - XXXX - 5678',
    expiry: '11/30'
  }
])

const deleteCard = (id: string) => {
  console.log('削除するカード:', id)
  savedCards.value = savedCards.value.filter((c) => c.id !== id)
  if (selectedCard.value === id) {
    selectedCard.value = ''
  }
}
// 確認ボタン処理 → 弹出ダイアログ
const handleConfirm = async () => {
  if (cardForm.value) {
    const isValid = await cardForm.value.validate()
    if (!isValid) {
      errorMessage.value = '入力内容に誤りがあります。再確認してください。'
      return
    }
  }
  const coupon = coupons.value.find((c) => c.id === selectedCoupon.value)
  const card = savedCards.value.find((c) => c.id === selectedCard.value)
  paymentInfo.value = {
    ...paymentInfo.value,
    amount: formatNumber(couponDiscountsAmt.value) || '0',
    coupon: coupon?.name || '',
    cardHolder:
      cardOption.value === 'saved' ? card?.name || '' : newCard.value.name,
    cardNumber:
      cardOption.value === 'saved'
        ? card?.maskedNumber || ''
        : newCard.value.number,
    cardExpiry:
      cardOption.value === 'saved'
        ? card?.expiry || ''
        : `${newCard.value.expMonth} / ${newCard.value.expYear}`
  }
  showConfirmDialog.value = true
}

// ボタンのdisabled
const paymentButtonDisabled = computed(() => {
  if (cardOption.value === 'new') {
    selectedCard.value = '' // 清空
    return !isFormValid.value
  } else {
    if (cardForm.value) {
      cardForm.value.reset()
    }
    saveCard.value = false
    return selectedCard.value === ''
  }
})

const couponDiscounts = computed(() => {
  console.log('couponDiscounts', selectedCoupon.value)
  const discount = coupons.value.find(
    (c) => c.id === selectedCoupon.value
  )?.discount
  const discountAmt = Number(paymentAmt.value) * Number(discount)
  couponDiscountsAmt.value = paymentAmt.value - discountAmt
  return discountAmt
})

const formatNumber = (num: number) => {
  if (num == null || num === 0) return 0
  return Number(num).toLocaleString('en-US')
}

const handleConfirmPayment = () => {
  console.log('父组件的方法被调用: 決済処理を実行！')
  showConfirmDialog.value = false
  showPaymentCompleteDialog.value = true
}
</script>

<template>
  <div class="payment-info-entry">
    <!-- タイトル -->
    <h2 class="page-title">決済情報入力</h2>

    <!-- 決済情報 -->
    <v-card outlined class="mb-4">
      <v-card-text>
        <v-row>
          <v-col cols="12" class="font-weight-bold">決済情報</v-col>
        </v-row>
        <v-row>
          <v-col cols="4">予約番号：</v-col>
          <v-col cols="8">{{ reservationNo }}</v-col>
        </v-row>
        <v-row>
          <v-col cols="4">決済金額：</v-col>
          <v-col cols="8">￥{{ formatNumber(paymentAmt) }}</v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- クーポン情報 -->
    <v-card outlined class="mb-8">
      <v-card-text>
        <v-row>
          <v-col cols="12" class="font-weight-bold">クーポン情報</v-col>
        </v-row>
        <v-row>
          <v-col cols="12" class="pb-0 v-col-fs">
            現在利用可能なクーポンを表示しています。
          </v-col>
          <v-col cols="12" class="pt-0 v-col-fs">
            クーポンを利用する場合は選択してください。
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-text>
        <v-row>
          <v-radio-group v-model="selectedCoupon" column hide-details>
            <div v-for="coupon in coupons" :key="coupon.id">
              <v-radio :label="coupon.name" :value="coupon.id"></v-radio>
            </div>
          </v-radio-group>
        </v-row>

        <v-row>
          <v-col cols="12" class="text-alert font-weight-bold pb-0">
            クーポン割引額：￥{{ formatNumber(Number(couponDiscounts)) }}
          </v-col>
          <v-col cols="12" class="text-alert font-weight-bold pt-0">
            クーポン適用後決済金額：￥{{ formatNumber(couponDiscountsAmt) }}
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-divider class="divider-w"></v-divider>
    <!-- クレジットカード情報 -->
    <v-card outlined class="mt-8">
      <v-card-text>
        <v-row>
          <v-col cols="12" class="font-weight-bold">
            クレジットカード情報入力
          </v-col>
        </v-row>

        <!-- 支払い方法 -->
        <v-row class="mb-2">
          <v-col cols="12" class="text-caption">
            お支払いに使用するカードの種類を選択してください
          </v-col>
        </v-row>

        <!-- エラーメッセージ -->
        <v-row class="align-center mb-2" v-if="errorMessage">
          <v-col cols="1">
            <v-icon color="error">mdi-alert</v-icon>
          </v-col>
          <v-col>
            <span class="text-error">{{ errorMessage }}</span>
          </v-col>
        </v-row>

        <v-radio-group v-model="cardOption">
          <!-- 登録済みカード -->
          <v-radio label="登録済カードを利用する" value="saved"></v-radio>
          <!-- 子级卡片选择，仅在父级为 saved 时显示 -->
          <v-card flat class="ml-6 pa-2" v-if="cardOption === 'saved'">
            <v-radio-group v-model="selectedCard">
              <div v-for="card in savedCards" :key="card.id" class="mb-2">
                <v-radio
                  v-model="selectedCard"
                  :label="`${card.maskedNumber}　${card.expiry}`"
                  :value="card.id"
                />
                <!-- 子卡片被选中时显示删除按钮 -->
                <v-btn
                  v-if="selectedCard === card.id"
                  size="small"
                  class="ml-8 custom-outline-btn"
                  rounded="xs"
                  variant="outlined"
                  @click="deleteCard(card.id)"
                >
                  クレジットカード情報を削除する
                </v-btn>
              </div>
            </v-radio-group>
          </v-card>

          <!-- 別のカード -->
          <v-radio label="別のカードを利用する" value="new"></v-radio>
          <v-card flat class="ml-0 pa-2" v-if="cardOption === 'new'">
            <v-form ref="cardForm" v-model="isFormValid" lazy-validation>
              <!-- 名義 -->
              <label class="field-label mt-3" for="cardName">
                クレジットカード名義
              </label>
              <v-text-field
                id="cardName"
                v-model="newCard.name"
                density="compact"
                hide-details="auto"
                placeholder="TARO YAMADA"
                class="mb-3"
                :rules="[
                  (v) => !!v || 'クレジットカード名義を入力してください',
                  (v) =>
                    /^[a-zA-Z\s]+$/.test(v) ||
                    'クレジットカード名義は英字のみで入力してください'
                ]"
              />
              <!-- 番号 -->
              <label class="field-label mt-3" for="cardNumber">
                クレジットカード番号
              </label>
              <v-text-field
                id="cardNumber"
                v-model="newCard.number"
                density="compact"
                hide-details="auto"
                placeholder="1234-5678-9012-3456"
                class="mb-1"
                :rules="[
                  (v) => !!v || 'カード番号を入力してください',
                  (v) =>
                    /^[0-9]{4}-[0-9]{4}-[0-9]{4}-[0-9]{4}$/.test(v) ||
                    'カード番号はXXXX-XXXX-XXXX-XXXXの形式で入力してください'
                ]"
              />
              <!-- 有効期限 -->
              <v-row class="mt-3 mb-3">
                <v-col cols="6">
                  <label class="field-label" for="cardExpMonth">
                    有効期限(月)
                  </label>
                  <v-text-field
                    id="cardExpMonth"
                    v-model="newCard.expMonth"
                    density="compact"
                    hide-details="auto"
                    placeholder="MM"
                    :rules="[
                      (v) => !!v || '有効期限（月）を入力してください',
                      (v) =>
                        /^[0-9]{2}$/.test(v) ||
                        '月は2桁の数字で入力してください'
                    ]"
                  />
                </v-col>
                <v-col cols="6">
                  <label class="field-label" for="cardExpYear">
                    有効期限(年)
                  </label>
                  <v-text-field
                    id="cardExpYear"
                    v-model="newCard.expYear"
                    density="compact"
                    hide-details="auto"
                    placeholder="YY"
                    :rules="[
                      (v) => !!v || '有効期限（年）を入力してください',
                      (v) =>
                        /^[0-9]{2}$/.test(v) ||
                        '年は2桁の数字で入力してください'
                    ]"
                  />
                </v-col>
              </v-row>

              <!-- セキュリティコード -->
              <label class="field-label mt-3" for="cardCvc">
                セキュリティコード
              </label>
              <v-text-field
                id="cardCvc"
                v-model="newCard.cvc"
                density="compact"
                hide-details="auto"
                placeholder="***"
                class="mb-0 pb-0"
                :rules="[
                  (v) => !!v || 'セキュリティコードを入力してください',
                  (v) =>
                    /^[0-9]{3,4}$/.test(v) ||
                    'セキュリティコードは3～4桁の数字で入力してください'
                ]"
              />
            </v-form>
          </v-card>
        </v-radio-group>
        <!-- 保存チェック -->
        <v-checkbox
          v-model="saveCard"
          label="クレジットカード情報を保存する"
          density="compact"
          v-if="cardOption === 'new'"
        />
      </v-card-text>
    </v-card>
    <!-- ボタン -->
    <div class="payment-btn d-flex flex-column mb-4 px-3">
      <v-btn
        block
        color="#ed785f"
        :disabled="paymentButtonDisabled"
        class="mb-2"
        height="40"
        @click="handleConfirm"
      >
        決済情報を確認する
      </v-btn>
      <v-btn
        block
        color="#e7f2fa"
        class="cancel-btn"
        height="40"
        @click="handleCancel"
      >
        キャンセル
      </v-btn>
    </div>
    <!-- 确认用ダイアログ -->
    <ConfirmPayment
      v-model="showConfirmDialog"
      :paymentInfo="paymentInfo"
      @confirm="handleConfirmPayment"
    />
    <PaymentComplete
      v-model="showPaymentCompleteDialog"
      :reservationNo="reservationNo"
    />
  </div>
</template>

<style scoped>
.page-title {
  text-align: center;
  color: #26499d;
  font-size: 18px;
  font-weight: 400;
}
.font-weight-bold {
  font-size: 17px;
  font-weight: 500 !important;
}
.text-alert {
  color: #d00000;
  font-size: 14px;
}
.v-card {
  box-shadow: unset;
}
.v-col {
  padding-top: 7px;
  padding-bottom: 7px;
}
.v-col-4 {
  max-width: 26% !important;
}
.v-col-fs,
:deep(.v-selection-control .v-label) {
  font-size: 13px !important;
}
:deep(.v-selection-control__input > .v-icon) {
  font-size: 18px !important;
  color: #26499d;
}
.v-selection-control--density-default {
  --v-selection-control-size: 30px;
}
.divider-w {
  width: 90%;
  margin: 0 auto;
}
.custom-outline-btn {
  color: black !important;
  border-color: #26499d;
}
.field-label {
  font-size: 13px !important;
  color: #26499d !important;
}
.field-label::after {
  content: ' *';
  color: red;
}
.cancel-btn {
  border: 1px solid #26499d;
  color: #26499d !important;
}
.payment-btn .v-btn {
  box-shadow: unset !important;
}
</style>
