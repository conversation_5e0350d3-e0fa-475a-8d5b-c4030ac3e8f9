<script lang="ts" setup>
import { ref, onMounted, type PropType } from 'vue'
import { useRouter } from 'vue-router'
import EventSpotMobile from './EventSpotMobile.vue'

/* *
 * プロパティ定義
 * */
const props = defineProps({
  sectionTitle: {
    type: String,
    default: '期間限定・イベント'
  },
  apiEndpoint: {
    type: String,
    default: '/event'
  },
  maxItems: {
    type: Number,
    default: 6
  },
  category: {
    type: String,
    required: true
  },
  eventsData: {
    type: Array as PropType<
      Array<{
        eventId: number
        name: string
        prefectureId: number
        address: string
        startDate: string
        endDate: string
        image: string
        description: string
      }>
    >,
    default: () => []
  }
})

/* *
 * ルーターの初期化
 * */
const router = useRouter()

/* *
 * 状態管理
 * */
const loading = ref(false)
const error = ref<string | null>(null)

/* *
 * イベントデータ取得関数
 * */
const fetchEvents = async () => {
  loading.value = true
  error.value = null

  try {
    // 現在はpropsから受け取ったデータを使用
  } catch (err) {
    console.error('Failed to fetch events:', err)
    error.value = err instanceof Error ? err.message : 'Failed to fetch events'
  } finally {
    loading.value = false
  }
}

/* *
 * 詳細ページへのナビゲーション
 * */
const navigateToDetail = (item: { eventId: number }) => {
  if (props.category === 'tourist') {
    router.push(`/tourist-facilities/edit/${item.eventId}`)
  } else if (props.category === 'event') {
    router.push(`/special-events/edit/${item.eventId}`)
  }
}

/* *
 * イベント一覧ページへのナビゲーション
 * */
const navigateToEventsList = () => {
  if (props.category === 'tourist') {
    router.push('/tourist-facilities')
  } else if (props.category === 'event') {
    router.push('/special-events')
  }
}

/* *
 * 画像エラー処理
 * */
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = 'https://picsum.photos/400/200?grayscale&blur=2'
  target.alt = '画像が読み込めませんでした'
}

/* *
 * 日付範囲のフォーマット
 * */
const formatDateRange = (startDate: string, endDate: string) => {
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return `${date.getMonth() + 1}/${date.getDate()}`
  }

  if (startDate === endDate) {
    return formatDate(startDate)
  }

  return `${formatDate(startDate)} - ${formatDate(endDate)}`
}

/* *
 * コンポーネントマウント時の処理
 * */
onMounted(() => {
  fetchEvents()
})
</script>

<template>
  <div class="event-spot">
    <h2 class="section-title">{{ sectionTitle }}</h2>
    <div class="title-underline"></div>

    <main class="attractions-container">
      <!-- エラー表示 -->
      <div v-if="error" class="error-container">
        <p>データの読み込みに失敗しました</p>
      </div>

      <!-- イベントデータ表示 -->
      <template v-else-if="props.eventsData.length > 0">
        <!-- デスクトップ表示 -->
        <div class="attractions-grid desktop-view">
          <div
            v-for="event in props.eventsData"
            :key="event.eventId"
            class="attraction-card"
            @click="navigateToDetail(event)"
          >
            <div class="card-image">
              <img
                :src="event.image"
                :alt="event.name"
                class="attraction-image"
                @error="handleImageError"
              />
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ event.name }}</h3>
              <p class="card-description">{{ event.description }}</p>
            </div>
          </div>
        </div>

        <!-- モバイル表示 -->
        <div class="mobile-view">
          <div class="carousel-container">
            <EventSpotMobile
              :items="props.eventsData"
              @item-click="navigateToDetail"
            />
          </div>
        </div>

        <!-- もっと見るボタン -->
        <div class="more-btn">
          <v-btn @click="navigateToEventsList" variant="outlined">
            もっと見る
          </v-btn>
        </div>
      </template>

      <!-- データなし表示 -->
      <div v-else class="empty-container">
        <p>現在開催中のイベントはありません</p>
      </div>
    </main>
  </div>
</template>

<style scoped lang="scss">
$primary-color: #26499d;

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  p {
    color: #e74c3c;
    font-size: 16px;
    margin-bottom: 16px;
  }

  .retry-btn {
    padding: 8px 16px;
    border: 1px solid $primary-color;
    background: #fff;
    color: $primary-color;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: $primary-color;
      color: #fff;
    }
  }
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;

  p {
    color: #999;
    font-size: 16px;
  }
}

.more-btn {
  text-align: center;
}

.section-title {
  text-align: center;
  color: $primary-color;
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  padding-top: 20px;
}

.title-underline {
  width: 40px;
  height: 2px;
  background: $primary-color;
  margin: 0 auto 20px;
}

.attractions-container {
  margin: 0 auto;
  padding: 0 20px 60px;
}

.desktop-view {
  width: 61%;
  margin: 0 auto;
  padding: 20px;
  display: block;
}

.mobile-view {
  display: none;
}

.attractions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .desktop-view {
    display: none;
  }

  .mobile-view {
    display: block;
  }

  .attractions-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .attractions-container {
    padding: 0 15px 40px;
  }

  .card-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .attractions-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .card-image {
    height: 150px;
  }

  .card-title {
    font-size: 15px;
  }

  .card-description {
    font-size: 13px;
  }
}

.attraction-card {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  }
}

.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.attraction-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.attraction-card:hover .attraction-image {
  transform: scale(1.05);
}

.card-content {
  padding: 20px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.card-description {
  font-size: 12px;
  color: #666;
  line-height: 1;
  margin: 0 0 0 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-dates {
  .date-range {
    font-size: 12px;
    color: $primary-color;
    background: rgba($primary-color, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
  }
}

.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 30px;
}
</style>
