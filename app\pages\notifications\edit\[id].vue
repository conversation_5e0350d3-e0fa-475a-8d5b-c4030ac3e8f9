<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { Touris, BusSchedule } from '~/types/notifications'

const router = useRouter()
const route = useRoute()
const touristId = ref<number | null>(null)

/* お知らせアイテムリスト */
const items = ref<Touris[]>([
  {
    spot_id: 1,
    name: '2024.12.15',
    description: 'メンテナンス',
    notifcation: 'システムメンテナンスのお知らせ'
  }
])

/* 通知テキスト */
const noticeText = ref(
  '●●バス運行●●線が東名高速管内の事故の影響で30分～1時間の遅れが発生しております。ご利用のお客様には大変ご迷惑をおかけしますが、乗り継ぎ便の時間を確認いただき、変更が必要な場合はお客様での変更対応をお願いいたします。'
)

/* バススケジュール情報 */
const busSchedule = ref<BusSchedule>({
  date: '2025年3月15日',
  name: '関西バス',
  route: '大阪～名古屋行き',
  departureTime: '13：15',
  destination: 'OCTA発着',
  arrivalTime: '16：30',
  platform: '名古屋駅前',
  status: '予定',
  delayInfo: '30分から1時間の遅れ'
})

/* 通知リストへの遷移 */
function BackList() {
  router.push({
    path: `/notifications`
  })
}

/* ページマウント時の初期化 */
onMounted(() => {
  const id = route.params.id
  touristId.value = id ? Number(id) : null
})
</script>

<template>
  <div class="regionpadding">
    <BaseHeader title="お知らせ" :showBack="true" :showRightIcon="false" />
  </div>
  <v-container class="" fluid>
    <div class="cardGrid">
      <div v-for="item in items" :key="item.spot_id" class="card" elevation="1">
        <div class="cardContent">
          <p>{{ item.name }}</p>
          <p class="maintenance">{{ item.description }}</p>
        </div>
        <div class="cardContentWrapper">
          <p class="clampedText">{{ item.notifcation }}</p>
        </div>
      </div>
    </div>

    <div>
      <p class="noticeContent">{{ noticeText }}</p>
    </div>

    <div>
      <p class="noticeContent">該当バス</p>
    </div>

    <div class="timeDetails">
      <div class="timeRow">
        <p class="time">{{ busSchedule.date }}</p>
        <p class="location">{{ busSchedule.route }}</p>
      </div>
      <div class="timeRos">
        <p class="time">{{ busSchedule.name }}</p>
        <p class="location">{{ busSchedule.route }}</p>
      </div>
      <div class="timeRos">
        <p class="time">{{ busSchedule.departureTime }}</p>
        <p class="location">{{ busSchedule.destination }}</p>
      </div>
      <div class="timeRos">
        <p class="time">{{ busSchedule.arrivalTime }}</p>
        <p class="location">{{ busSchedule.platform }}</p>
        <p class="location">{{ busSchedule.status }}</p>
      </div>
      <div class="delayNotice">{{ busSchedule.delayInfo }}</div>
    </div>

    <div class="buttonContainer">
      <button class="returnButton" @click="BackList">一覧に戻る</button>
    </div>
  </v-container>
</template>

<style lang="scss" scoped>
.card {
  overflow: hidden;
  height: 100%;
  border-bottom: 1px solid #dfdfdf;
  padding: 12px 0;

  .maintenance {
    border: 1px solid #ed785f;
    padding: 0 8px;
    color: #ed785f;
  }

  h3 {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 4px;
  }

  p {
    font-size: 12px;
    color: #555;
    margin-right: 10px;
  }
}

.cardContent {
  display: flex;
}

.cardContentWrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 24px 0 32px 0;
}

.clampedText {
  color: #000 !important;
  font-size: 16px !important;
}

.noticeContent {
  margin-top: 32px;
}

.timeDetails {
  background-color: #f2f8fc;
  padding: 20px;
  font-size: 14px;
  border-radius: 8px;
  margin-top: 22px;

  .timeRos {
    display: flex;

    .location {
      margin-left: 10px;
    }
  }
}

.delayNotice {
  color: #d00000;
  font-size: 13px;
  font-weight: 600;
  margin-top: 30px;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  margin-top: 32px;

  .returnButton {
    width: 100%;
    max-width: 200px;
    padding: 10px 20px;
    border: 1px solid #9cbcd4;
    border-radius: 20px;
    color: #26499d;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: #26499d;
      color: #fff;
    }
  }
}
</style>
