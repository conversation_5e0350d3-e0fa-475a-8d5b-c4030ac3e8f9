<script lang="ts" setup>
import { useRegistrationFormStore } from '@/stores/member-registration'
import type { ConfirmFormData } from '~/types/member-Registration'
import { useRouter } from 'vue-router'

const router = useRouter()
const showPassword = ref(false)
const formStore = useRegistrationFormStore()

/* 送信されたフォームデータの取得 */
const submittedData = formStore.submittedData as unknown as
  | ConfirmFormData
  | undefined

/* 性別の表示形式を変換 */
const formatGender = (gender?: string): string => {
  if (gender === 'male') return '男性'
  if (gender === 'female') return '女性'
  if (gender === 'answer') return '回答しない'
  return ''
}

/* 性別の表示用computed */
const genderLabel = computed(() => formatGender(submittedData?.gender))

/* メール購読の表示形式を変換 */
const formatEmailSubscription = (isSubscribe?: boolean | null): string => {
  return isSubscribe ? '希望する' : '希望しない'
}

/* 前ページに戻る処理 */
const goBack = (): void => {
  if (window.history.length > 1) {
    window.history.back()
  }
}

/* 最終的な登録処理 */
const handleSubmit = (): void => {
  router.push('/member-registration/authentication-code')
}
</script>

<template>
  <div class="registrationContainer">
    <div class="regionpadding">
      <BaseHeader
        title="新規会員登録確認"
        :showBack="true"
        :showRightIcon="false"
      />
    </div>

    <div class="formContentArea">
      <div class="instructionText">
        入力内容をご確認の上、よろしければ「登録する」ボタンを押してください。誤記・お忘れの内容がございましたら、「戻る」ボタンでご修正お願いします。
      </div>

      <div class="formFieldsList">
        <div class="fieldGroup">
          <span class="fieldLabel">お名前</span>
          <div class="fieldValue">
            {{ submittedData?.lastName }} {{ submittedData?.firstName }}
          </div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">フリガナ</span>
          <div class="fieldValue">
            {{ submittedData?.lastNameKana }} {{ submittedData?.firstNameKana }}
          </div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">生年月日</span>
          <div class="fieldValue">
            {{ submittedData?.birthYear }}年{{ submittedData?.birthMonth }}月{{
              submittedData?.birthDay
            }}日
          </div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">性別</span>
          <div class="fieldValue">
            {{ genderLabel }}
          </div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">居住地</span>
          <div class="fieldValue">{{ submittedData?.residence?.label }}</div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">市区町村・番地以降</span>
          <div class="fieldValue">{{ submittedData?.cityAddressDetail }}</div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">職業</span>
          <div class="fieldValue">{{ submittedData?.occupation?.label }}</div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">電話番号</span>
          <div class="fieldValue">{{ submittedData?.phoneNumber }}</div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">メールアドレス</span>
          <div class="fieldValue">{{ submittedData?.emailAddress }}</div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">パスワード</span>
          <div
            class="fieldValue"
            style="display: flex; align-items: center; gap: 8px"
          >
            <span>
              {{
                showPassword
                  ? submittedData?.password
                  : '*'.repeat(submittedData?.password?.length || 0)
              }}
            </span>
            <v-icon
              @click="showPassword = !showPassword"
              :icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
              class="passwordToggle"
              style="cursor: pointer"
            />
          </div>
        </div>

        <div class="fieldGroup">
          <span class="fieldLabel">メールマガジン配信</span>
          <div class="fieldValue">
            {{ formatEmailSubscription(submittedData?.emailSubscription) }}
          </div>
        </div>
      </div>

      <div class="buttonArea">
        <button class="primaryButton" @click="handleSubmit">登録する</button>
        <button class="secondaryButton" @click="goBack">戻る</button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.passwordToggle {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  color: #26499d;
}

.registrationContainer {
  min-height: 100vh;
  background-color: #f8f9fa;

  .regionHeaderTop {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background-color: #fff;
    border-bottom: 1px solid #e5e7eb;
    position: relative;

    .regionSelectorBackBtn {
      background: none;
      border: none;
      padding: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #f3f4f6;
        border-radius: 4px;
      }
    }
  }

  .formContentArea {
    .instructionText {
      background-color: #fff;
      padding: 16px;
      font-size: 14px;
      line-height: 1.6;
      color: #374151;
      padding-bottom: 40px;
    }

    .formFieldsList {
      background-color: #fff;
      overflow: hidden;
      padding: 0 10px 40px 10px;

      .fieldGroup {
        display: flex;
        flex-direction: column;
        padding: 16px 20px;
        border-bottom: 1px solid #f3f4f6 !important;

        &:last-child {
          border-bottom: none;
        }

        .fieldLabel {
          min-width: 120px;
          font-size: 14px;
          font-weight: 500;
          color: #26499d;
          padding-right: 16px;
          flex-shrink: 0;
        }

        .fieldValue {
          flex: 1;
          font-size: 14px;
          color: #1f2937;
          word-break: break-word;
        }
      }
    }

    .buttonArea {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 12px;
      background-color: #fff;
      margin-bottom: 24px;

      .primaryButton {
        width: 90%;
        height: 48px;
        background: #ed785f;
        color: #fff;
        border: none;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .secondaryButton {
        width: 90%;
        height: 48px;
        background-color: #e7f2fa;
        color: #26499d;
        border: 1px solid #9cbcd4;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
      }
    }
  }
}
</style>
