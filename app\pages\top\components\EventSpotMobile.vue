<script lang="ts" setup>
import { ref, watchEffect } from 'vue'

/* *
 * プロパティ定義
 * */
const props = defineProps({
  items: {
    type: Array as () => Array<{
      eventId?: string | number
      id?: string | number
      name: string
      description: string
      image: string
    }>,
    required: true
  }
})

/* *
 * イベント定義
 * */
const emit = defineEmits(['item-click'])

/* *
 * DOM参照と状態管理
 * */
const scrollRef = ref<HTMLElement | null>(null)
const isDragging = ref(false)
const startX = ref(0)
const startScrollLeft = ref(0)
const hasScroll = ref(false)

/* *
 * スクロール可能かどうかを監視
 * */
watchEffect(() => {
  if (scrollRef.value) {
    const { scrollWidth, clientWidth } = scrollRef.value
    hasScroll.value = scrollWidth > clientWidth + 10
  }
})

/* *
 * アイテムクリック時の処理
 * */
const handleItemClick = (item: any) => {
  if (item.event_id) {
    emit('item-click', item.event_id)
  } else if (item.id) {
    emit('item-click', item.id)
  } else {
    emit('item-click', item)
  }
}

/* *
 * ドラッグ開始処理
 * */
const startDrag = (e: MouseEvent) => {
  if (!scrollRef.value) return

  isDragging.value = true
  startX.value = e.pageX - scrollRef.value.offsetLeft
  startScrollLeft.value = scrollRef.value.scrollLeft
  scrollRef.value.style.cursor = 'grabbing'
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', endDrag)
}

/* *
 * ドラッグ中の処理
 * */
const handleDrag = (e: MouseEvent) => {
  if (!isDragging.value || !scrollRef.value) return

  e.preventDefault()
  const currentX = e.pageX - scrollRef.value.offsetLeft
  const scrollDistance = (currentX - startX.value) * 1.2
  scrollRef.value.scrollLeft = startScrollLeft.value - scrollDistance
}

/* *
 * ドラッグ終了処理
 * */
const endDrag = () => {
  setTimeout(() => {
    isDragging.value = false
  }, 100)

  if (scrollRef.value) {
    scrollRef.value.style.cursor = 'grab'
  }
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', endDrag)
}

/* *
 * ホイールイベント処理
 * */
const handleWheel = (e: WheelEvent) => {
  e.preventDefault()
  if (!scrollRef.value) return

  scrollRef.value.scrollBy({
    left: e.deltaY > 0 ? 150 : -150,
    behavior: 'smooth'
  })
}

/* *
 * 画像読み込みエラー処理
 * */
const handleImgError = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.src = 'https://picsum.photos/400/200?grayscale&blur=2'
  target.alt = '画像が読み込めませんでした'
}
</script>
<template>
  <div class="horizontal-scroll">
    <div
      class="scroll-container"
      ref="scrollRef"
      @mousedown="startDrag"
      @wheel="handleWheel"
    >
      <div class="scroll-content">
        <div
          class="scroll-item"
          v-for="(item, index) in props.items"
          :key="item.eventId || index"
          @click="handleItemClick(item)"
        >
          <div class="item-img-wrap">
            <img
              :src="item.image"
              :alt="`${item.name}-${index}`"
              class="item-img"
              @error="handleImgError($event)"
            />
          </div>

          <div class="item-text">
            <h3 class="item-title">{{ item.name }}</h3>
            <p style="border: 1px solid #c1d5e4; height: 1px; width: 24px"></p>
            <p class="item-desc">{{ item.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.horizontal-scroll {
  width: 100%;
  box-sizing: border-box;
}

.scroll-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  cursor: grab;

  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
}

.scroll-content {
  display: flex;
  gap: 20px;
  padding: 10px 0;
  margin-left: 10px;
  margin-right: 10px;
}

.scroll-item {
  width: 280px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: translateY(0);
  }
}

.item-img-wrap {
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.item-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.scroll-item:hover .item-img {
  transform: scale(1.05);
}

.item-text {
  padding: 0 16px 16px;
}

.item-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.item-desc {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
