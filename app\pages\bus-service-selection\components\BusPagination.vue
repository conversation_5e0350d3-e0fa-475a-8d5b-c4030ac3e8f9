<script lang="ts" setup> 
import { computed } from 'vue'

/* プロパティ定義 */
const props = defineProps<{
  currentPage: number
  totalPages: number
  showPageNumbers?: boolean
  maxVisiblePages?: number
}>()

/* イベント発行定義 */
const emit = defineEmits<(e: 'page-change', page: number) => void>()

/* 前ページへの移動処理 */
const handlePrevious = () => {
  if (props.currentPage > 1) {
    emit('page-change', props.currentPage - 1)
  }
}

/* 次ページへの移動処理 */
const handleNext = () => {
  if (props.currentPage < props.totalPages) {
    emit('page-change', props.currentPage + 1)
  }
}

/* ページ番号クリック処理 */
const handlePageClick = (page: number | string) => {
  if (typeof page === 'number') {
    emit('page-change', page)
  }
}

/* 表示するページ番号の計算 */
const pageNumbers = computed(() => {
  const pages = []
  const { totalPages, currentPage, maxVisiblePages = 5 } = props

  if (totalPages <= maxVisiblePages) {
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i)
    }
    return pages
  }

  const half = Math.floor(maxVisiblePages / 2)

  if (currentPage <= half + 1) {
    for (let i = 1; i <= half + 2; i++) {
      pages.push(i)
    }
    pages.push('...')
    pages.push(totalPages)
  } else if (currentPage >= totalPages - half) {
    pages.push(1)
    pages.push('...')
    for (let i = totalPages - half - 1; i <= totalPages; i++) {
      pages.push(i)
    }
  } else {
    pages.push(1)
    pages.push('...')
    for (let i = currentPage - half; i <= currentPage + half; i++) {
      pages.push(i)
    }
    pages.push('...')
    pages.push(totalPages)
  }

  return pages
})

/* ページ番号表示フラグ */
const showPageNumbers = computed(() => {
  return props.showPageNumbers !== false
})
</script>
<template>
  <div class="paginationContainer">
    <div class="paginationControls">
      <button
        class="paginationBtn prev"
        @click="handlePrevious"
        :disabled="currentPage === 1"
      >
        前の候補
      </button>

      <button
        class="paginationBtn next"
        @click="handleNext"
        :disabled="currentPage === totalPages"
      >
        次の候補
      </button>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.paginationContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
}

.paginationControls {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  justify-content: space-between;
}

.paginationInfo {
  color: #000;
  font-size: 14px;
  font-weight: 500;
}

.paginationBtn {
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.prev {
  display: inline-block;
  background-color: #224591;
  color: white;
  position: relative;
  margin: 0;
  height: 39px;
  line-height: 39px;
  width: 80px;
  text-align: center;
}

.prev::after {
  content: '';
  position: absolute;
  top: 0;
  left: -19px;
  width: 0;
  height: 37px;
  border-right: 20px solid #224591;
  border-top: 16px solid transparent;
  border-bottom: 16px solid transparent;
}

.next {
  display: inline-block;
  background-color: #224591;
  color: white;
  position: relative;
  margin: 0;
  height: 39px;
  line-height: 39px;
  width: 80px;
  text-align: center;
}

.next::after {
  content: '';
  position: absolute;
  top: 0;
  right: -19px;
  width: 0;
  height: 37px;
  border-left: 20px solid #224591;
  border-top: 16px solid transparent;
  border-bottom: 16px solid transparent;
}

.pageIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pageNumber {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
}

.pageNumber.active {
  background-color: #224591;
  color: white;
  font-weight: bold;
}

.pageNumber:not(.active):not(.ellipsis):hover {
  background-color: #f0f0f0;
}

.pageNumber.ellipsis {
  cursor: default;
  color: #999;
}
</style>
