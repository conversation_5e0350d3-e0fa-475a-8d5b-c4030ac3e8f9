<script lang="ts" setup>
interface Props {
  modelValue: boolean
  reservationNumber?: string
  paymentDeadline?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'payment'): void
  (e: 'goToDetail'): void
}

const props = withDefaults(defineProps<Props>(), {
  reservationNumber: 'XXXXXXXXX',
  paymentDeadline: '2024年8月5日（月）まで'
})

const emit = defineEmits<Emits>()

// ダイアログの表示状態を管理
const isVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 決済ボタンのクリック処理
const handlePayment = () => {
  emit('payment')
  isVisible.value = false
}

// 予約詳細ボタンのクリック処理
const handleGoToDetail = () => {
  emit('goToDetail')
  isVisible.value = false
}
</script>
<template>
  <v-dialog v-model="isVisible" max-width="343" persistent>
    <v-card class="change-completion-dialog">
      <div class="dialog-content">
        <!-- タイトル -->
        <h2 class="dialog-title">便変更完了</h2>

        <!-- 情報セクション -->
        <div class="info-section">
          <div class="reservation-number">
            予約番号：　{{ reservationNumber }}
          </div>
          <div class="payment-deadline">決済期限：　{{ paymentDeadline }}</div>
          <div class="description">
            便変更が完了しました。
            <br />
            決済期限までに決済を行ってください。
            <br />
            予約情報はマイページの予約履歴からご確認いただけます。
          </div>
        </div>

        <!-- ボタンセクション -->
        <div class="button-section">
          <v-btn
            class="payment-btn"
            variant="elevated"
            size="large"
            block
            @click="handlePayment"
          >
            決済を行う
          </v-btn>

          <v-btn
            class="detail-btn"
            variant="outlined"
            size="large"
            block
            @click="handleGoToDetail"
          >
            予約詳細へ
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>
<style scoped>
.change-completion-dialog {
  border-radius: 10px !important;
  background: #ffffff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 440px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  text-align: center;
  margin: 0;
  width: 100%;
}

.info-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 311px;
  height: 208px;
}

.reservation-number {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #000000;
  text-align: left;
  width: 311px;
}

.payment-deadline {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #000000;
  text-align: left;
  width: 100%;
}

.description {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714285714;
  color: #000000;
  text-align: left;
  width: 100%;
  margin-top: 8px;
}

/* ボタンセクション */
.button-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  padding: 24px 0 0;
  position: absolute;
  bottom: 24px;
  left: 16px;
  width: 311px;
}

.payment-btn {
  width: 311px !important;
  height: 48px !important;
  background-color: #ed785f !important;
  color: #ffffff !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  border-radius: 4px !important;

  padding: 14px 13.38px !important;
}

.payment-btn:hover {
  background-color: #e56b50 !important;
}

.detail-btn {
  width: 311px !important;
  height: 48px !important;
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  color: #26499d !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  border-radius: 4px !important;

  padding: 14px 13.38px !important;
}

.detail-btn:hover {
  background-color: #d6e9f5 !important;
}
</style>
