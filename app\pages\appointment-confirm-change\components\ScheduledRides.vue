<script lang="ts" setup>
// インターフェース定義
interface Reservation {
  status: '決済済' | '決済待ち'
  reservationNumber: string
  rideDate: string
  departure: string
  destination: string
  via?: string
}

// リアクティブデータ
const expandedItems = ref<number[]>([])

// 予約データ（サンプル）
const reservations = ref<Reservation[]>([
  {
    status: '決済済',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪',
    via: '東京'
  },
  {
    status: '決済待ち',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪'
  },
  {
    status: '決済待ち',
    reservationNumber: '000000000000',
    rideDate: '3/21（金）乗車',
    departure: '岡山',
    destination: '大阪'
  }
])

// メソッド
const getStatusClass = (status: string) => {
  return {
    'status-paid': status === '決済済',
    'status-pending': status === '決済待ち'
  }
}

const toggleExpand = (index: number) => {
  const expandIndex = expandedItems.value.indexOf(index)
  if (expandIndex > -1) {
    expandedItems.value.splice(expandIndex, 1)
  } else {
    expandedItems.value.push(index)
  }
}

// 詳細ページへの遷移処理
const navigateToDetail = (reservation: Reservation) => {
  navigateTo({
    path: '/reservation-detail',
    query: {
      status: reservation.status
    }
  })
}
</script>
<template>
  <div class="scheduled-rides">
    <!-- 件数表示 -->
    <div class="count-section">
      <span class="count-text">3件（2件 決済待ち）</span>
    </div>

    <!-- 予約リスト -->
    <div class="reservation-list">
      <div
        v-for="(reservation, index) in reservations"
        :key="index"
        class="reservation-item"
        @click="navigateToDetail(reservation)"
      >
        <div class="reservation-content">
          <!-- ステータスと予約番号 -->
          <div class="reservation-header">
            <div
              class="status-badge"
              :class="getStatusClass(reservation.status)"
            >
              {{ reservation.status }}
            </div>
            <span class="reservation-number">
              予約番号：{{ reservation.reservationNumber }}
            </span>
          </div>

          <!-- 乗車情報 -->
          <div class="ride-info">
            <div class="ride-date">
              {{ reservation.rideDate }}
            </div>
            <div class="route-info">
              <span class="station">{{ reservation.departure }}</span>
              <v-icon size="20" color="#9CBCD4" class="arrow-icon">
                mdi-arrow-right
              </v-icon>
              <span class="station">{{ reservation.destination }}</span>
              <v-icon
                v-if="reservation.via"
                size="20"
                color="#9CBCD4"
                class="arrow-icon"
              >
                mdi-arrow-right
              </v-icon>
              <span v-if="reservation.via" class="station">
                {{ reservation.via }}
              </span>
            </div>
          </div>
        </div>

        <!-- 展開ボタン -->
        <v-btn
          icon
          variant="text"
          color="primary"
          size="small"
          @click="toggleExpand(index)"
        >
          <v-icon>mdi-chevron-right</v-icon>
        </v-btn>
      </div>
    </div>
  </div>
</template>
<style scoped>
.scheduled-rides {
  background-color: #ffffff;
}

.count-section {
  padding: 16px;
  border-bottom: 1px solid #dfdfdf;
  background-color: #ffffff;
}

.count-text {
  font-size: 16px;
  font-weight: 500;
  color: #26499d;
  line-height: 1.375;
}

.reservation-list {
  background-color: #ffffff;
}

.reservation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #dfdfdf;
  gap: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.reservation-item:hover {
  background-color: #f5f5f5;
}

.reservation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reservation-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  min-width: 58px;
  height: 24px;
}

.status-paid {
  background-color: #26499d;
  color: #ffffff;
  border: 1px solid #26499d;
}

.status-pending {
  background-color: #d00000;
  color: #ffffff;
  border: 1px solid #d00000;
}

.reservation-number {
  font-size: 14px;
  font-weight: 400;
  color: #7d7d7d;
  line-height: 1.571;
}

.ride-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ride-date {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  line-height: 1.375;
}

.route-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.station {
  font-size: 20px;
  font-weight: 400;
  color: #000000;
  line-height: 1.2;
}

.arrow-icon {
  margin: 0 2px;
}
</style>
