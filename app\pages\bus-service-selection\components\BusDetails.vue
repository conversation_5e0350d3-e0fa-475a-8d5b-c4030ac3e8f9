<script lang="ts" setup>
import type { ProductItem } from '~/types/easy-choice'

/* プロパティ定義 */
const props = defineProps<{
  products: ProductItem[]
}>()

/* 価格のフォーマット処理 */
const formatPrice = (price: number): string => {
  return price
    .toLocaleString('ja-JP', { minimumIntegerDigits: 6, useGrouping: false })
    .replace(/\d(?=(\d{3})+$)/g, '$&,')
}

/* ページトップへのスクロール処理 */
const scrollToTop = (): void => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}
</script>
<template>
  <div class="routePage">
    <div class="pageTitle">
      {{ products?.[0]?.origin }}
      <span class="mdi mdi-arrow-right"></span>
      {{ products?.[0]?.destination }}
      <span>ルートでのお得な商品</span>
    </div>

    <div class="productList">
      <div v-for="product in products" :key="product.id" class="productCard">
        <div class="routeTag">
          片道：
          <span v-for="(station, index) in product.route" :key="index">
            {{ station }}
            <span
              class="mdi mdi-arrow-right"
              v-if="index !== product.route.length - 1"
            ></span>
          </span>
        </div>

        <div class="productContent">
          <div class="productInfo">
            <h3 class="productTitle">{{ product.title }}</h3>
            <p class="productDesc">{{ product.description }}</p>
            <div class="productDetails">
              <div class="productImage">
                <img :src="product.image" :alt="product.title" />
              </div>
              <div class="detailBox">
                <div class="detailItem">
                  <span class="label">所要时间</span>
                  <span class="value">
                    约
                    <span class="highlight">{{ product.duration }}</span>
                    时间
                  </span>
                </div>

                <div class="detailItem">
                  <span class="label">乗り継ぎ回数</span>
                  <span class="value">
                    <span class="highlight">{{ product.transfers }}</span>
                    回
                  </span>
                </div>

                <div class="detailItem">
                  <span class="label">大人1人の料金</span>
                  <span class="value price">
                    {{ formatPrice(product.price) }}円
                  </span>
                </div>
              </div>
            </div>

            <div class="dateInfo">
              <span class="dateProduct">{{ product.date }}</span>
              <span class="dateEffective">乗車分まで有効</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="backToTop" @click="scrollToTop">
      <span
        class="mdi mdi-format-vertical-align-top"
        style="color: white; font-size: 30px"
      ></span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.routePage {
  margin: 0 auto;
  background-color: #fff7ec;
}

.pageTitle {
  color: #086cbf;
  font-size: 14px;
  text-align: center;
  padding-top: 20px;
}

.productList {
  padding: 0 16px 80px;
}

.productCard {
  margin-top: 16px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .routeTag {
    background: #ed785f;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
  }

  .productContent {
    padding: 16px;
    display: flex;
    gap: 12px;
  }

  .productImage {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .productInfo {
    flex: 1;
    min-width: 0;
  }

  .productTitle {
    font-size: 20px;
    font-weight: bold;
    color: #000;
    margin: 0 0 8px;
    line-height: 1.4;
  }

  .productDesc {
    font-size: 12px;
    color: #000;
    line-height: 1.3;
    margin: 0 0 12px;
  }

  .productDetails {
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    .detailBox {
      width: 100%;

      .detailItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;
        padding: 0 0 0 20px;

        .label {
          color: #000;
        }

        .value {
          color: #333;
          font-weight: bold;

          .highlight {
            color: #000;
            font-weight: bold;
            font-size: 20px;
          }

          &.price {
            font-size: 20px;
            color: #000;
          }
        }
      }
    }
  }

  .dateInfo {
    font-size: 11px;
    color: #3454a3;
    text-align: center;
    font-weight: bold;

    .dateEffective {
      color: #999;
    }
  }
}

.backToTop {
  position: fixed;
  bottom: 20px;
  right: 8px;
  width: 48px;
  height: 48px;
  background: #26499d;
  border-radius: 10%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.4);
  transition: all 0.3s ease;
}
</style>
