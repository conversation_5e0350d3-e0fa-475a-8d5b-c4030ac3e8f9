<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import {
  onMounted,
  ref,
  computed,
  watchEffect,
  nextTick,
  onUnmounted,
  watch
} from 'vue'
import {
  useSpecialSpotStore,
  useSpecialOriginStore,
  useSpecialWaypointStore,
  useSpecialAppStore
} from '~/stores/special-events'
import OneWayRoundTripRouteSelect from '~/components/OneWayRoundTripRouteSelect.vue'

/* ストア初期化 */
const spotStore = useSpecialSpotStore()
const originStore = useSpecialOriginStore()
const waypointStore = useSpecialWaypointStore()
const appStore = useSpecialAppStore()

const router = useRouter()
const route = useRoute()
const touristData = ref<any>(null)

/* 型定義 */
type LocationType = '' | 'prefecture' | 'area' | 'busStop'
type TripDirection = 'departure' | 'arrival'
type TripTypeOption = 'oneway' | 'roundtrip'

interface TripInfo {
  date: string
  time: string
  direction: TripDirection
}

interface SearchFormData {
  tripType: TripTypeOption
  departure: string
  departureId: string
  departureType: LocationType
  destination: string
  destinationId: string
  destinationType: LocationType
  waypoints: Array<{
    id: string
    location: string
    locationId: string
    locationType: LocationType
  }>
  date?: string
  time?: string
  direction?: TripDirection
  outbound?: TripInfo
  return?: TripInfo
}

/* 型定義 */
interface LocationData {
  name: string
  id: string | number
  type?: LocationType
  coordinates?: { lat: number; lng: number }
  busStopInfo?: string
  parentId?: string | number
  level?: number
  children?: LocationData[]
  hasChildren?: boolean
}

/* 選択された場所情報のインターフェース定義 */
interface SelectedLocation extends LocationData {
  type: LocationType
}

/* 位置タイプ検出クラス */
class LocationTypeDetector {
  static detectLocationType(location: LocationData | null): LocationType {
    if (!location) return ''
    if (location.type) return location.type

    if (this.isBusStop(location)) return 'busStop'
    if (this.isArea(location)) return 'area'
    return 'prefecture'
  }

  private static isBusStop(location: LocationData): boolean {
    return !!(
      location.busStopInfo ||
      this.hasStopKeywords(location.name) ||
      (!location.hasChildren && location.level && location.level >= 3)
    )
  }

  private static isArea(location: LocationData): boolean {
    return !!(
      (location.hasChildren && location.parentId) ||
      location.level === 2 ||
      this.hasAreaKeywords(location.name)
    )
  }

  private static hasStopKeywords(name: string): boolean {
    return /駅|站|バス停|停留所|乗り場|前|口/.test(name)
  }

  private static hasAreaKeywords(name: string): boolean {
    return /区|區|町|市|村|郡|地区|地區/.test(name)
  }
}

/* フォームデータ初期化 */
const formData = ref<SearchFormData>({
  tripType: appStore.savedTripType || 'oneway',
  departure: '',
  departureId: '',
  departureType: '',
  destination: '',
  destinationId: '',
  destinationType: '',
  waypoints: [],
  date: '',
  time: '',
  direction: 'departure'
})

/* 旅程タイプ変更ハンドラ */
const tripChange = (newTripType: TripTypeOption) => {
  isUpdatingForm = true

  const clearedData: SearchFormData = {
    tripType: newTripType,
    departure: '',
    departureId: '',
    departureType: '',
    destination: '',
    destinationId: '',
    destinationType: '',
    waypoints: [],
    date: '',
    time: '',
    direction: 'departure',
    outbound: { date: '', time: '', direction: 'departure' },
    return: { date: '', time: '', direction: 'departure' }
  }

  formData.value = clearedData
  selectedLocations.value = {
    departure: null,
    destination: null,
    waypoints: []
  }

  waypointStore.clearAllWaypoints()
  spotStore.clearCurrentTouristData()
  originStore.clearCurrentOriginData()

  appStore.setSavedTripType(newTripType)

  nextTick(() => {
    isUpdatingForm = false
  })
}

/* 選択された位置情報管理 */
const selectedLocations = ref({
  departure: null as SelectedLocation | null,
  destination: null as SelectedLocation | null,
  waypoints: [] as SelectedLocation[]
})

/* 再帰更新防止フラグ */
let isUpdatingForm = false

/* 経由地データ取得 */
const currentWaypoints = computed(() => waypointStore.getAllWaypoints)

/* 経由地をフォームに同期 */
const syncWaypointsToForm = () => {
  if (isUpdatingForm) {
    return
  }

  const waypoints = currentWaypoints.value

  if (!waypoints || waypoints.length === 0) {
    if (formData.value.waypoints.length === 0) {
      return
    }
    formData.value.waypoints = []
    selectedLocations.value.waypoints = []
    return
  }

  const currentFormWaypoints = formData.value.waypoints
  if (currentFormWaypoints.length === waypoints.length) {
    const needsUpdate = waypoints.some((waypoint, index) => {
      const formWaypoint = currentFormWaypoints[index]
      return (
        !formWaypoint ||
        formWaypoint.locationId !== waypoint.id?.toString() ||
        formWaypoint.location !== waypoint.name
      )
    })

    if (!needsUpdate) {
      return
    }
  }

  const formattedWaypoints = waypoints.map((waypoint, index) => {
    const detectedType = LocationTypeDetector.detectLocationType(waypoint)
    return {
      id: waypoint.id?.toString() || '',
      location: waypoint.name || '',
      locationId: waypoint.id?.toString() || '',
      locationType: detectedType
    }
  })

  isUpdatingForm = true

  formData.value = {
    ...formData.value,
    waypoints: formattedWaypoints
  }

  updateSelectedWaypoints(waypoints)

  nextTick(() => {
    isUpdatingForm = false
  })
}

/* 選択された経由地を更新 */
const updateSelectedWaypoints = (waypoints: LocationData[]) => {
  selectedLocations.value.waypoints = waypoints.map((waypoint, index) => {
    const detectedType = LocationTypeDetector.detectLocationType(waypoint)
    return {
      name: waypoint.name || '',
      id: waypoint.id?.toString() || '',
      type: detectedType as 'prefecture' | 'area' | 'busStop',
      coordinates: waypoint.coordinates || { lat: 0, lng: 0 },
      parentId: waypoint.parentId,
      level: waypoint.level,
      hasChildren: waypoint.hasChildren,
      children: waypoint.children,
      busStopInfo: waypoint.busStopInfo
    }
  })
}

/* 選択されたスポット取得 */
const selectedSpot = computed(() => spotStore.getSelectedSpot)

/* 経由地同期監視 */
watchEffect(() => {
  if (isUpdatingForm) return

  nextTick(() => {
    syncWaypointsToForm()
  })
})

/* 戻るボタンハンドラ */
function handleBack() {
  isUpdatingForm = true

  router.go(-1)

  setTimeout(() => {
    originStore.clearCurrentOriginData()
    spotStore.clearCurrentTouristData()
    waypointStore.clearAllWaypoints()

    Object.assign(formData.value, {
      departure: '',
      departureId: '',
      departureType: '',
      destination: '',
      destinationId: '',
      destinationType: '',
      waypoints: []
    })

    selectedLocations.value = {
      departure: null,
      destination: null,
      waypoints: []
    }
  }, 100)
}

/* 検索実行ハンドラ */
const handleSearch = async (): Promise<void> => {
  // 検索処理の実装
}

/* フォーム変更ハンドラ */
const handleFormChange = (newFormData: SearchFormData) => {
  if (isUpdatingForm) return

  if (
    newFormData.waypoints &&
    newFormData.waypoints.length < selectedLocations.value.waypoints.length
  ) {
    selectedLocations.value.waypoints = selectedLocations.value.waypoints.slice(
      0,
      newFormData.waypoints.length
    )
  }

  if (newFormData.tripType !== formData.value.tripType) {
    formData.value.tripType = newFormData.tripType
  }

  if (newFormData.date !== formData.value.date) {
    formData.value.date = newFormData.date
  }

  if (newFormData.time !== formData.value.time) {
    formData.value.time = newFormData.time
  }
}

/* ルート遷移ハンドラ */
const handleWaypoint = () => {
  router.push({
    path: '/transit-point',
    query: {
      mode: 'waypoint'
    }
  })
}

const handleDeparture = () => {
  router.push({
    path: '/target-location',
    query: {
      mode: 'departure'
    }
  })
}

const handleDestinationMapClick = () => {
  router.push({
    path: '/origin-location',
    query: {
      mode: 'destination'
    }
  })
}

/* 位置情報更新処理 */
const updateLocationData = (
  location: LocationData | null,
  isDestination = false
) => {
  if (isUpdatingForm) return

  if (!location || !location.name) {
    if (isDestination) {
      Object.assign(formData.value, {
        destination: '',
        destinationId: '',
        destinationType: ''
      })
    } else {
      Object.assign(formData.value, {
        departure: '',
        departureId: '',
        departureType: ''
      })
      selectedLocations.value.departure = null
    }
    return
  }

  const detectedType = LocationTypeDetector.detectLocationType(location)

  if (isDestination) {
    Object.assign(formData.value, {
      destination: location.name,
      destinationId: location.id?.toString() || '',
      destinationType: detectedType
    })
  } else {
    Object.assign(formData.value, {
      departure: location.name,
      departureId: location.id?.toString() || '',
      departureType: detectedType
    })

    if (detectedType) {
      selectedLocations.value.departure = {
        name: location.name,
        id: location.id?.toString() || '',
        type: detectedType as 'prefecture' | 'area' | 'busStop',
        coordinates: location.coordinates || { lat: 0, lng: 0 },
        parentId: location.parentId,
        level: location.level,
        hasChildren: location.hasChildren,
        children: location.children,
        busStopInfo: location.busStopInfo
      }
    }
  }
}

/* 旅程タイプ保存監視 */
watch(
  () => formData.value.tripType,
  (newValue) => {
    appStore.setSavedTripType(newValue)
  },
  { immediate: true }
)

/* スポット選択監視 */
watchEffect(() => {
  const spot = selectedSpot.value
  updateLocationData(spot, true)
})

/* 出発地データ監視 */
watchEffect(() => {
  const originData = originStore.currentOriginData
  updateLocationData(originData, false)
})

/* コンポーネント初期化 */
onMounted(() => {
  if (route.query.touristData) {
    try {
      touristData.value = JSON.parse(route.query.touristData as string)
    } catch (error) {
      console.log(error)
    }
  }

  nextTick(() => {
    syncWaypointsToForm()
  })
})

/* コンポーネント破棄時処理 */
onUnmounted(() => {
  isUpdatingForm = true
})
</script>

<template>
  <div class="pageContainer">
     <div class="regionpadding">
        <BaseHeader title="ルート検索" :showBack="true" :showRightIcon="false" />
    </div>
    
    <div class="destinationBox">
      <div class="destination">
        目的地：{{ spotStore.currentTouristData?.name }}
      </div>
      <div class="round">
        <OneWayRoundTripRouteSelect
          :departure-config="{
            value: formData.departure,
            readonly: true,
            showMapButton: false
          }"
          :destination-config="{
            value: formData.destination,
            readonly: true,
            showMapButton: true
          }"
          :waypoint-config="{
            readonly: true,
            showMapButton: false
          }"
          :initial-form-data="formData"
          @search="handleSearch"
          @form-change="handleFormChange"
          @departure-click="handleDeparture"
          @destination-click="handleDestinationMapClick"
          @waypoint-click="handleWaypoint"
          :initialTripType="formData.tripType"
          @trip-type-change="tripChange"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pageContainer {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.round {
  padding: 20px;
  background-color: #fff;
  margin: 20px 20px 0 20px;
  border-radius: 10px;
}

.destination {
  font-size: 14px;
  padding: 16px 20px 0 20px;
}

.destinationBox {
  background-color: #e7f2fa;
  padding-bottom: 20px;
  flex-grow: 1;
}
</style>
