<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useGLStore } from '~/stores/globalLoading'

const gl = useGLStore()
const { isGlobalLoading } = storeToRefs(gl)
</script>
<template>
  <v-overlay :model-value="isGlobalLoading" class="loading" height="100%" width="100%">
    <v-progress-circular class="v-progress-circular" color="primary" indeterminate size="64" />
  </v-overlay>
</template>
<style lang="scss">
.loading {
  .v-progress-circular {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
