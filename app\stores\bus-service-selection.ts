import { defineStore } from 'pinia'
import { reactive } from 'vue'

// 乗客数の型定義
interface PassengerCounts {
  adult: { male: number; female: number }
  child: { male: number; female: number }
  student: { male: number; female: number }
  disabilityAdult: { male: number; female: number }
  disabilityChild: { male: number; female: number }
  disability: { male: number; female: number }
}

// バスサービスの型定義
interface BusService {
  tripNumber: string
  route: string
  datePicker: string
  date: string
  serviceName: string
  operator: string
  timeType: string
  price: string
  features: string
  selectedStop: string | null
  selectedDropOff: string | null
  passengerCount: string | null
  passengerDetails: string | null
  passengerCounts: PassengerCounts | null // 新增：乗客詳細数
  adultCount: number | null
  wheelchairCount: string | null
  availableSeats: number
  departureTime: string
  selectedSeats: string[]
  amenities: Array<{
    name: string
    type?: string
    icon?: string
    iconColor?: string
  }>
}

// ルート情報の型定義
interface RouteInfo {
  routeId: number
  order: number
  departureId: number
  departureType: 'prefecture' | 'area' | 'busStop'
  departureName: string
  destinationId: number
  destinationType: 'prefecture' | 'area' | 'busStop'
  destinationName: string
  busService: BusService[]
}

interface RouteList {
  // 移除 arrival 字段，因为不再需要
  // arrival: any
  // 移除全局的 origin, destination, transferCount, transferCondition
  // 这些字段现在在 outbound 和 inbound 中
}

// 扩展的路线列表接口，包含完整的搜索表单数据
interface EnhancedRouteList extends RouteList {
  tripType: 'oneway' | 'roundtrip'
  outbound: {
    date: string
    time: string
    direction: 'departure' | 'arrival'
    departureId: string
    departureType: 'prefecture' | 'area' | 'busStop'
    departureName: string
    destinationId: string
    destinationType: 'prefecture' | 'area' | 'busStop'
    destinationName: string
    transferCount: string
    transferCondition: string
    waypoints: Array<{
      id: string
      location: string
      locationId: string
      locationType: 'prefecture' | 'area' | 'busStop'
    }>
  }
  inbound: {
    date: string
    time: string
    direction: 'departure' | 'arrival'
    departureId: string
    departureType: 'prefecture' | 'area' | 'busStop'
    departureName: string
    destinationId: string
    destinationType: 'prefecture' | 'area' | 'busStop'
    destinationName: string
    transferCount: string
    transferCondition: string
    waypoints: Array<{
      id: string
      location: string
      locationId: string
      locationType: 'prefecture' | 'area' | 'busStop'
    }>
  }
}

interface FullRouteData extends RouteList {
  routes: RouteInfo[]
}

export const useRouteStore = defineStore('route', {
  state: () => ({
    currentSelectedRouteDirection: 'inbound' as 'inbound' | 'outbound',
    discountInfo:[
      { count: 1, rate: '2%' },
      { count: 2, rate: '5%' },
      { count: 3, rate: '8%' },
      { count: 4, rate: '10%' },
      { count: 5, rate: '12%' },
    ],
    routeSelected: {
      // 统一的路线列表数据，无论是片道还是往返都使用同一套结构
      routeList: {
        // 移除全局字段，现在都在 outbound 和 inbound 中
        tripType: 'oneway' as 'oneway' | 'roundtrip',
        outbound: {
          date: '',
          time: '',
          direction: 'departure' as 'departure' | 'arrival',
          departureId: '',
          departureType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          departureName: '',
          destinationId: '',
          destinationType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          destinationName: '',
          transferCount: '',
          transferCondition: '',
          waypoints: [] as Array<{
            id: string
            location: string
            locationId: string
            locationType: 'prefecture' | 'area' | 'busStop'
          }>
        },
        inbound: {
          date: '',
          time: '',
          direction: 'departure' as 'departure' | 'arrival',
          departureId: '',
          departureType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          departureName: '',
          destinationId: '',
          destinationType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          destinationName: '',
          transferCount: '',
          transferCondition: '',
          waypoints: [] as Array<{
            id: string
            location: string
            locationId: string
            locationType: 'prefecture' | 'area' | 'busStop'
          }>
        }
      } as EnhancedRouteList,
      // 保留 routeInbound 结构，包含 routes 等数据
      routeOutbound: {
        transferCount:2,
        routes: [
          {
            routeId: 4,
            order: 1,
            departureId: 3,
            departureType: 'prefecture' as const,
            departureName: '東京',
            destinationId: 6,
            destinationType: 'prefecture' as const,
            destinationName: '名古屋',
            busService: [
              {
                tripNumber: '1便目',
                route: '東京→名古屋',
                date: '2025年3/25（月）',
                datePicker: '3/25（月）',
                serviceName: '名東プレミアム号',
                operator: '○○バス',
                timeType: '昼便',
                price: '9,000',
                features: 'プレミアム',
                selectedStop: '東京駅（日本橋口）',
                selectedDropOff: '名古屋駅太閤通口',
                passengerCount: null,
                passengerDetails: null,
                passengerCounts: null,
                adultCount: 2,
                wheelchairCount: null,
                availableSeats: 8,
                departureTime: '09:00',
                selectedSeats: ['A6', 'A7'],
                amenities: [
                  {
                    name: '昼行便',
                    type: 'day',
                    icon: 'mdi-white-balance-sunny',
                    iconColor: '#F2B774'
                  },
                  { name: '4列シート', type: 'normal' },
                  { name: 'トイレ付', type: 'normal' },
                  { name: '座席指定', type: 'normal' },
                  { name: 'Wi-Fi', type: 'normal' },
                  { name: '女性安心', type: 'normal' },
                  { name: 'プレミアムシート', type: 'normal' },
                  { name: 'フットレスト', type: 'normal' },
                  { name: '読書灯', type: 'normal' }
                ]
              }
            ]
          },
          {
            routeId: 5,
            order: 2,
            departureId: 6,
            departureType: 'prefecture' as const,
            departureName: '名古屋1',
            destinationId: 5,
            destinationType: 'prefecture' as const,
            destinationName: '大阪',
            busService: [
              {
                tripNumber: '2便目',
                route: '名古屋→大阪',
                date: '2025年3/25（月）',
                datePicker: '3/25（月）',
                serviceName: '関西・東海ブリッジ号',
                operator: '○○バス',
                timeType: '昼便',
                price: '4,500',
                features: '快適',
                selectedStop: '名古屋駅太閤通口',
                selectedDropOff: '湊町バスターミナル（OCAT）',
                passengerCount: null,
                passengerDetails: null,
                passengerCounts: null,
                adultCount: 2,
                wheelchairCount: null,
                availableSeats: 6,
                departureTime: '14:00',
                selectedSeats: ['B6', 'B7'],
                amenities: [
                  {
                    name: '昼行便',
                    type: 'day',
                    icon: 'mdi-white-balance-sunny',
                    iconColor: '#F2B774'
                  },
                  { name: '4列シート', type: 'normal' },
                  { name: 'トイレ付', type: 'normal' },
                  { name: '座席指定', type: 'normal' },
                  { name: 'Wi-Fi', type: 'normal' },
                  { name: '女性安心', type: 'normal' },
                  { name: 'USB充電', type: 'normal' },
                  { name: 'リクライニング', type: 'normal' }
                ]
              }
            ]
          },
          {
            routeId: 6,
            order: 3,
            departureId: 5,
            departureType: 'prefecture' as const,
            departureName: '大阪',
            destinationId: 1,
            destinationType: 'prefecture' as const,
            destinationName: '岡山1',
            busService: [
              {
                tripNumber: '3便目',
                route: '大阪→岡山',
                date: '2025年3/26（火）',
                datePicker: '3/26（火）',
                serviceName: '備前ウィング大阪線',
                operator: '○○バス',
                timeType: '夜便',
                price: '7,200',
                features: '夜行',
                selectedStop: '湊町バスターミナル（OCAT）',
                selectedDropOff: '岡山駅 西口',
                passengerCount: null,
                passengerDetails: null,
                passengerCounts: null,
                adultCount: 2,
                wheelchairCount: null,
                availableSeats: 10,
                departureTime: '18:30',
                selectedSeats: ['A8', 'A9'],
                amenities: [
                  {
                    name: '夜行便',
                    type: 'night',
                    icon: 'mdi-weather-night',
                    iconColor: '#4A5568'
                  },
                  { name: '4列シート', type: 'normal' },
                  { name: 'トイレ付', type: 'normal' },
                  { name: '座席指定', type: 'normal' },
                  { name: 'Wi-Fi', type: 'normal' },
                  { name: '女性安心', type: 'normal' },
                  { name: 'コンセント', type: 'normal' },
                  { name: 'ブランケット', type: 'normal' },
                  { name: 'アイマスク', type: 'normal' },
                  { name: 'スリッパ', type: 'normal' }
                ]
              }
            ]
          }
        ] as RouteInfo[]
      },
      routeInbound: {
        transferCount:2,
        routes: [
          {
            routeId: 4,
            order: 1,
            departureId: 3,
            departureType: 'prefecture' as const,
            departureName: '東京',
            destinationId: 6,
            destinationType: 'prefecture' as const,
            destinationName: '名古屋',
            busService: [
              {
                tripNumber: '1便目',
                route: '東京→名古屋',
                date: '2025年3/25（月）',
                datePicker: '3/25（月）',
                serviceName: '名東プレミアム号',
                operator: '○○バス',
                timeType: '昼便',
                price: '9,000',
                features: 'プレミアム',
                selectedStop: '東京駅（日本橋口）',
                selectedDropOff: '名古屋駅太閤通口',
                passengerCount: null,
                passengerDetails: null,
                passengerCounts: null,
                adultCount: 2,
                wheelchairCount: null,
                availableSeats: 8,
                departureTime: '09:00',
                selectedSeats: ['A6', 'A7'],
                amenities: [
                  {
                    name: '昼行便',
                    type: 'day',
                    icon: 'mdi-white-balance-sunny',
                    iconColor: '#F2B774'
                  },
                  { name: '4列シート', type: 'normal' },
                  { name: 'トイレ付', type: 'normal' },
                  { name: '座席指定', type: 'normal' },
                  { name: 'Wi-Fi', type: 'normal' },
                  { name: '女性安心', type: 'normal' },
                  { name: 'プレミアムシート', type: 'normal' },
                  { name: 'フットレスト', type: 'normal' },
                  { name: '読書灯', type: 'normal' }
                ]
              }
            ]
          },
          {
            routeId: 5,
            order: 2,
            departureId: 6,
            departureType: 'prefecture' as const,
            departureName: '名古屋',
            destinationId: 5,
            destinationType: 'prefecture' as const,
            destinationName: '大阪',
            busService: [
              {
                tripNumber: '2便目',
                route: '名古屋→大阪',
                date: '2025年3/25（月）',
                datePicker: '3/25（月）',
                serviceName: '関西・東海ブリッジ号',
                operator: '○○バス',
                timeType: '昼便',
                price: '4,500',
                features: '快適',
                selectedStop: '名古屋駅太閤通口',
                selectedDropOff: '湊町バスターミナル（OCAT）',
                passengerCount: null,
                passengerDetails: null,
                passengerCounts: null,
                adultCount: 2,
                wheelchairCount: null,
                availableSeats: 6,
                departureTime: '14:00',
                selectedSeats: ['B6', 'B7'],
                amenities: [
                  {
                    name: '昼行便',
                    type: 'day',
                    icon: 'mdi-white-balance-sunny',
                    iconColor: '#F2B774'
                  },
                  { name: '4列シート', type: 'normal' },
                  { name: 'トイレ付', type: 'normal' },
                  { name: '座席指定', type: 'normal' },
                  { name: 'Wi-Fi', type: 'normal' },
                  { name: '女性安心', type: 'normal' },
                  { name: 'USB充電', type: 'normal' },
                  { name: 'リクライニング', type: 'normal' }
                ]
              }
            ]
          },
          {
            routeId: 6,
            order: 3,
            departureId: 5,
            departureType: 'prefecture' as const,
            departureName: '大阪',
            destinationId: 1,
            destinationType: 'prefecture' as const,
            destinationName: '岡山',
            busService: [
              {
                tripNumber: '3便目',
                route: '大阪→岡山',
                date: '2025年3/26（火）',
                datePicker: '3/26（火）',
                serviceName: '備前ウィング大阪線',
                operator: '○○バス',
                timeType: '夜便',
                price: '7,200',
                features: '夜行',
                selectedStop: '湊町バスターミナル（OCAT）',
                selectedDropOff: '岡山駅 西口',
                passengerCount: null,
                passengerDetails: null,
                passengerCounts: null,
                adultCount: 2,
                wheelchairCount: null,
                availableSeats: 10,
                departureTime: '18:30',
                selectedSeats: ['A8', 'A9'],
                amenities: [
                  {
                    name: '夜行便',
                    type: 'night',
                    icon: 'mdi-weather-night',
                    iconColor: '#4A5568'
                  },
                  { name: '4列シート', type: 'normal' },
                  { name: 'トイレ付', type: 'normal' },
                  { name: '座席指定', type: 'normal' },
                  { name: 'Wi-Fi', type: 'normal' },
                  { name: '女性安心', type: 'normal' },
                  { name: 'コンセント', type: 'normal' },
                  { name: 'ブランケット', type: 'normal' },
                  { name: 'アイマスク', type: 'normal' },
                  { name: 'スリッパ', type: 'normal' }
                ]
              }
            ]
          }
        ] as RouteInfo[]
      }
    }
  }),
  getters: {
    getFullRouteData(state): FullRouteData {
      return reactive({
        ...state.routeSelected.routeList,
        routes: state.routeSelected.routeOutbound.routes
      }) as FullRouteData
    },

    // 現在選択中のルート方向のバスサービス一覧を取得
    getCurrentBusServices: (state) => {
      const currentRoute =
        state.currentSelectedRouteDirection === 'inbound'
          ? state.routeSelected.routeInbound
          : state.routeSelected.routeOutbound

      return currentRoute.routes?.flatMap((route) => route.busService) || []
    },

    // 指定されたルートIDのバスサービスを取得
    getBusServicesByRouteId: (state) => (routeId: number) => {
      const currentRoute =
        state.currentSelectedRouteDirection === 'inbound'
          ? state.routeSelected.routeInbound
          : state.routeSelected.routeOutbound

      const route = currentRoute.routes?.find((r) => r.routeId === routeId)
      return route?.busService || []
    }
  },
  actions: {
    // 动态更新路由数据，只更新提供的字段
    setRouteData(payload: Partial<EnhancedRouteList>) {
      this.routeSelected.routeList = {
        ...this.routeSelected.routeList,
        ...payload
      }
      console.log('Route data updated:', this.routeSelected.routeList)
    },

    // 从 RoundTripForm 数据转换并设置到 store
    setFromRoundTripForm(data: {
      type: string
      departure: string
      destination: string
      waypoints: string[]
      outbound: {
        date: string
        time: string
        direction: 'departure' | 'arrival'
      }
      return: { date: string; time: string; direction: 'departure' | 'arrival' }
      departureId?: string
      departureType?: string
      destinationId?: string
      destinationType?: string
      waypointDetails?: Array<{
        id: string
        location: string
        locationId: string
        locationType: string
      }>
    }) {
      console.log('setFromRoundTripForm - 接収到的数据:', data)

      const waypoints = (data.waypointDetails || []).map((wp) => ({
        ...wp,
        locationType: wp.locationType as 'prefecture' | 'area' | 'busStop'
      }))

      const outboundData = {
        date: data.outbound.date || '',
        time: data.outbound.time || '',
        direction: data.outbound.direction || 'departure',
        departureId: data.departureId || '',
        departureType:
          (data.departureType as 'prefecture' | 'area' | 'busStop') ||
          'prefecture',
        departureName: data.departure || '',
        destinationId: data.destinationId || '',
        destinationType:
          (data.destinationType as 'prefecture' | 'area' | 'busStop') ||
          'prefecture',
        destinationName: data.destination || '',
        transferCount: '',
        transferCondition: '',
        waypoints: waypoints
      }

      let inboundData
      if (data.type === 'roundtrip') {
        inboundData = {
          date: data.return.date || '',
          time: data.return.time || '',
          direction: data.return.direction || 'departure',
          departureId: data.destinationId || '',
          departureType:
            (data.destinationType as 'prefecture' | 'area' | 'busStop') ||
            'prefecture',
          departureName: data.destination || '',
          destinationId: data.departureId || '',
          destinationType:
            (data.departureType as 'prefecture' | 'area' | 'busStop') ||
            'prefecture',
          destinationName: data.departure || '',
          transferCount: '',
          transferCondition: '',
          waypoints: [...waypoints].reverse()
        }
      } else {
        inboundData = {
          date: '',
          time: '',
          direction: 'departure' as 'departure' | 'arrival',
          departureId: '',
          departureType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          departureName: '',
          destinationId: '',
          destinationType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          destinationName: '',
          transferCount: '',
          transferCondition: '',
          waypoints: []
        }
      }

      this.routeSelected.routeList = {
        ...this.routeSelected.routeList,
        tripType: data.type as 'oneway' | 'roundtrip',
        outbound: outboundData,
        inbound: inboundData
      }
      console.log(
        'setFromRoundTripForm - 更新后的 routeList:',
        this.routeSelected.routeList
      )
    },

    // 从 OneWayRoundTripRouteSelect 数据转换并设置到 store
    setFromSearchForm(data: {
      tripType?: 'oneway' | 'roundtrip'
      type?: string
      departure: string
      departureId: string
      departureType: 'prefecture' | 'area' | 'busStop'
      destination: string
      destinationId: string
      destinationType: 'prefecture' | 'area' | 'busStop'
      waypoints: Array<{
        id: string
        location: string
        locationId: string
        locationType: 'prefecture' | 'area' | 'busStop'
      }>
      date: string
      time: string
      direction: 'departure' | 'arrival'
      outbound?: {
        date: string
        time: string
        direction: 'departure' | 'arrival'
      }
      return?: {
        date: string
        time: string
        direction: 'departure' | 'arrival'
      }
    }) {
      console.log('setFromSearchForm - 接収到的数据:', data)

      const tripType =
        data.tripType || (data.type as 'oneway' | 'roundtrip') || 'oneway'

      const outboundData = {
        date:
          tripType === 'oneway' ? data.date || '' : data.outbound?.date || '',
        time:
          tripType === 'oneway' ? data.time || '' : data.outbound?.time || '',
        direction:
          tripType === 'oneway'
            ? data.direction || 'departure'
            : data.outbound?.direction || 'departure',
        departureId: data.departureId,
        departureType: data.departureType,
        departureName: data.departure,
        destinationId: data.destinationId,
        destinationType: data.destinationType,
        destinationName: data.destination,
        transferCount: '',
        transferCondition: '',
        waypoints: data.waypoints
      }

      let inboundData
      if (tripType === 'roundtrip') {
        inboundData = {
          date: data.return?.date || '',
          time: data.return?.time || '',
          direction: data.return?.direction || 'departure',
          departureId: data.destinationId,
          departureType: data.destinationType,
          departureName: data.destination,
          destinationId: data.departureId,
          destinationType: data.departureType,
          destinationName: data.departure,
          transferCount: '',
          transferCondition: '',
          waypoints: [...data.waypoints].reverse()
        }
      } else {
        inboundData = {
          date: '',
          time: '',
          direction: 'departure' as 'departure' | 'arrival',
          departureId: '',
          departureType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          departureName: '',
          destinationId: '',
          destinationType: 'prefecture' as 'prefecture' | 'area' | 'busStop',
          destinationName: '',
          transferCount: '',
          transferCondition: '',
          waypoints: []
        }
      }

      this.routeSelected.routeList = {
        ...this.routeSelected.routeList,
        tripType: tripType,
        outbound: outboundData,
        inbound: inboundData
      }
      console.log(
        'Route data updated from SearchForm:',
        this.routeSelected.routeList
      )
    },

    // ルート方向を設定
    setDirection(direction: 'inbound' | 'outbound') {
      this.currentSelectedRouteDirection = direction
      console.log('Route direction updated:', direction)
    },

    // バスサービス情報を更新
    updateBusService(
      routeId: number,
      serviceIndex: number,
      updates: Partial<BusService>
    ) {
      const currentRoute =
        this.currentSelectedRouteDirection === 'inbound'
          ? this.routeSelected.routeInbound
          : this.routeSelected.routeOutbound

      const route = currentRoute.routes?.find((r) => r.routeId === routeId)
      if (route?.busService[serviceIndex]) {
        Object.assign(route.busService[serviceIndex], updates)
        console.log(
          `Bus service updated - Route ID: ${routeId}, Service Index: ${serviceIndex}`,
          route.busService[serviceIndex]
        )
      }
    },

    // 停留所選択を更新
    updateStopSelection(
      routeId: number,
      serviceIndex: number,
      stopType: 'pickup' | 'dropoff',
      stopName: string
    ) {
      const updates =
        stopType === 'pickup'
          ? { selectedStop: stopName }
          : { selectedDropOff: stopName }

      this.updateBusService(routeId, serviceIndex, updates)
      console.log(
        `Stop selection updated - Route ID: ${routeId}, Service Index: ${serviceIndex}, ${stopType}: ${stopName}`
      )
    },

    // 乗客情報を更新
    updatePassengerInfo(
      routeId: number,
      serviceIndex: number,
      passengerInfo: {
        adultCount?: number | null
        passengerDetails?: string | null
        wheelchairCount?: string | null
      }
    ) {
      this.updateBusService(routeId, serviceIndex, passengerInfo)
      console.log(
        `Passenger info updated - Route ID: ${routeId}, Service Index: ${serviceIndex}`,
        passengerInfo
      )
    },

    // 座席選択を更新
    updateSeatSelection(
      routeId: number,
      serviceIndex: number,
      selectedSeats: string[]
    ) {
      const updates: Partial<BusService> = { selectedSeats }

      if (selectedSeats.length > 0) {
        updates.wheelchairCount = selectedSeats.join('、')
      } else {
        const currentRoute =
          this.currentSelectedRouteDirection === 'inbound'
            ? this.routeSelected.routeInbound
            : this.routeSelected.routeOutbound

        const route = currentRoute.routes?.find((r) => r.routeId === routeId)
        const service = route?.busService[serviceIndex]

        if (service?.passengerDetails) {
          const disabilityCount = this.calculateDisabilityCount(
            service.passengerDetails
          )
          updates.wheelchairCount =
            disabilityCount > 0 ? disabilityCount.toString() : null
        } else {
          updates.wheelchairCount = null
        }
      }

      this.updateBusService(routeId, serviceIndex, updates)
      console.log(
        `Seat selection updated - Route ID: ${routeId}, Service Index: ${serviceIndex}, Seats: ${selectedSeats.join(
          ', '
        )}`
      )
    },

    // 障害者人数を計算するヘルパーメソッド
    calculateDisabilityCount(passengerDetails: string): number {
      if (!passengerDetails) return 0

      const disabilityAdultMatch = passengerDetails.match(
        /障害者大人：[^/]*男性(\d+)名|障害者大人：[^/]*女性(\d+)名/g
      )
      const disabilityChildMatch = passengerDetails.match(
        /障害者子供：[^/]*男性(\d+)名|障害者子供：[^/]*女性(\d+)名/g
      )

      let totalDisability = 0

      if (disabilityAdultMatch) {
        disabilityAdultMatch.forEach((match) => {
          const numbers = match.match(/(\d+)名/g)
          if (numbers) {
            numbers.forEach((num) => {
              totalDisability += parseInt(num.replace('名', ''))
            })
          }
        })
      }

      if (disabilityChildMatch) {
        disabilityChildMatch.forEach((match) => {
          const numbers = match.match(/(\d+)名/g)
          if (numbers) {
            numbers.forEach((num) => {
              totalDisability += parseInt(num.replace('名', ''))
            })
          }
        })
      }

      console.log(`Disability count calculated: ${totalDisability}`)
      return totalDisability
    },

    // 新增：乗客数情報を更新するアクション
    updatePassengerCounts(
      routeId: number,
      serviceIndex: number,
      passengerCounts: PassengerCounts,
      formattedInfo: string
    ) {
      // 人数大于0的数据を抽出
      const filteredCounts: PassengerCounts = {
        adult: { male: 0, female: 0 },
        child: { male: 0, female: 0 },
        student: { male: 0, female: 0 },
        disabilityAdult: { male: 0, female: 0 },
        disabilityChild: { male: 0, female: 0 },
        disability: { male: 0, female: 0 }
      }

      // 只保存人数大于0的数据
      Object.keys(passengerCounts).forEach((key) => {
        const category = key as keyof PassengerCounts
        const counts = passengerCounts[category]
        if (counts.male > 0 || counts.female > 0) {
          filteredCounts[category] = { ...counts }
        }
      })

      // 计算总成人数
      const totalAdults =
        filteredCounts.adult.male +
        filteredCounts.adult.female +
        filteredCounts.student.male +
        filteredCounts.student.female +
        filteredCounts.disabilityAdult.male +
        filteredCounts.disabilityAdult.female +
        filteredCounts.disability.male +
        filteredCounts.disability.female

      const updates: Partial<BusService> = {
        passengerCounts: filteredCounts,
        passengerDetails: formattedInfo,
        adultCount: totalAdults > 0 ? totalAdults : null
      }

      this.updateBusService(routeId, serviceIndex, updates)
      console.log(
        `Passenger counts updated - Route ID: ${routeId}, Service Index: ${serviceIndex}`,
        { filteredCounts, formattedInfo, totalAdults }
      )
    }
  }
})

// PassengerCounts 型をエクスポート
export type { PassengerCounts,RouteInfo ,BusService }
