import { defineStore } from 'pinia'
import type { UserFormData } from '~/types/member-Registration'

export const useRegistrationFormStore = defineStore('registrationForm', {
  state: () => ({
    submittedData: null as UserFormData | null
  }),
  actions: {
    saveSubmittedData(data: UserFormData) {
      this.submittedData = { ...data }
    },
    clearSubmittedData() {
      this.submittedData = null
    }
  }
})
