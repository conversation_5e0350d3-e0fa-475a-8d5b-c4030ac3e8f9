<script lang="ts" setup>
const props = defineProps<{
  modelValue: boolean
  paymentInfo: {
    reservationNo: string | number
    amount: string
    coupon: string
    cardHolder: string
    cardNumber: string
    cardExpiry: string
  }
}>()
const emit = defineEmits(['update:modelValue', 'confirm'])

const handleConfirm = () => {
  emit('confirm')
}
const handleCancel = () => {
  emit('update:modelValue', false)
}
</script>

<template>
  <div class="payment-dialog-container">
    <v-dialog v-model="props.modelValue" max-width="480">
      <v-card>
        <v-card-title class="text-h6 font-weight-regular mt-2 pl-3">決済情報確認</v-card-title>
        <v-card-text class="px-3">
          <p class="mb-5">
            決済を実行します。
            <br />
            決済情報にお間違いがないか確認してください。
          </p>

          <v-row>
            <v-col cols="4">予約番号：</v-col>
            <v-col cols="8">{{ props.paymentInfo.reservationNo }}</v-col>
          </v-row>

          <v-row>
            <v-col cols="4">決済金額：</v-col>
            <v-col cols="8">
              ￥{{ props.paymentInfo.amount }}
            </v-col>
          </v-row>

          <v-row v-if="props.paymentInfo.coupon" class="coupon-fs">
            <v-col cols="4" class="pr-0">クーポン利用：</v-col>
            <v-col cols="8" class="pl-2">{{ props.paymentInfo.coupon }}</v-col>
          </v-row>

          <v-divider class="my-5"></v-divider>

          <v-row class="coupon-fs">
            <v-col cols="7">クレジットカード名義</v-col>
            <v-col cols="5">{{ props.paymentInfo.cardHolder }}</v-col>
          </v-row>
          <v-row class="coupon-fs">
            <v-col cols="7">クレジットカード番号</v-col>
            <v-col cols="5">{{ props.paymentInfo.cardNumber }}</v-col>
          </v-row>
          <v-row class="coupon-fs">
            <v-col cols="7">クレジットカード有効期限</v-col>
            <v-col cols="5">{{ props.paymentInfo.cardExpiry }}</v-col>
          </v-row>
        </v-card-text>

        <div class="payment-btn d-flex flex-column mb-4 px-3">
          <v-btn
            block
            color="#ed785f"
            class="mb-2"
            height="40"
            rounded="xs"
            @click="handleConfirm"
          >
            決済を確定する
          </v-btn>
          <v-btn
            block
            color="#e7f2fa"
            class="cancel-btn"
            height="40"
            rounded="xs"
            @click="handleCancel"
          >
            キャンセル
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped>
.v-card-text p,
.coupon-fs .v-col {
  font-size: 13px;
}
.v-col {
  font-size: 15px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.cancel-btn {
  border: 1px solid #26499d;
  color: #26499d !important;
}
.payment-btn .v-btn {
  box-shadow: unset !important;
}
</style>
