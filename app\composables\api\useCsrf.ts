type CsrfToken = {
  headerName: string
  parameterName: string
  token: string
}

let csrfHeaderName: string | null = null
let csrfToken: string | null = null
let inflight: Promise<void> | null = null

async function fetchCsrf(): Promise<void> {
  try {
    const api = useApi()
    const res = await api<CsrfToken>('/backend/auth/csrf', { method: 'GET' })
    csrfHeaderName = res.headerName
    csrfToken = res.token
  } catch (error) {
    // If CSRF fetch fails (e.g., no backend), set default values
    console.warn('Failed to fetch CSRF token:', error)
    csrfHeaderName = 'X-CSRF-Token'
    csrfToken = 'fallback-token'
  }
}

async function ensureCsrf(): Promise<void> {
  if (csrfHeaderName && csrfToken) return
  inflight ??= fetchCsrf().finally(() => (inflight = null))
  await inflight
}

async function refreshCsrf(): Promise<void> {
  await fetchCsrf()
}

function applyCsrf(headers: Record<string, any>) {
  if (csrfHeaderName && csrfToken) {
    headers[csrfHeaderName] = csrfToken
  }
}

function clearCsrf(): void {
  csrfHeaderName = null
  csrfToken = null
  inflight = null
}

export function useCsrf() {
  return {
    ensureCsrf,
    refreshCsrf,
    applyCsrf,
    clearCsrf,
  }
}


