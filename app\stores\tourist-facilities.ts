import { defineStore } from 'pinia'
import type { TouristSpot } from '~/types/tourist-facilities'

// ルート検索tabs
export const useAppStore = defineStore('app', {
  state: () => ({
    savedTripType: 'oneway' as 'oneway' | 'roundtrip'
  }),
  actions: {
    setSavedTripType(type: 'oneway' | 'roundtrip') {
      this.savedTripType = type
    }
  }
})

// モック
export const useWaypointStore = defineStore('waypoint', {
  state: () => ({
    waypoints: [] as TouristSpot[]
  }),
  getters: {
    getAllWaypoints: (state) => {
      return [...state.waypoints]
    },
    getWaypointCount: (state) => state.waypoints.length
  },
  actions: {
    addWaypoint(data: TouristSpot) {
      const exists = this.waypoints.some(
        (waypoint) => waypoint.id === data.id && waypoint.name === data.name
      )

      if (!exists) {
        this.waypoints.push({ ...data })
      }
    },

    // 指定位置の経由地を更新する
    updateWaypoint(index: number, data: TouristSpot) {
      if (index >= 0 && index < this.waypoints.length) {
        this.waypoints[index] = { ...data }
      }
    },

    // 指定したインデックスの経由地を削除する
    removeWaypoint(index: number) {
      if (index >= 0 && index < this.waypoints.length) {
        this.waypoints.splice(index, 1)
      }
    },

    // すべての経由地をクリアする
    clearAllWaypoints() {
      this.waypoints = []
    },

    // 経由地リストを設定する（リスト全体を置き換える）
    setWaypoints(waypoints: TouristSpot[]) {
      this.waypoints = waypoints.map((item) => ({ ...item }))
    }
  }
})

// 始発地
export const useOriginStore = defineStore('origin', {
  state: () => ({
    currentOriginData: null as TouristSpot | null
  }),
  getters: {
    getSelectedOrigin: (state) => {
      return state.currentOriginData
    }
  },
  actions: {
    setCurrentOriginData(data: TouristSpot) {
      this.currentOriginData = { ...data }
    },
    clearCurrentOriginData() {
      this.currentOriginData = null
    }
  }
})

// 到着地
export const useSpotStore = defineStore('tourist-facilities', {
  state: () => ({
    currentTouristData: null as TouristSpot | null
  }),
  getters: {
    getSelectedSpot: (state) => {
      return state.currentTouristData
    }
  },
  actions: {
    setCurrentTouristData(data: TouristSpot) {
      this.currentTouristData = { ...data }
    },
    clearCurrentTouristData() {
      this.currentTouristData = null
    }
  }
})

// time
export const useDateTimeStore = defineStore('dateTime', {
  state: () => ({
    selectedDate: '' as string,
    selectedTime: '' as string,
    direction: 'departure' as 'departure' | 'arrival',

    outbound: {
      date: '' as string,
      time: '' as string,
      direction: 'departure' as 'departure' | 'arrival'
    },
    return: {
      date: '' as string,
      time: '' as string,
      direction: 'departure' as 'departure' | 'arrival'
    }
  }),
  actions: {
    setDateTime(date: string, time: string) {
      this.selectedDate = date
      this.selectedTime = time
    },
    setDirection(direction: 'departure' | 'arrival') {
      this.direction = direction
    },

    setOutboundDateTime({
      date,
      time,
      direction
    }: {
      date: string
      time: string
      direction: 'departure' | 'arrival'
    }) {
      this.outbound.date = date
      this.outbound.time = time
      this.outbound.direction = direction
    },

    setReturnDateTime({
      date,
      time,
      direction
    }: {
      date: string
      time: string
      direction: 'departure' | 'arrival'
    }) {
      this.return.date = date
      this.return.time = time
      this.return.direction = direction
    },

    clearDateTime() {
      this.selectedDate = ''
      this.selectedTime = ''
      this.direction = 'departure'

      this.outbound = { date: '', time: '', direction: 'departure' }
      this.return = { date: '', time: '', direction: 'departure' }
    }
  }
})

/**
* 旅行関連のストアデータをすべてクリアします
*/
export function clearAllTouristData() {
  const appStore = useAppStore()
  const waypointStore = useWaypointStore()
  const originStore = useOriginStore()
  const spotStore = useSpotStore()
  const dateTimeStore = useDateTimeStore()

  try {
    originStore.clearCurrentOriginData()
    spotStore.clearCurrentTouristData()
    waypointStore.clearAllWaypoints()
    dateTimeStore.clearDateTime()
    appStore.setSavedTripType('oneway')
  } catch (error) {
    console.error('ストアデータの消去に失敗しました:', error)
  }
}


/**
 * 旅行関連の全データをまとめて取得する
 */
export function getAllTouristData() {
  const appStore = useAppStore()
  const waypointStore = useWaypointStore()
  const originStore = useOriginStore()
  const spotStore = useSpotStore()
  const dateTimeStore = useDateTimeStore()

  return {
    tripType: appStore.savedTripType, 
    origin: originStore.getSelectedOrigin, 
    destination: spotStore.getSelectedSpot, 
    waypoints: waypointStore.getAllWaypoints,
    waypointCount: waypointStore.getWaypointCount,
    dateTime: {
      selected: {
        date: dateTimeStore.selectedDate,
        time: dateTimeStore.selectedTime,
        direction: dateTimeStore.direction
      },
      outbound: {
        date: dateTimeStore.outbound.date,
        time: dateTimeStore.outbound.time,
        direction: dateTimeStore.outbound.direction
      },
      return: {
        date: dateTimeStore.return.date,
        time: dateTimeStore.return.time,
        direction: dateTimeStore.return.direction
      }
    }
  }
}
