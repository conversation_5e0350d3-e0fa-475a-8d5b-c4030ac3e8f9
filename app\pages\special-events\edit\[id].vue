<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { EventsSpot } from '~/types/special-events'
import { useSpecialSpotStore } from '~/stores/special-events'

const router = useRouter()
const route = useRoute()
const spotStore = useSpecialSpotStore()

definePageMeta({
  footerPlaceholderHeight: 86
})

/* イベントデータの初期値 */
const eventsData = ref<EventsSpot>({
  id: 1,
  spot_id: 1001,
  name: '東京タワー',
  description:
    '東京タワーは、東京都港区にある通信と観光を目的としたタワーです。1958年に完成し、高さ333メートルです。東京のシンボル的存在として知られ、夜間はライトアップされ美しい景観を楽しめます。',
  address: '2025年00月00日（月） ～ 2025年00月00日（日）',
  homepage: 'https://www.tokyotower.co.jp',
  images: [
    'https://picsum.photos/400/300?random=5',
    'https://picsum.photos/400/300?random=3',
    'https://picsum.photos/400/300?random=4'
  ],
  reminder: 'サンシャインホール',
  venue: '東京都渋谷区宇田川町 03-1234-5678',
  category: '観光タワー',
  prefecture: '東武日光駅',
  nearest_bus_stop_id: 501
})

/* 現在のスライド */
const currentSlide = ref<number>(0)

/* 自動スライド用タイマー */
let carouselTimer: NodeJS.Timeout | null = null

/* スライドを指定の位置に移動 */
const goToSlide = (index: number): void => {
  currentSlide.value = index
  resetTimer()
}

/* 次のスライド */
const nextSlide = (): void => {
  currentSlide.value = (currentSlide.value + 1) % eventsData.value.images.length
}

/* 自動再生を開始 */
const startAutoPlay = (): void => {
  carouselTimer = setInterval(nextSlide, 3000)
}

/* 自動再生を停止 */
const stopAutoPlay = (): void => {
  if (carouselTimer) {
    clearInterval(carouselTimer)
    carouselTimer = null
  }
}

/* タイマーをリセット */
const resetTimer = (): void => {
  stopAutoPlay()
  startAutoPlay()
}

/* 戻る操作 */
function handleBack() {
  router.go(-1)
}

/* イベントデータをstoreにセットして画面遷移 */
function contentSearch() {
  spotStore.setCurrentTouristData(eventsData.value)
  router.push({
    path: '/special-events/routeSelect'
  })
}

const touristId = ref<number | null>(null)

/* マウント時の処理 */
onMounted(() => {
  startAutoPlay()
  const id = route.params.id
  touristId.value = id ? Number(id) : null
})

/* アンマウント時の処理 */
onUnmounted(() => {
  stopAutoPlay()
})
</script>

<template>
  <div>
    <div class="regionpadding">
      <BaseHeader
        title="期間限定・イベント"
        :showBack="true"
        :showRightIcon="false"
      />
    </div>

    <div class="touristAttractionCard">
      <div class="carouselContainer">
        <div
          class="carouselWrapper"
          :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
        >
          <div
            v-for="(image, index) in eventsData.images"
            :key="index"
            class="carouselSlide"
            :style="{ backgroundImage: `url(${image})` }"
          ></div>
        </div>
      </div>

      <div class="carouselIndicators">
        <button
          v-for="(image, index) in eventsData.images"
          :key="index"
          :class="['indicator', { active: currentSlide === index }]"
          @click="goToSlide(index)"
          :style="{ backgroundImage: `url(${image})` }"
          :aria-label="`スライド ${index + 1} を表示`"
        ></button>
      </div>

      <div class="cardContent">
        <h2 class="cardTitle">{{ eventsData.name }}</h2>

        <div class="overviewSection">
          <h3 class="sectionTitle">概要</h3>
          <p class="description">
            {{ eventsData.description }}
          </p>
        </div>

        <div class="locationSection">
          <h3 class="sectionTitle">期間</h3>
          <p class="locationText">{{ eventsData.address }}</p>
        </div>

        <div class="locationSection">
          <h3 class="sectionTitle">開催場所</h3>
          <p class="locationText">{{ eventsData.reminder }}</p>
          <p class="locationText">{{ eventsData.venue }}</p>
        </div>

        <div class="homepageSection">
          <h3 class="sectionTitle">開催場所</h3>
          <a :href="eventsData.homepage" class="homepageLink">
            {{ eventsData.homepage }}
          </a>
        </div>
      </div>
    </div>

    <div class="fixedBottomContainer" @click="contentSearch">
      <div class="content">この目的地でバス検索</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.fixedBottomContainer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  height: 86px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  text-align: center;
  font-size: 16px;
  color: #fff;
  background-color: #26499d;
  width: 343px;
  height: 48px;
  line-height: 48px;
  border-radius: 4px;
}

.touristAttractionCard {
  background: white;
  border-radius: 4px;
  overflow: hidden;

  .carouselContainer {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    background-color: #f5f5f5;

    .carouselWrapper {
      display: flex;
      width: 100%;
      height: 100%;
      transition: transform 0.5s ease-in-out;

      .carouselSlide {
        flex: 0 0 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }
    }
  }

  .carouselIndicators {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    padding: 0 20px;

    .indicator {
      width: 51px;
      height: 38px;
      border: 2px solid transparent;
      background-size: cover;
      background-position: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        border-color: #26499d;
        opacity: 1;
      }

      &:not(.active) {
        opacity: 0.7;
      }

      &:hover {
        opacity: 1;
      }
    }
  }

  .cardContent {
    padding: 20px;
    line-height: 1.6;

    .cardTitle {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin: 0 0 20px 0;
      padding: 0;
    }

    .overviewSection,
    .locationSection,
    .homepageSection {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .sectionTitle {
      color: #0066cc;
      font-weight: normal;
      background-color: #e7f2fa;
      width: 88px;
      height: 22px;
      text-align: center;
      font-size: 12px;
      margin-bottom: 4px;
    }

    .description {
      font-size: 12px;
      color: #333;
      line-height: 1.5;
      margin: 0;
      text-align: justify;
    }

    .locationText {
      font-size: 13px;
      color: #333;
      margin: 0;
    }

    .homepageLink {
      font-size: 13px;
      color: #0066cc;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
