<script lang="ts" setup>
declare global {
  interface Window {
    google: any
    initMapDialog: () => void
  }
}

import { ref, computed, watch, onMounted, nextTick } from 'vue'
import StationDetailDialog from '~/components/StationDetailDialog.vue'
import { useRuntimeConfig } from 'nuxt/app'

/*
 * 型定義
 */
interface BusStop {
  busStopId: number
  busStopName: string
  busStopShortName: string
  hasToilet: boolean
  hasWifi: boolean
  mapInfo: string
  labels: string[]
  coordinates?: { lat: number; lng: number }
}

interface Area {
  areaId: number
  areaName: string
  displayOrder: number
  mapInfo: string
  busStops: BusStop[]
  coordinates?: { lat: number; lng: number }
}

interface Prefecture {
  prefectureId: number
  prefectureName: string
  displayOrder: number
  mapInfo: string
  areas: Area[]
  coordinates?: { lat: number; lng: number }
}

interface Region {
  regionId: number
  regionName: string
  displayOrder: number
  prefectures: Prefecture[]
}

type LocationType = 'prefecture' | 'area' | 'busStop'

interface MapMarker {
  id: string
  name: string
  type: LocationType
  coordinates: { lat: number; lng: number }
  googleMarker?: any
  data: Prefecture | Area | BusStop
}

// プロパティ定義
interface Props {
  modelValue: boolean
  title?: string
  coordinates?: Array<{ lat: number; lng: number; name?: string }>
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'stop-detail', data: any): void
}

const config = useRuntimeConfig()

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '地図から選択',
  coordinates: () => []
})

const emit = defineEmits<Emits>()

// リアクティブデータ
const dialogOpen = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 停留所詳細ダイアログの表示状態
const showStationDetail = ref(false)

// マップ関連のリアクティブデータ
const mapContainer = ref<HTMLElement>()
const googleMap = ref<any>(null)
const isMapLoading = ref(true)
const regions = ref<Region[]>([])
const mapMarkers = ref<MapMarker[]>([])
const selectedMarker = ref<MapMarker | null>(null)
const showMarkerModal = ref(false)

/*
 * マップ設定とズームレベル定義
 */
const MAP_CONFIG = {
  DEFAULT_ZOOM: 15,
  DEFAULT_CENTER: { lat: 35.6762, lng: 139.6503 },
  MIN_ZOOM: 5,
  MAX_ZOOM: 18,
  ZOOM_LEVELS: {
    PREFECTURE: { min: 5, max: 7 },
    AREA: { min: 8, max: 13 },
    BUS_STOP: { min: 14, max: 18 }
  }
}

/*
 * テストデータ作成
 */
const createTestData = (): Region[] => {
  return [
    {
      regionId: 1,
      regionName: '関東',
      displayOrder: 1,
      prefectures: [
        {
          prefectureId: 13,
          prefectureName: '東京都',
          displayOrder: 1,
          mapInfo:
            '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.747252088458!2d139.6981904155935!3d35.68950168019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c00a789aef7%3A0x64f2139555f9285d!2z5pel5pys5pys5Lqs6YO95pys!5e0!3m2!1sja!2sjp!4v1694512345678!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
          areas: [
            {
              areaId: 1,
              areaName: '新宿区',
              displayOrder: 1,
              mapInfo:
                '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.6997678307626!2d139.69972241559353!3d35.68948718019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c4a600699eb%3A0x3202f530161cc70!2z5Lqs6YO95pys5Y-w5Yy6!5e0!3m2!1sja!2sjp!4v1694512456789!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
              busStops: [
                {
                  busStopId: 1,
                  busStopName: '新宿駅西口',
                  busStopShortName: '新宿西口',
                  hasToilet: true,
                  hasWifi: false,
                  mapInfo:
                    '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.696658068324!2d139.69936831559353!3d35.68948558019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c4a615f2a0b%3A0x507910f8cb060f0!2z5Lqs6YO95pys5Y-w5Yy65p2x5Lqs!5e0!3m2!1sja!2sjp!4v1694512567890!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
                  labels: ['map_device']
                },
                {
                  busStopId: 2,
                  busStopName: '新宿三丁目',
                  busStopShortName: '新宿3丁目',
                  hasToilet: false,
                  hasWifi: true,
                  mapInfo:
                    '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.780423121436!2d139.7074515155935!3d35.68957288019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c4e1e0f9b0f%3A0x318e5a6e51564e1b!2z5Lqs6YO95LiW55WM!5e0!3m2!1sja!2sjp!4v1694512678901!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
                  labels: ['map']
                }
              ]
            },
            {
              areaId: 2,
              areaName: '渋谷区',
              displayOrder: 2,
              mapInfo:
                '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.157263634711!2d139.7016128155938!3d35.65857618020021!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c98bd8b6c2f%3A0xc06096bc34ed4e4b!2z5bqc5Lqs5Lqe5bed!5e0!3m2!1sja!2sjp!4v1694512789012!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
              busStops: [
                {
                  busStopId: 3,
                  busStopName: '渋谷駅',
                  busStopShortName: '渋谷',
                  hasToilet: true,
                  hasWifi: true,
                  mapInfo:
                    '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.155666649352!2d139.7014268155938!3d35.65857458020021!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c98a4e6146b%3A0x58283425193cc19c!2z5bqc5Lqs5Lqe5bed5rW3!5e0!3m2!1sja!2sjp!4v1694512890123!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
                  labels: ['map_device']
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

/*
 * mapInfo文字列から座標を解析
 */
const parseCoordinatesFromMapInfo = (
  mapInfo: string
): { lat: number; lng: number } | null => {
  if (!mapInfo) return null

  try {
    const lngMatch = mapInfo.match(/!2d([0-9.-]+)/)
    const latMatch = mapInfo.match(/!3d([0-9.-]+)/)

    if (
      lngMatch &&
      latMatch &&
      typeof lngMatch[1] === 'string' &&
      typeof latMatch[1] === 'string'
    ) {
      return {
        lng: parseFloat(lngMatch[1]),
        lat: parseFloat(latMatch[1])
      }
    }
  } catch (error) {
    console.error('Failed to parse coordinates from mapInfo:', error)
  }

  return null
}

/*
 * データからマーカーオブジェクト作成
 */
const createMarkerFromData = (
  data: Prefecture | Area | BusStop,
  type: 'prefecture' | 'area' | 'busStop'
): MapMarker | null => {
  const coords = parseCoordinatesFromMapInfo(data.mapInfo)
  if (!coords) return null

  const getId = () => {
    if (type === 'prefecture') return (data as Prefecture).prefectureId
    if (type === 'area') return (data as Area).areaId
    return (data as BusStop).busStopId
  }

  const getName = () => {
    if (type === 'prefecture') return (data as Prefecture).prefectureName
    if (type === 'area') return (data as Area).areaName
    return (data as BusStop).busStopName
  }

  return {
    id: `${type}-${getId()}`,
    name: getName(),
    type,
    coordinates: coords,
    data
  }
}

/*
 * マーカー処理とデータ読み込み
 */
function addBusStopMarkers(busStops: BusStop[], markers: MapMarker[]) {
  busStops.forEach((busStop) => {
    const busStopMarker = createMarkerFromData(busStop, 'busStop')
    if (busStopMarker) markers.push(busStopMarker)
  })
}

const processMapMarkers = (): void => {
  const markers: MapMarker[] = []

  regions.value.forEach((region) => {
    region.prefectures.forEach((prefecture) => {
      const prefectureMarker = createMarkerFromData(prefecture, 'prefecture')
      if (prefectureMarker) markers.push(prefectureMarker)

      prefecture.areas.forEach((area) => {
        const areaMarker = createMarkerFromData(area, 'area')
        if (areaMarker) markers.push(areaMarker)

        addBusStopMarkers(area.busStops, markers)
      })
    })
  })

  mapMarkers.value = markers
  updateMarkersOnMap()
}

const loadMapData = async (): Promise<void> => {
  try {
    regions.value = createTestData()
    processMapMarkers()
  } catch (error) {
    console.error('Failed to load map data:', error)
  }
}

/*
 * マーカーアイコン作成
 */
const createMarkerContent = () => {
  return {
    path: window.google.maps.SymbolPath.CIRCLE,
    fillColor: '#26499d',
    fillOpacity: 1,
    strokeColor: '#fff',
    strokeWeight: 2,
    scale: 8
  }
}

/*
 * Google Map初期化
 */
const initGoogleMap = async (): Promise<void> => {
  if (!mapContainer.value) return

  try {
    // デフォルトの中心点を計算
    const defaultCenter =
      props.coordinates.length > 0
        ? props.coordinates[0]
        : MAP_CONFIG.DEFAULT_CENTER

    const map = new window.google.maps.Map(mapContainer.value, {
      zoom: MAP_CONFIG.DEFAULT_ZOOM,
      minZoom: MAP_CONFIG.MIN_ZOOM,
      maxZoom: MAP_CONFIG.MAX_ZOOM,
      center: defaultCenter,
      mapTypeId: 'roadmap',
      gestureHandling: 'greedy', // ドラッグとズームを有効化
      zoomControl: true,
      streetViewControl: false,
      fullscreenControl: false,
      mapTypeControl: false
    })

    googleMap.value = map

    // データを読み込んでマーカーを追加
    await loadMapData()

    // propsの座標がある場合、それらも表示
    if (props.coordinates.length > 0) {
      addPropsMarkersToMap()

      // 複数の座標がある場合、すべてが見えるように調整
      if (props.coordinates.length > 1) {
        const bounds = new window.google.maps.LatLngBounds()
        props.coordinates.forEach((coord) => {
          bounds.extend(new window.google.maps.LatLng(coord.lat, coord.lng))
        })
        map.fitBounds(bounds)

        // パディングを追加してマーカーが端に寄らないようにする
        setTimeout(() => {
          map.panToBounds(bounds, { top: 20, right: 20, bottom: 20, left: 20 })
        }, 100)
      }
    }

    isMapLoading.value = false
  } catch (error) {
    console.error('Failed to initialize Google Map:', error)
    await loadMapData()
    isMapLoading.value = false
  }
}

/*
 * マーカー管理
 */
const clearAllMarkers = () => {
  mapMarkers.value.forEach((marker) => {
    if (marker.googleMarker) {
      try {
        marker.googleMarker.setMap(null)
        marker.googleMarker.setVisible(false)
        if (window.google?.maps?.event) {
          window.google.maps.event.clearInstanceListeners(marker.googleMarker)
        }
        marker.googleMarker = null
      } catch (error) {
        console.error(`Error clearing marker ${marker.name}:`, error)
      }
    }
  })
}

const updateMarkersOnMap = (): void => {
  if (googleMap.value && window.google && window.google.maps) {
    clearAllMarkers()

    setTimeout(() => {
      mapMarkers.value.forEach((marker) => {
        const googleMarker = new window.google.maps.Marker({
          position: marker.coordinates,
          map: googleMap.value,
          title: marker.name,
          icon: createMarkerContent(),
          optimized: true,
          visible: true
        })

        marker.googleMarker = googleMarker

        googleMarker.addListener('click', () => {
          handleMarkerClick(marker)
        })
      })
    }, 100)
  }
}

/*
 * propsの座標をマップに追加
 */
const addPropsMarkersToMap = () => {
  if (!googleMap.value || !window.google || !window.google.maps) return

  props.coordinates.forEach((coord, index) => {
    const colors = ['#4caf50', '#f44336', '#ff9800', '#2196f3', '#9c27b0']
    const color = colors[index % colors.length]

    const svg = `
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <circle cx="16" cy="16" r="16" fill="${color}" stroke="#ffffff" stroke-width="2"/>
        <circle cx="16" cy="16" r="6" fill="#ffffff"/>
      </svg>
    `
    const icon = {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svg),
      scaledSize: new window.google.maps.Size(32, 32),
      anchor: new window.google.maps.Point(16, 16)
    }

    const marker = new window.google.maps.Marker({
      position: { lat: coord.lat, lng: coord.lng },
      map: googleMap.value,
      title: coord.name || `地点 ${index + 1}`,
      icon: icon,
      optimized: true
    })

    // マーカークリックイベント
    marker.addListener('click', () => {
      const infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div style="padding: 8px; ">
            <h4 style="margin: 0 0 4px 0; font-size: 14px; color: #000;">${
              coord.name || `地点 ${index + 1}`
            }</h4>
            <p style="margin: 0; font-size: 12px; color: #666;">
              緯度: ${coord.lat.toFixed(6)}<br>
              経度: ${coord.lng.toFixed(6)}
            </p>
          </div>
        `
      })
      infoWindow.open(googleMap.value, marker)
    })
  })
}

/*
 * Google Maps API読み込み
 */
const loadGoogleMapsAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (window.google && window.google.maps) {
      resolve()
      return
    }

    const apiKey = config.public.googleMapsApiKey
    if (!apiKey) {
      console.warn('Google Maps API key is not configured, using fallback')
      isMapLoading.value = false
      reject(new Error('Google Maps API key is not configured'))
      return
    }

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=marker,geometry&loading=async&callback=initMapDialog`
    script.async = true
    script.defer = true

    window.initMapDialog = () => {
      resolve()
    }

    script.onerror = () => {
      console.error('Failed to load Google Maps API')
      isMapLoading.value = false
      reject(new Error('Failed to load Google Maps API'))
    }

    document.head.appendChild(script)
  })
}

// メソッド
const closeDialog = () => {
  emit('update:modelValue', false)
}

// 停留所詳細ボタンのクリック処理
const handleStopDetail = () => {
  showStationDetail.value = true
}

// 停留所詳細ダイアログを閉じる（戻るボタン）
const closeStationDetail = () => {
  showStationDetail.value = false
}

// すべてのダイアログを閉じる
const closeAllDialogs = () => {
  showStationDetail.value = false
  closeDialog()
}

/*
 * 計算プロパティ - マーカー情報表示
 */
const selectedLocationInfo = computed<string>(() => {
  if (!selectedMarker.value) return ''

  const marker = selectedMarker.value
  if (marker.type === 'busStop') {
    const busStop = marker.data as BusStop
    return `【バス停】${busStop.busStopName}`
  } else if (marker.type === 'area') {
    const area = marker.data as Area
    return `【エリア】${area.areaName}`
  } else if (marker.type === 'prefecture') {
    const prefecture = marker.data as Prefecture
    return `【都道府県】${prefecture.prefectureName}`
  }
  return ''
})

/*
 * マーカークリック処理
 */
const handleMarkerClick = (marker: MapMarker): void => {
  selectedMarker.value = marker
  showMarkerModal.value = true
}

/*
 * 停留所選択処理
 */
const handleSelectStation = (): void => {
  if (!selectedMarker.value) return

  // 選択された停留所の情報をemitで親コンポーネントに送信
  emit('stop-detail', {
    marker: selectedMarker.value,
    coordinates: selectedMarker.value.coordinates,
    name: selectedMarker.value.name,
    type: selectedMarker.value.type
  })

  showMarkerModal.value = false
  closeDialog()
}

// ダイアログが開かれた時にマップを初期化
watch(dialogOpen, async (newValue) => {
  if (newValue && !googleMap.value) {
    isMapLoading.value = true
    try {
      await nextTick() // DOMの更新を待つ
      await loadGoogleMapsAPI()
      await initGoogleMap()
    } catch (error) {
      console.error('Failed to initialize map:', error)
      isMapLoading.value = false
    }
  }
})

// 座標が変更された時にマーカーを更新
watch(
  () => props.coordinates,
  () => {
    if (googleMap.value) {
      // 既存のprops座標マーカーをクリア（データマーカーは保持）
      addPropsMarkersToMap()

      // 複数の座標がある場合、すべてが見えるように調整
      if (props.coordinates.length > 1) {
        const bounds = new window.google.maps.LatLngBounds()
        props.coordinates.forEach((coord) => {
          bounds.extend(new window.google.maps.LatLng(coord.lat, coord.lng))
        })
        googleMap.value.fitBounds(bounds)
      } else if (props.coordinates.length === 1) {
        googleMap.value.setCenter(props.coordinates[0])
        googleMap.value.setZoom(MAP_CONFIG.DEFAULT_ZOOM)
      }
    }
  },
  { deep: true }
)

onMounted(() => {
  // コンポーネントマウント時は何もしない（ダイアログが開かれた時に初期化）
})
</script>
<template>
  <v-dialog
    v-model="dialogOpen"
    max-width="343"
    persistent
    class="map-selection-dialog"
  >
    <v-card class="map-dialog-card">
      <div class="map-dialog-content">
        <!-- ヘッダー -->
        <div class="map-dialog-header">
          <div class="map-header-title">{{ title }}</div>
          <v-btn
            variant="outlined"
            size="small"
            class="map-detail-button"
            @click="handleStopDetail"
          >
            停留所の詳細
          </v-btn>
        </div>

        <!-- 地図エリア -->
        <div class="map-area">
          <div
            ref="mapContainer"
            class="google-map-container"
            style="width: 100%; height: 412px"
          >
            <div class="map-instruction">
              地図を操作して、地点を選択してください。
            </div>
          </div>

          <!-- マップローディング状態 -->
          <div v-if="isMapLoading" class="map-loading-overlay">
            <v-progress-circular
              indeterminate
              color="primary"
              size="64"
              class="loading-spinner"
            ></v-progress-circular>
            <p class="loading-text">地図を読み込み中...</p>
          </div>
        </div>

        <!-- フッター：閉じるボタン -->
        <div class="map-dialog-footer">
          <v-btn
            variant="outlined"
            class="map-close-button"
            block
            @click="closeDialog"
          >
            閉じる
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>

  <!-- 停留所詳細ダイアログ -->
  <StationDetailDialog
    v-model="showStationDetail"
    :station-name="title"
    @go-back="closeStationDetail"
    @close-all="closeAllDialogs"
  />

  <!-- マーカー詳細モーダル -->
  <v-dialog v-model="showMarkerModal" max-width="400" class="marker-modal">
    <v-card v-if="selectedMarker" class="marker-card">
      <v-card-title class="modal-header">
        <v-chip rounded variant="outlined" size="small" class="type-tag">
          {{
            selectedMarker.type === 'busStop'
              ? '停留所'
              : selectedMarker.type === 'area'
              ? 'エリア'
              : '都道府県'
          }}
        </v-chip>

        <v-btn
          icon
          variant="text"
          size="small"
          @click="showMarkerModal = false"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text class="modal-content">
        <div class="location-info">
          <h4 class="location-name">{{ selectedMarker.name }}</h4>
          <p v-if="selectedLocationInfo" class="location-detail">
            {{ selectedLocationInfo }}
          </p>
        </div>

        <div class="action-buttons">
          <v-btn
            color="primary"
            rounded="false"
            class="action-button"
            @click="handleSelectStation"
          >
            この停留所を選択
          </v-btn>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<style scoped lang="scss">
.map-selection-dialog {
  z-index: 1001;
}

.map-dialog-card {
  border-radius: 10px !important;
  background-color: #ffffff !important;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.map-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 580px;
}

/* ヘッダー */
.map-dialog-header {
  width: 100%;
  position: relative;
}

.map-header-title {
  color: #26499d;
  font-size: 20px;
  font-weight: 400;
  margin: 0;
}

.map-detail-button {
  position: absolute;
  right: 0;
  top: -4px;
  width: auto !important;
  height: 32px !important;
  background-color: #ffffff !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  color: #000000 !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 6px 16px !important;
  min-width: auto !important;
}

.map-detail-button:hover {
  background-color: #f5f5f5 !important;
}

/* 地図エリア */
.map-area {
  width: 311px;
  height: 412px;
  background-color: #f0f0f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.google-map-container {
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.map-instruction {
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  color: #3b3b3b;
  font-size: 12px;
  line-height: 20px;
  font-weight: 400;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 12px;
  border-radius: 4px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(178, 209, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  border-radius: 8px;
}

.loading-text {
  margin-top: 16px;
  color: #26499d;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.loading-spinner {
  width: 64px !important;
  height: 64px !important;
}

/* フッター */
.map-dialog-footer {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  width: 311px;
  position: absolute;
  bottom: 24px;
  left: 16px;
}

.map-close-button {
  width: 311px !important;
  height: 48px !important;
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  color: #26499d !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.map-close-button:hover {
  background-color: #d6e9f5 !important;
}

/* Vuetifyのデフォルトスタイルをオーバーライド */
:deep(.v-dialog > .v-overlay__content) {
  margin: 0 !important;
}

:deep(.v-card) {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

:deep(.v-btn) {
  text-transform: none !important;
}

:deep(.v-btn--variant-outlined) {
  border-width: 1px !important;
}

/* アクセシビリティとレスポンシブ対応 */
@media (max-width: 375px) {
  .map-dialog-content {
    padding: 12px;
  }
}

.google-map-container:focus-within {
  outline: 2px solid #26499d;
  outline-offset: 2px;
}

@media (prefers-contrast) {
  .map-dialog-card {
    border-width: 2px;
  }

  .map-close-button {
    border: 2px solid #9cbcd4;
  }
}

@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
}

/* マーカー詳細モーダル */
.marker-modal :deep(.v-dialog) {
  margin: 16px;
}

.marker-card {
  border-radius: 10px;
  border: 1px solid #9cbcd4;
  overflow: hidden;
  background: #ffffff;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 11px 14px;
  border-bottom: none;
}

.type-tag {
  background: #ffffff;
  border: 1px solid #7d7d7d;
  padding: 5px 10px;
  font-size: 12px;
}

.modal-content {
  padding: 0 14px 11px 14px;
}

.location-info {
  margin-bottom: 9px;
}

.location-name {
  color: #000000;
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.location-detail {
  color: #7d7d7d;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  background: #26499d !important;
  height: 49px;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff !important;
}

.action-button:hover {
  background: #1e3d8a !important;
}

@media (prefers-contrast) {
  .marker-card {
    border-width: 2px;
  }

  .action-button {
    border: 2px solid #ffffff;
  }
}
</style>
