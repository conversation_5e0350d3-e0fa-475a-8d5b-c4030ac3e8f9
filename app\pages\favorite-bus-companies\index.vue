<script lang="ts" setup>
definePageMeta({
  hideFooter: true,
  footerPlaceholderHeight: 86
})

import { ref } from 'vue'

/* お気に入りバス会社データの定義 */
const busCompanies = ref([
  {
    id: 1,
    name: 'バス会社A'
  },
  {
    id: 2,
    name: 'バス会社B'
  },
  {
    id: 3,
    name: 'バス会社C'
  },
  {
    id: 4,
    name: 'バス会社D'
  },
  {
    id: 5,
    name: 'バス会社E'
  },
  {
    id: 6,
    name: 'バス会社F'
  },
  {
    id: 7,
    name: 'バス会社G'
  },
  {
    id: 8,
    name: 'バス会社H'
  },
  {
    id: 9,
    name: 'バス会社I'
  }
])

/* 編集モードと選択された会社の管理 */
const isEditMode = ref(false)
const selectedCompanies = ref<number[]>([])

/* 戻るボタンの処理 */
const handleBack = () => {
  if (isEditMode.value) {
    isEditMode.value = false
    selectedCompanies.value = []
  } else {
    window.history.back()
  }
}

/* 編集モードの切り替え */
const toggleEditMode = () => {
  isEditMode.value = !isEditMode.value
}

/* 新しいバス会社を追加するページへ遷移 */
const addCompany = async () => {
  await navigateTo({
    path: '/favorite-bus-companies/detail/search'
  })
}

/* 選択されたバス会社を削除 */
const deleteSelected = () => {
  busCompanies.value = busCompanies.value.filter(
    (c) => !selectedCompanies.value.includes(c.id)
  )
  selectedCompanies.value = []
  isEditMode.value = false
}

/* バス会社の詳細ページへ遷移 */
const getCompanyDetail = async (id: number) => {
  if (!isEditMode.value) {
    await navigateTo({
      path: '/favorite-bus-companies/detail/' + id
    })
  }
}
</script>

<template>
  <div class="favorite-bus-companies">
    <div class="header">
      <v-icon color="#26499d" size="30px" class="pl-4" @click="handleBack">
        mdi-chevron-left
      </v-icon>

      <h3 class="title">お気に入りバス会社</h3>
      <span class="edit-btn" @click="toggleEditMode">
        {{ isEditMode ? '' : '編集' }}
      </span>
    </div>

    <div v-if="isEditMode" class="delete-hint">
      削除したい項目を選択してください
    </div>

    <v-list>
      <v-list-item
        v-for="company in busCompanies"
        :key="company.id"
        class="company-item"
        @click="getCompanyDetail(company.id)"
        :class="{
          'selected-item': isEditMode && selectedCompanies.includes(company.id)
        }"
      >
        <template v-if="isEditMode" #prepend>
          <v-checkbox
            v-model="selectedCompanies"
            :value="company.id"
            hide-details
            density="compact"
            class="small-checkbox"
          />
        </template>
        <v-list-item-title>{{ company.name }}</v-list-item-title>
        <template v-if="!isEditMode" #append>
          <v-icon color="#26499D">mdi-chevron-right</v-icon>
        </template>
      </v-list-item>
    </v-list>

    <div class="bottom-btn" v-if="isEditMode && selectedCompanies.length > 0">
      <v-col>
        <v-btn
          color="#ed785f"
          block
          dark
          @click="deleteSelected"
          rounded="xs"
          height="43px"
        >
          選択項目を削除
        </v-btn>
      </v-col>
    </div>
    <div class="bottom-btn" v-if="!isEditMode">
      <v-col>
        <v-btn
          color="#26499D"
          block
          dark
          @click="addCompany"
          rounded="xs"
          height="43px"
        >
          <v-icon start>mdi-plus</v-icon>
          お気に入りのバス会社を追加
        </v-btn>
      </v-col>
    </div>
  </div>
</template>

<style scoped>
.favorite-bus-companies {
  display: flex;
  flex-direction: column;
  background: #fff;
}

.header {
  position: relative;
  display: flex;
  align-items: center;
  height: 48px;
  justify-content: space-between;
}

.back-btn {
  background: none;
  border: none;
  padding: 0 12px;
  display: flex;
  align-items: center;
}

.title {
  flex: 1;
  text-align: center;
  color: #26499d;
  font-size: 19px;
  font-weight: 400;
  margin: 0;
}

.edit-btn {
  right: 12px;
  color: #26499d;
  font-size: 14px;
  cursor: pointer;
  padding: 0 20px;
}

.delete-hint {
  color: #ed785f;
  font-size: 14px;
  padding: 8px 16px 0px 16px;
}

.v-list-item {
  border-bottom: 1px solid #e0e0e0;
}

.v-list-item:first-child {
  border-top: 1px solid #e0e0e0;
}

.selected-item {
  background-color: #e7f2fa;
}

.bottom-btn {
  position: fixed;
  background: #fff;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  height: 86px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bottom-btn .v-btn {
  font-weight: normal !important;
}

.v-list-item--density-default.v-list-item--one-line {
  min-height: 45px;
}

.v-list-item-title {
  font-size: 14px;
}

.v-input--density-default {
  --v-input-control-height: 40px;
}

.small-checkbox {
  transform: scale(0.8);
  transform-origin: center left;
}

.small-checkbox .v-selection-control__input .v-icon {
  font-size: 16px !important;
}

:deep(
    .small-checkbox .v-selection-control__input .v-icon:not(.v-icon--checked)
  ) {
  color: #26499d !important;
}

:deep(.small-checkbox .v-selection-control__input .v-icon.v-icon--checked) {
  color: #26499d !important;
}
</style>
