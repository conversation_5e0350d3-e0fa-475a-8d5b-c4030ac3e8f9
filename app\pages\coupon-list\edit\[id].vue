<script lang="ts" setup>
import { ref } from 'vue'

const coupon = ref({
  title: '【乗り継ぎ応援】10％OFFクーポン',
  validUntil: '2025年12月24日（水）まで有効',
  usageLimit: '1ユーザーにつき10回まで利用可能',
  content:
    '対象の乗り継ぎ便に限り、運賃の10％を割引説明明説明説明説明説明説明説明説明説明説明説明',
  conditions:
    '対象便は「東京駅→名古屋駅→京都駅」など、2区間以上の乗り継ぎ便に限る。対象の乗り継ぎ便に限り、運賃の10％明'
})

const handleBack = async () => {
  await navigateTo({
    path: `/coupon-list`
  })
}
</script>

<template>
  <div class="coupon-detail">
    <div class="couponHeaderTop createPadding">
      <v-icon color="#26499d" size="30px" class="pl-1" @click="handleBack">
        mdi-chevron-left
      </v-icon>
      <h3 class="couponSelectorTitle">クーポン詳細</h3>
    </div>
    <v-card class="coupon-card">
      <v-card-text>
        <div class="coupon-title">
          <span class="title">{{ coupon.title }}</span>
        </div>

        <div class="valid-period">
          <span class="label">有効期限</span>
          <span class="value">{{ coupon.validUntil }}</span>
        </div>

        <div class="usage-limit">
          <span class="label">利用可能回数</span>
          <span class="value">{{ coupon.usageLimit }}</span>
        </div>

        <v-row class="divider-row"></v-row>

        <div class="coupon-content">
          <v-row>
            <v-col class="mb-0 pb-0">
              <span class="label">クーポン内容</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" class="pa-3">
              <span class="value">{{ coupon.content }}</span>
            </v-col>
          </v-row>
        </div>

        <div class="usage-conditions min-h-172">
          <v-row>
            <v-col class="mb-0 pb-0">
              <span class="label">利用条件</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" class="pa-3">
              <span class="value">{{ coupon.conditions }}</span>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<style scoped>
.coupon-detail {
  color: #000000;
}

.couponHeaderTop {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 48px;
}

.couponSelectorBackBtn {
  background: none;
  border: none;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.couponSelectorTitle {
  position: absolute;
  color: #26499d;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20px;
  font-weight: 400;
}

.coupon-title {
  font-size: 17px;
  font-weight: 500;
  margin-bottom: 25px;
}

.v-card {
  border-radius: 0px !important;
  padding: 10px;
  border: 0 !important;
  box-shadow: unset;
}

.v-card-text {
  padding: 0.5rem;
}

.valid-period,
.usage-limit,
.coupon-content,
.usage-conditions {
  margin-bottom: 10px;
}

.min-h-172 {
  min-height: 172px;
}

.valid-period,
.usage-limit {
  justify-content: space-between;
  display: flex;
}

.label {
  font-size: 14px;
  font-weight: bold;
}

.value {
  font-size: 13px;
  color: #000000;
  display: block;
  margin-top: 4px;
}

.divider-row {
  height: 1px;
  background-color: #e0e0e0; /* Light gray line */
  margin: 35px 0; /* Adding some space around the line */
}
</style>
