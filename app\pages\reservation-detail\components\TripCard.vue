<script lang="ts" setup>
// インターフェース定義
interface Passenger {
  type: string
  detail: string
}

interface TripInfo {
  label: string
  value: string
}

interface Amenity {
  name: string
  icon?: string
  color?: string
  type?: string
}

interface TripData {
  date: string
  departure: string
  arrival: string
  departureTime: string
  arrivalTime: string
  departureStation: string
  arrivalStation: string
  duration: string
  seatNumbers: string
  passengers: Passenger[]
  tripInfo: TripInfo[]
  amenities: Amenity[]
}

interface Props {
  tripNumber: string
  tripData: TripData
  isNew?: boolean
  initialExpanded?: boolean
  status?: string
  id?: string
}

// プロパティ定義
const props = withDefaults(defineProps<Props>(), {
  isNew: false,
  initialExpanded: true,
  status: '決済待ち',
  id: undefined
})

// エミット定義
const emit = defineEmits<{
  'cancel-trip': []
  'change-trip': []
  'show-ticket': []
  'show-station': [stationName: string]
}>()

// リアクティブデータ
const isExpanded = ref(props.initialExpanded)
const isAmenitiesExpanded = ref(false)

// 表示する設備情報を計算
const displayedAmenities = computed(() => {
  if (isAmenitiesExpanded.value || props.tripData.amenities.length <= 6) {
    return props.tripData.amenities
  }
  return props.tripData.amenities.slice(0, 6)
})

// ステータスに基づく背景色クラスを計算
const headerBackgroundClass = computed(() => {
  return props.status === '乗車済' || props.status === 'キャンセル済'
    ? 'header-completed'
    : 'header-default'
})

const toggleAmenities = () => {
  isAmenitiesExpanded.value = !isAmenitiesExpanded.value
}
</script>
<template>
  <div class="trip-card" :id="props.id">
    <!-- カードヘッダー -->
    <div class="card-header" :class="headerBackgroundClass">
      <div class="icon-title">
        <!-- 決済済・決済待ちの場合はバスアイコン -->
        <v-icon
          v-if="status === '決済済' || status === '決済待ち'"
          color="primary"
          size="24"
          class="bus-icon"
        >
          mdi-bus
        </v-icon>

        <!-- 乗車済の場合は乗車済バッジ -->
        <div
          v-else-if="status === '乗車済'"
          class="status-badge completed-badge"
        >
          乗車済
        </div>

        <!-- キャンセル済の場合はキャンセル済バッジ -->
        <div
          v-else-if="status === 'キャンセル済'"
          class="status-badge cancelled-badge"
        >
          キャンセル済
        </div>
      </div>
      <h3 class="trip-number">{{ tripNumber }}</h3>
    </div>

    <!-- カード内容 -->
    <div v-if="isExpanded" class="card-content">
      <!-- 基本情報 -->
      <div class="trip-basic-info">
        <div class="trip-header">
          <div class="date-info">
            <div class="date-boarding">
              <span class="date">{{ tripData.date }}</span>
              <div class="boarding-frame">
                <span class="boarding-label">乗車</span>
              </div>
            </div>
          </div>
          <div class="route-info">
            <span class="departure">{{ tripData.departure }}</span>
            <v-icon color="info" size="20">mdi-arrow-right</v-icon>
            <span class="arrival">{{ tripData.arrival }}</span>
          </div>
        </div>

        <div class="divider"></div>

        <!-- 時刻・駅情報 -->
        <div class="time-station-info">
          <div class="time-line">
            <div class="time-dots">
              <div class="dot start-dot"></div>
              <div class="connecting-line"></div>
              <div class="dot end-dot"></div>
            </div>
            <div class="time-details">
              <div class="time-station">
                <span class="time">{{ tripData.departureTime }}</span>
                <v-btn
                  variant="text"
                  color="primary"
                  size="small"
                  class="station-link"
                  @click="emit('show-station', tripData.departureStation)"
                >
                  {{ tripData.departureStation }}
                </v-btn>
              </div>
              <div class="time-station">
                <span class="time">{{ tripData.arrivalTime }}</span>
                <v-btn
                  variant="text"
                  color="primary"
                  size="small"
                  class="station-link"
                  @click="emit('show-station', tripData.arrivalStation)"
                >
                  {{ tripData.arrivalStation }}
                </v-btn>
              </div>
            </div>
          </div>
          <v-chip
            color="info"
            variant="tonal"
            size="small"
            class="duration-chip"
          >
            {{ tripData.duration }}
          </v-chip>
        </div>
      </div>

      <!-- 座席番号 -->
      <div class="info-section">
        <div class="section-header" :class="headerBackgroundClass">
          <span class="info-section-title">座席番号</span>
        </div>
        <div class="section-content">
          <span class="seat-numbers">{{ tripData.seatNumbers }}</span>
        </div>
      </div>

      <!-- 乗車人数 -->
      <div class="info-section">
        <div class="section-header" :class="headerBackgroundClass">
          <span class="info-section-title">乗車人数</span>
        </div>
        <div class="section-content">
          <div class="passenger-list">
            <div
              v-for="passenger in tripData.passengers"
              :key="passenger.type"
              class="passenger-item"
            >
              <span class="trip-passenger-type">{{ passenger.type }}</span>
              <span class="passenger-detail">{{ passenger.detail }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 運行情報 -->
      <div class="info-section">
        <div class="section-header" :class="headerBackgroundClass">
          <span class="info-section-title">運行情報</span>
        </div>
        <div class="section-content">
          <div class="trip-info-list">
            <div
              v-for="info in tripData.tripInfo"
              :key="info.label"
              class="trip-info-item"
            >
              <span class="trip-info-label">{{ info.label }}</span>
              <span class="trip-info-value">{{ info.value }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 便情報詳細 -->
      <div class="info-section">
        <div class="section-header" :class="headerBackgroundClass">
          <span class="info-section-title">便情報詳細</span>
        </div>
        <div class="section-content">
          <div class="amenities-container">
            <div class="amenities-list">
              <v-chip
                v-for="amenity in displayedAmenities"
                :key="amenity.name"
                :color="amenity.color || 'default'"
                :prepend-icon="amenity.icon"
                variant="outlined"
                size="small"
                class="amenity-chip"
              >
                {{ amenity.name }}
              </v-chip>
            </div>
            <v-btn
              v-if="tripData.amenities.length > 6"
              variant="text"
              color="primary"
              size="small"
              class="see-all-btn"
              @click="toggleAmenities"
            >
              {{ isAmenitiesExpanded ? '閉じる' : '…すべて見る' }}
            </v-btn>
          </div>
        </div>
      </div>

      <!-- アクションボタン -->
      <div class="action-buttons">
        <v-btn
          color="error"
          variant="elevated"
          size="large"
          rounded="10"
          class="cancel-btn"
          @click="emit('cancel-trip')"
        >
          この便をキャンセル
        </v-btn>
        <v-btn
          color="primary"
          variant="outlined"
          size="large"
          rounded="10"
          class="change-btn"
          @click="emit('change-trip')"
        >
          予約変更
        </v-btn>
      </div>
      <!-- 乗車券表示ボタン -->
      <div v-if="status === '決済済' || status === '乗車済'" class="ticket-btn">
        <v-btn
          color="primary"
          variant="elevated"
          size="large"
          block
          class="ticket-display-btn"
          @click="emit('show-ticket')"
        >
          <v-icon left size="20" class="ticket-icon">mdi-ticket</v-icon>
          乗車券を表示
        </v-btn>
      </div>
    </div>
  </div>
</template>
<style scoped>
.trip-card {
  display: flex;
  flex-direction: column;
  width: 343px;
}

.card-header {
  display: flex;
  align-items: center;
  width: 343px;
  height: 50px;
  border: 1px solid #9cbcd4;
  border-radius: 10px 10px 0 0;
  padding: 0 16px;
  position: relative;
}

.header-default {
  background-color: #e7f2fa;
}

.header-completed {
  background-color: #dfdfdf;
  border: 1px solid #7d7d7d !important;
  color: #000000;
}

.icon-title {
  display: flex;
  align-items: center;
  margin-right: 17px;
}

.status-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  min-width: 80px;
  height: 28px;
}

.completed-badge {
  background-color: #e7f2fa;
  color: #26499d;
  border: 1px solid #26499d;
}

.cancelled-badge {
  background-color: #ffdfdf;
  color: #d00000;
  border: 1px solid #d00000;
}

.trip-number {
  flex: 1;
  font-size: 20px;
  font-weight: 400;
  color: #000000;
  margin: 0;
}

.expand-btn {
  position: absolute;
  right: 16px;
  top: 3px;
}

.card-content {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-left: 1px solid #9cbcd4;
  border-right: 1px solid #9cbcd4;
  border-bottom: 1px solid #9cbcd4;
  border-radius: 0 0 10px 10px;
}

.trip-basic-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #9cbcd4;
}

.trip-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-boarding {
  display: flex;
  gap: 4px;
}

.date {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.boarding-frame {
  display: flex;
  align-items: center;
  gap: 10px;
}

.boarding-label {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.route-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.departure,
.arrival {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.divider {
  width: 311px;
  height: 1px;
  background-color: #d9d9d9;
  margin: 12px 0;
}

.time-line {
  display: flex;
  gap: 8px;
}

.time-dots {
  position: relative;
  width: 12px;
  height: 51px;
}

.dot {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: white;
  border: 2px solid #9cbcd4;
  border-radius: 50%;
}

.start-dot {
  top: 0;
}

.end-dot {
  bottom: 0;
}

.connecting-line {
  position: absolute;
  left: 5px;
  top: 11px;
  width: 0;
  height: 34px;
  border-left: 2px solid #9cbcd4;
}

.time-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 180px;
}

.time-station {
  display: flex;
  align-items: center;
  gap: 20px;
}

.time {
  font-size: 22px;
  font-weight: 700;
  color: #000000;
  line-height: 1.36;
}

.station-link {
  font-size: 14px;
  font-weight: 500;
  text-decoration: underline;
}

.duration-chip {
  background-color: #e7f2fa !important;
  color: #26499d !important;
  padding: 2px 8px;
  align-self: flex-start;
  font-size: 12px;
  font-weight: 500;
  border-radius: 10px !important;
  margin-top: 5px;
}

.info-section {
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 10px;
  border-bottom: 1px solid #9cbcd4;
}

/* セクションヘッダーの背景色は共通クラスを使用 */
.section-header.header-default {
  background-color: #e7f2fa;
}

.section-header.header-completed {
  background-color: #dfdfdf;
  border: 1px solid #7d7d7d !important;
}
.section-header.header-completed .info-section-title {
  color: #000000 !important;
}
.info-section-title {
  font-size: 14px;
  color: #26499d;
  font-weight: 400;
}

.section-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  background-color: white;
}

.seat-numbers {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.passenger-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.passenger-item {
  display: flex;
  gap: 8px;
}

.trip-passenger-type {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.passenger-detail {
  font-size: 16px;
  color: #000000;
}

.trip-info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trip-info-item {
  display: flex;
  gap: 8px;
}

.trip-info-label {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.trip-info-value {
  font-size: 16px;
  color: #000000;
}

.amenities-container {
  position: relative;
  width: 311px;
  min-height: 60px;
  padding-bottom: 20px;
}

.amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 311px;
  align-items: center;
  padding-right: 80px;
}

.amenity-chip {
  border: 1px solid #9cbcd4 !important;
  border-radius: 26px !important;
  padding: 5px 10px;
  font-size: 14px;
}

.see-all-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 14px !important;
  color: #26499d !important;
  text-decoration: underline;
  cursor: pointer;
  user-select: none;
  min-width: auto !important;
  height: auto !important;
  padding: 0 !important;
}

.action-buttons {
  display: flex;
  gap: 16px;
  padding: 16px;
  background-color: white;
  border-radius: 0 0 10px 10px;
  width: 311px;
}

.cancel-btn {
  height: 48px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  background-color: #ed785f !important;
}

.change-btn {
  flex: 1;
  height: 48px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  border: 1px solid #9cbcd4 !important;
  background-color: #e7f2fa !important;
  color: #26499d !important;
}

.ticket-btn {
  padding: 16px;
  background-color: white;
  border-radius: 0 0 10px 10px;
}

.ticket-display-btn {
  height: 56px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 400;
  background-color: #26499d !important;
  color: #ffffff !important;
  text-transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ticket-icon {
  margin-right: 8px;
}
</style>
