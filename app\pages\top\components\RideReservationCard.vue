<script lang="ts" setup>
import { ref, nextTick, computed } from 'vue'
import { useDisplay } from 'vuetify'
import { useRouter } from 'vue-router'

const router = useRouter()
const { mobile } = useDisplay()

/* 旅程データのインターフェース */
interface Trip {
  id: number
  start: string
  end: string
  next: string
  final: string
  tripHeader: string
  startDate: string
  endDate: string
  startTime: string
  endTime: string
  startLocation: string
  endLocation: string
  isRouteExpanded?: boolean
  isContentVisible?: boolean
  showReservationData?: boolean
  [key: string]: any
}

/* チケットデータのインターフェース */
interface Ticket {
  id: string
  arr: Trip[]
  [key: string]: any
}

const props = defineProps<{
  tickets: Ticket[]
}>()

/* リアクティブな状態管理 */
const activeTripIndexes = ref<number[]>(props.tickets.map(() => 0))
const currentPage = ref<number>(1)
const reservationData = ref<string>('03/22(土) 21:00発～03/22(土) 10:00着')

// グローバル展開状態（Mobile版用）
const isAllExpanded = ref<boolean>(true)

// PC版用の状態管理
const ticketPages = ref<Record<string, number>>({})
const activeTicketId = ref<string>(props.tickets[0]?.id || '')

/* 初期化処理: 各チケットのページを1に設定 */
props.tickets.forEach((ticket) => {
  ticketPages.value[ticket.id] = 1
})

/*
 * 計算プロパティ
 * ticketIds: 全チケットのIDリスト
 * activeIndex: 現在アクティブなチケットのインデックス
 */
const ticketIds = computed<string[]>(() => {
  return props.tickets.map((ticket) => ticket.id)
})

const activeIndex = computed<number>(() => {
  return ticketIds.value.indexOf(activeTicketId.value)
})

/* 表示する旅程データを取得（PC版用：現在のページに応じて2件ずつ表示） */
const getVisibleTrips = (ticket: Ticket): Trip[] => {
  const currentPage = ticketPages.value[ticket.id] || 1
  const startIndex = (currentPage - 1) * 2
  let endIndex = startIndex + 2

  if (currentPage === getTotalPages(ticket) && ticket.arr.length % 2 !== 0) {
    endIndex = startIndex + 1
  }

  return ticket.arr.slice(startIndex, endIndex)
}

/* 総ページ数を計算（PC版用） */
const getTotalPages = (ticket: Ticket): number => {
  return Math.ceil(ticket.arr.length / 2)
}

/* 汎用スクロール処理 */
const scrollTo = async (selector: string, targetIndex: number) => {
  await nextTick()
  const scrollContainer = document.querySelector<HTMLDivElement>(selector)
  const targetCard = scrollContainer?.children[targetIndex] as
    | HTMLElement
    | undefined
  if (targetCard && scrollContainer) {
    scrollContainer.scrollTo({
      left: targetCard.offsetLeft - 16,
      behavior: 'smooth'
    })
  }
}

/* Mobile版ページ切り替え処理 */
const switchPage = async (page: number) => {
  currentPage.value = page
  await scrollTo('.ticket-scroll-container', page - 1)
}

/* PC版ページ切り替え処理 */
const switchTicketPage = async (ticket: Ticket, pageNum: number) => {
  ticketPages.value[ticket.id] = pageNum
  await scrollTo(`.ticketScrollContainer-${ticket.id}`, 0)
}

/* チケット切り替え処理（PC版用） */
const switchToTicket = (ticketId: string) => {
  activeTicketId.value = ticketId

  nextTick(() => {
    const index = ticketIds.value.indexOf(ticketId)
    const activeElement = document.querySelector(
      `.ticketBox:nth-child(${index + 1})`
    )
    if (activeElement) {
      activeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      })
    }
  })
}

/* 前後のページに移動（Mobile版） */
const nextPage = () => {
  if (currentPage.value < props.tickets.length) {
    switchPage(currentPage.value + 1)
  } else {
    switchPage(1)
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    switchPage(currentPage.value - 1)
  } else {
    switchPage(props.tickets.length)
  }
}

/* 前後のチケットに移動（PC版） */
const nextTicket = () => {
  const currentIndex = activeIndex.value
  const nextIndex =
    currentIndex === ticketIds.value.length - 1 ? 0 : currentIndex + 1
  switchToTicket(ticketIds.value[nextIndex]!)
}

const prevTicket = () => {
  const currentIndex = activeIndex.value
  const prevIndex =
    currentIndex === 0 ? ticketIds.value.length - 1 : currentIndex - 1
  switchToTicket(ticketIds.value[prevIndex]!)
}

/* 次の旅程ページに移動（PC版） */
const nextTicketPage = (ticket: Ticket) => {
  const currentPage = ticketPages.value[ticket.id] || 1
  if (currentPage < getTotalPages(ticket)) {
    switchTicketPage(ticket, currentPage + 1)
  } else {
    switchTicketPage(ticket, 1)
  }
}

/* 前の旅程ページに移動（PC版） */
const prevTicketPage = (ticket: Ticket) => {
  const currentPage = ticketPages.value[ticket.id] || 1
  if (currentPage > 1) {
    switchTicketPage(ticket, currentPage - 1)
  } else {
    switchTicketPage(ticket, getTotalPages(ticket))
  }
}

/* 旅程の展開/折りたたみ */
const toggleRouteExpand = (trip: Trip | undefined) => {
  if (trip) {
    trip.isRouteExpanded = !trip.isRouteExpanded
    trip.isContentVisible = !trip.isContentVisible
  }
}

/* 全カードの展開/折りたたみ（Mobile版用） */
const toggleAllExpanded = () => {
  isAllExpanded.value = !isAllExpanded.value

  // 全tripの展開状態を更新
  props.tickets.forEach((ticket) => {
    ticket.arr.forEach((trip) => {
      trip.isRouteExpanded = isAllExpanded.value
      trip.isContentVisible = isAllExpanded.value
    })
  })
}

/* 乗車券表示 */
const showTicket = (trip: Trip | undefined, reservationId?: string) => {
  if (reservationId) {
    console.log(`乗車券表示: ${trip?.tripHeader}, 予約番号: ${reservationId}`)
  }
  router.push('/ticket/qr')
}

/* 予約変更処理 */
const confirmOrChangeReservation = (trip?: Trip, reservationId?: string) => {
  if (reservationId) {
    activeTicketId.value = reservationId
    console.log(`予約変更: ${trip?.tripHeader}, 予約番号: ${reservationId}`)
  }
  router.push('/appointment/confirmation')
}

/* 前後の旅程に移動（Mobile版） */
const showNextTrip = (resIndex: number) => {
  const reservation = props.tickets[resIndex]
  if (!reservation || !reservation.arr) return

  const arrLength = reservation.arr.length
  if (
    activeTripIndexes.value &&
    typeof activeTripIndexes.value[resIndex] === 'number'
  ) {
    activeTripIndexes.value[resIndex] =
      (activeTripIndexes.value[resIndex] + 1) % arrLength
  }

  if (currentPage.value !== resIndex + 1) {
    currentPage.value = resIndex + 1
    scrollTo('.ticket-scroll-container', resIndex)
  }
}

const showPrevTrip = (resIndex: number) => {
  const reservation = props.tickets[resIndex]
  if (!reservation || !reservation.arr) return

  if (
    activeTripIndexes.value &&
    typeof activeTripIndexes.value[resIndex] === 'number'
  ) {
    activeTripIndexes.value[resIndex] =
      (activeTripIndexes.value[resIndex] - 1 + reservation.arr.length) %
      reservation.arr.length
  }
}
</script>

<template>
  <!-- モバイル表示 -->
  <div class="ticket-container" v-show="tickets.length && mobile">
    <h2 class="section-title">直近の乗車予定</h2>
    <div class="title-underline"></div>

    <div
      class="ticket-scroll-container"
      :class="{ 'center-container': tickets.length === 1 }"
    >
      <div
        class="ticket-card"
        v-for="(reservation, resIndex) in tickets"
        :key="reservation.id"
      >
        <div v-if="reservation.arr.length" class="trip-item-wrapper">
          <template
            v-if="
              typeof activeTripIndexes[resIndex] === 'number' &&
              reservation.arr[activeTripIndexes[resIndex]]
            "
          >
            <!-- 旅程ルート表示 -->
            <div class="route">
              <span>
                {{ reservation.arr[activeTripIndexes[resIndex]]?.start }}
              </span>
              <img
                src="~/assets/image/triangle.png"
                class="trigicon"
                alt="経路アイコン"
              />
              <span>
                {{ reservation.arr[activeTripIndexes[resIndex]]?.end }}
              </span>
              <img
                src="~/assets/image/triangle.png"
                class="trigicon"
                alt="経路アイコン"
              />
              <span>
                {{ reservation.arr[activeTripIndexes[resIndex]]?.next }}
              </span>
              <img
                src="~/assets/image/triangle.png"
                class="trigicon"
                alt="経路アイコン"
              />
              <span>
                {{ reservation.arr[activeTripIndexes[resIndex]]?.final }}
              </span>
              <button class="route-expand-btn" @click="toggleAllExpanded">
                <img
                  src="~/assets/image/Icon.png"
                  class="chevron-icon"
                  alt="展開アイコン"
                  :class="{
                    'rotate-180': isAllExpanded
                  }"
                />
              </button>
            </div>

            <!-- 予約情報表示 -->
            <div>
              <div class="reservation-number">
                予約番号：{{ reservation.id }}
              </div>
              <div
                v-if="
                  !isAllExpanded &&
                  reservation.arr[activeTripIndexes[resIndex]]
                    ?.showReservationData
                "
                class="reservationData"
              >
                {{ reservationData }}
              </div>
            </div>

            <!-- 旅程詳細情報 -->
            <div v-if="isAllExpanded">
              <div class="trip-item">
                <div class="trip-header">
                  {{ reservation.arr[activeTripIndexes[resIndex]]?.tripHeader }}
                </div>
                <div class="trip-date">
                  <span>
                    {{
                      reservation.arr[activeTripIndexes[resIndex]]?.startDate
                    }}
                  </span>
                  <span>
                    {{ reservation.arr[activeTripIndexes[resIndex]]?.endDate }}
                  </span>
                </div>
                <div class="trip-time">
                  <span>
                    {{
                      reservation.arr[activeTripIndexes[resIndex]]?.startTime
                    }}
                  </span>
                  <span class="line">-</span>
                  <span>
                    {{ reservation.arr[activeTripIndexes[resIndex]]?.endTime }}
                  </span>
                </div>
                <div class="trip-location">
                  <span>
                    {{
                      reservation.arr[activeTripIndexes[resIndex]]
                        ?.startLocation
                    }}
                  </span>
                  <span>
                    {{
                      reservation.arr[activeTripIndexes[resIndex]]?.endLocation
                    }}
                  </span>
                </div>
                <button
                  class="show-ticket-btn"
                  @click="
                    showTicket(reservation.arr[activeTripIndexes[resIndex]])
                  "
                >
                  乗車券を表示
                  <img
                    src="~/assets/image/ticket.png"
                    class="ticketIcon"
                    alt="乗車券アイコン"
                  />
                </button>
              </div>

              <!-- 旅程ナビゲーション -->
              <div class="trip-navigation">
                <div
                  v-if="activeTripIndexes[resIndex] > 0"
                  class="prev-trip"
                  @click="showPrevTrip(resIndex)"
                >
                  <img
                    src="~/assets/image/Icon.png"
                    class="prevIcon"
                    alt="前の便アイコン"
                  />
                  前の便
                </div>

                <div
                  v-if="
                    activeTripIndexes[resIndex] < reservation.arr.length - 1
                  "
                  class="next-trip"
                  :class="{ 'ml-auto': !(activeTripIndexes[resIndex] > 0) }"
                  @click="showNextTrip(resIndex)"
                >
                  次の便
                  <img
                    src="~/assets/image/Icon.png"
                    class="nextIcon"
                    alt="次の便アイコン"
                  />
                </div>
              </div>

              <!-- 予約アクションボタン -->
              <div
                class="reservation-actions"
                @click="confirmOrChangeReservation()"
              >
                <span>予約確認・変更</span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- ページナビゲーション -->
    <div class="page-nav">
      <img
        src="~/assets/image/Icon.png"
        class="prevIcon"
        @click="prevPage"
        alt="前のページへ"
      />
      <button
        v-for="page in tickets.length"
        :key="page"
        class="page-btn"
        :class="{ active: currentPage === page }"
        @click="switchPage(page)"
      >
        {{ page }}
      </button>
      <img
        src="~/assets/image/Icon.png"
        class="nextIcon"
        @click="nextPage"
        alt="次のページへ"
      />
    </div>
  </div>

  <!-- 予約がない場合の表示 -->
  <div class="reservation-card" v-if="!tickets.length && mobile">
    <div class="header">
      <h2 class="title">直近の乗車予定</h2>
    </div>
    <div class="content">
      <p class="message">乗車前の予約はありません</p>
    </div>
  </div>

  <!-- PC表示 -->
  <div v-show="!mobile">
    <h2 class="sectionHeading">直近の乗車予定</h2>
    <div class="headingUnderline"></div>
    <div class="ticketMainContainer">
      <div class="hideScrollbar">
        <div
          class="ticketBox"
          v-for="(ticket, index) in tickets"
          :key="ticket.id"
          :class="{ 'active-ticket': activeTicketId === ticket.id }"
          @click="switchToTicket(ticket.id)"
        >
          <div>
            <div class="routeDisplay" v-if="getVisibleTrips(ticket).length > 0">
              <span>{{ getVisibleTrips(ticket)[0]?.start }}</span>
              <img
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="経路アイコン"
              />
              <span>{{ getVisibleTrips(ticket)[0]?.end }}</span>
              <img
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="経路アイコン"
              />
              <span>{{ getVisibleTrips(ticket)[0]?.next }}</span>
              <img
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="経路アイコン"
              />
              <span>{{ getVisibleTrips(ticket)[0]?.final }}</span>
            </div>

            <div style="height: 42px">
              <div class="reservationNumberDisplay">
                予約番号：{{ ticket.id }}
              </div>
              <div
                v-if="getVisibleTrips(ticket)[0]?.showReservationData"
                class="reservationData"
              >
                {{ reservationData }}
              </div>
            </div>
          </div>

          <div
            :class="`ticketScrollContainer ticketScrollContainer-${ticket.id}`"
          >
            <template
              v-for="(trip, idx) in getVisibleTrips(ticket)"
              :key="trip.id"
            >
              <div
                class="ticketCardWrapper"
                :class="{
                  'single-card': getVisibleTrips(ticket).length === 1,
                  'first-card':
                    getVisibleTrips(ticket).length === 2 && idx === 0,
                  'second-card':
                    getVisibleTrips(ticket).length === 2 && idx === 1
                }"
              >
                <div class="tripItemWrapper">
                  <div v-if="trip.isContentVisible">
                    <div class="tripInfoItem">
                      <div class="tripHeaderTitle">{{ trip.tripHeader }}</div>
                      <div class="tripDateInfo">
                        <span>{{ trip.startDate }}</span>
                        <span>{{ trip.endDate }}</span>
                      </div>
                      <div class="tripTimeInfo">
                        <span>{{ trip.startTime }}</span>
                        <span class="timeSeparatorLine">-</span>
                        <span>{{ trip.endTime }}</span>
                      </div>
                      <div class="tripLocationInfo">
                        <span>{{ trip.startLocation }}</span>
                        <span>{{ trip.endLocation }}</span>
                      </div>
                      <button
                        class="showTicketButton"
                        @click.stop="showTicket(trip, ticket.id)"
                      >
                        乗車券を表示
                        <img
                          src="~/assets/image/ticket.png"
                          class="ticketIconImg"
                          alt="乗車券アイコン"
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <img
                v-if="getVisibleTrips(ticket).length === 2 && idx === 0"
                src="~/assets/image/triangle.png"
                class="triangleIcon"
                alt="区切りアイコン"
              />
            </template>
          </div>

          <div class="actionsContainer">
            <div class="trip-navigation">
              <div
                class="prevTripButton"
                @click.stop="prevTicketPage(ticket)"
                v-if="
                  getTotalPages(ticket) > 1 && (ticketPages[ticket.id] ?? 1) > 1
                "
              >
                <img
                  src="~/assets/image/Icon.png"
                  alt="前の便アイコン"
                  class="prevTripIcon"
                />
                前の便
              </div>

              <div
                class="nextTripButton"
                @click.stop="nextTicketPage(ticket)"
                v-if="
                  getTotalPages(ticket) > 1 &&
                  (ticketPages[ticket.id] ?? 1) < getTotalPages(ticket)
                "
              >
                次の便
                <img
                  src="~/assets/image/Icon.png"
                  class="nextTripIcon"
                  alt="次へアイコン"
                />
              </div>
            </div>
            <div
              class="reservationActionsArea"
              @click.stop="
                confirmOrChangeReservation(
                  getVisibleTrips(ticket)[0]!,
                  ticket.id
                )
              "
            >
              <span>予約確認・変更</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- チケット切り替えナビ -->
    <div class="page-nav" v-if="ticketIds.length > 1">
      <img
        src="~/assets/image/Icon.png"
        class="prevGlobalPageIcon"
        @click="prevTicket"
        alt="前へアイコン"
        v-if="activeIndex > 0"
      />

      <button
        v-for="(ticketId, index) in ticketIds"
        :key="ticketId"
        class="page-btn"
        :class="{ active: activeTicketId === ticketId }"
        @click="switchToTicket(ticketId)"
      >
        {{ index + 1 }}
      </button>

      <img
        src="~/assets/image/Icon.png"
        class="nextGlobalPageIcon"
        @click="nextTicket"
        alt="次へアイコン"
        v-if="activeIndex < ticketIds.length - 1"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ticket-container {
  padding: 20px;
  overflow-x: auto;
}

.section-title {
  text-align: center;
  color: #26499d;
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
}

.title-underline {
  width: 40px;
  height: 2px;
  background: #26499d;
  margin: 0 auto 20px;
}

.ticket-scroll-container {
  display: flex;
  overflow-x: auto;
  gap: 16px;
  padding-bottom: 8px;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  &.center-container {
    justify-content: center;
  }
}

.ticket-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 300px;
  flex: 0 0 auto;
  height: fit-content;
  padding: 0 0 10px 0;
}

.route {
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 15px;
  background-color: #e7f2fa;
  height: 50px;
  border-radius: 8px 8px 0 0;
}

.route-expand-btn {
  background: none;
  border: none;
  padding: 15px;
  margin-left: 8px;
}

.chevron-icon {
  display: inline-block;
  transition: transform 0.3s ease;
  width: 24px;
  height: 24px;
  color: #2d5199;
  margin-top: 2px;

  &.rotate-180 {
    transform: rotate(180deg);
  }
}

.reservation-number {
  font-size: 14px;
  text-align: center;
  color: #26499d;
  margin: 16px 0 0 0;
}

.reservationData {
  text-align: center;
  font-size: 14px;
  color: #000;
}

.trip-item {
  background-color: #f3faff;
  border-radius: 6px;
  border: 1px solid #acd1ed;
  padding-bottom: 16px;
  margin: 15px;
}

.trip-header {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #26499d;
  background-color: #acd1ed;
  padding: 4px 0;
  border-radius: 4px 4px 0 0;
  height: 33px;
}

.trip-date,
.trip-time,
.trip-location {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 0 25px;
}

.trip-date {
  color: #555;
  padding: 18px 25px 6px 25px;
}

.trip-time {
  font-size: 20px;
  font-weight: bold;
  color: #26499d;
  padding: 0 25px 6px 25px;

  .line {
    width: 14px;
    height: 5px;
    color: #ccc;
  }
}

.trip-location {
  color: #555;
  padding: 0 25px 16px 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.show-ticket-btn {
  background-color: #2d5199;
  color: #fff;
  border-radius: 40px;
  padding: 16px 0px;
  font-size: 14px;
  transition: background-color 0.3s ease;
  width: 230px;
  height: 50px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;

  .ticketIcon {
    width: 22px;
    height: 22px;
    margin-left: 10px;
  }
}

.trip-navigation {
  display: flex;
  padding: 0 20px 0 10px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
}

.prev-trip,
.next-trip {
  font-size: 14px;
  color: #26499d;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  transition: color 0.3s ease;
  margin-right: 0;
}

.reservation-actions {
  font-size: 14px;
  color: #26499d;
  text-align: center;
  transition: background-color 0.3s ease;
  border-top: 1px solid #9cbcd4;
  padding: 16px 20px;

  span {
    border-bottom: 1px solid #26499d;
  }
}

.page-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.page-btn {
  width: 30px;
  height: 30px;
  color: #26499d;
  margin: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.active {
    background-color: #e7f2fa;
    color: #26499d;
    border-color: #2d5199;
    border-radius: 50%;
  }
}

.prevIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(270deg);
  transition: transform 0.3s ease;
}

.nextIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.reservation-card {
  background-color: white;
  width: 85%;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin: 0 auto;
  border: 1px solid #f3f4f6;
}

.header {
  background-color: #eff6ff;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
}

.title {
  color: #26499d;
  font-weight: 500;
  font-size: 18px;
  text-align: center;
  margin: 0;
}

.content {
  padding: 32px 24px;
  text-align: center;
}

.message {
  color: #4b5563;
  margin: 0;
}

/* アイコン */
.trigicon {
  width: 13px;
  height: 8px;
  margin: 0 8px;
}

/* PC版スタイル */
.prevTripButton {
  font-size: 14px;
  color: #26499d;
  text-align: left;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: color 0.3s ease;
  margin-left: 20px;
  cursor: pointer;
}

.prevTripIcon {
  width: 22px;
  height: 22px;
  margin-right: 10px;
  transform: rotate(270deg);
  transition: transform 0.3s ease;
}

.hideScrollbar {
  display: flex;
  gap: 16px;
  min-width: 74rem;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.sectionHeading {
  text-align: center;
  color: #26499d;
  font-size: 18px;
  font-weight: 600;
  margin: 0 auto 8px;
  display: block;
  width: 100%;
  padding-top: 60px;
}

.headingUnderline {
  width: 40px;
  height: 2px;
  background: #26499d;
  margin: 0 auto 20px;
}

.ticketMainContainer {
  padding: 0 20px 20px 20px;
  text-align: right;
  margin-left: auto;
  margin-right: 0;
  overflow: hidden;
}

/* チケットボックススタイル */
.ticketBox {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  min-width: 54%;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;

  &.active-ticket {
    box-shadow: 0 4px 12px rgba(38, 73, 157, 0.2);
    transform: translateY(-2px);
  }
}

/* 旅程表示スタイル */
.routeDisplay {
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #e7f2fa;
  height: 50px;
  border-radius: 8px 8px 0 0;
}

.reservationNumberDisplay {
  font-size: 14px;
  text-align: center;
  color: #26499d;
  margin: 16px 0 0 0;
}

/* チケットスクロールコンテナ */
.ticketScrollContainer {
  display: flex;
  padding: 20px 0;
  overflow-x: auto;
  scrollbar-width: thin;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  scroll-behavior: smooth;
  justify-content: center;

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #aaa;
  }
}

/* チケットカードスタイル */
.ticketCardWrapper {
  background-color: #fff;
  border-radius: 8px;
  width: 320px;
  flex: 0 0 auto;
  height: fit-content;

  &.single-card {
    margin: 0 auto;
  }

  &.first-card {
    margin-right: 20px;
  }

  &.second-card {
    margin-left: 20px;
  }
}

/* 旅程情報スタイル */
.tripInfoItem {
  background-color: #f3faff;
  border-radius: 6px;
  border: 1px solid #acd1ed;
  padding-bottom: 16px;
  margin: 15px;
}

.tripHeaderTitle {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #26499d;
  background-color: #acd1ed;
  padding: 4px 0;
  border-radius: 4px 4px 0 0;
  height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tripDateInfo,
.tripTimeInfo,
.tripLocationInfo {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 0 25px;
}

.tripTimeInfo {
  font-size: 20px;
  font-weight: bold;
  color: #26499d;
  padding: 0 25px 6px 25px;

  .timeSeparatorLine {
    width: 14px;
    height: 5px;
    color: #ccc;
  }
}

.tripLocationInfo {
  color: #555;
  padding: 0 25px 16px 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ボタンスタイル */
.showTicketButton {
  background-color: #2d5199;
  color: #fff;
  border-radius: 40px;
  padding: 16px 0px;
  font-size: 14px;
  transition: background-color 0.3s ease;
  width: 230px;
  height: 50px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;

  .ticketIconImg {
    width: 22px;
    height: 22px;
    margin-left: 10px;
  }

  &:hover {
    background-color: #22407d;
  }
}

.actionsContainer {
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.nextTripButton {
  font-size: 14px;
  color: #26499d;
  text-align: right;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  transition: color 0.3s ease;
  margin-right: 20px;
  width: 100%;
}

.nextTripIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.reservationActionsArea {
  font-size: 14px;
  color: #26499d;
  text-align: center;
  transition: background-color 0.3s ease;
  border-top: 1px solid #9cbcd4;
  padding: 16px 20px;
  cursor: pointer;

  span {
    border-bottom: 1px solid #26499d;
  }
}

.prevGlobalPageIcon,
.nextGlobalPageIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.prevGlobalPageIcon {
  transform: rotate(-90deg);
  margin-right: 8px;
}

.nextGlobalPageIcon {
  transform: rotate(90deg);
  margin-left: 8px;
}

.triangleIcon {
  width: 13px;
  height: 8px;
  margin: 0 8px;
  align-self: center;
}
</style>
