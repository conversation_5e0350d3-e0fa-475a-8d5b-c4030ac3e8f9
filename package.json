{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@mdi/font": "^7.4.47", "@nuxt/eslint": "^1.7.1", "@nuxt/image": "^1.10.0", "@nuxt/test-utils": "^3.19.2", "@nuxt/ui": "^3.3.0", "@pinia/nuxt": "^0.11.2", "eslint": "^9.32.0", "nuxt": "^4.0.1", "pinia": "^3.0.3", "typescript": "^5.8.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "vuetify-nuxt-module": "^0.18.7"}, "devDependencies": {"sass-embedded": "^1.90.0", "vite-plugin-vuetify": "^2.1.2", "vuetify": "^3.9.5"}}