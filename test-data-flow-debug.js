// 测试数据流调试
console.log('=== 数据流调试测试 ===');

console.log('🔍 问题现象：');
console.log('1. TravelSearchForm.vue 中设定 10月10日 和时间');
console.log('2. BusHeader.vue 显示当天日期，时间为空');
console.log('');

console.log('🔍 数据流分析：');
console.log('1. TravelSearchForm.vue 调用子组件的 handleSearch');
console.log('2. 子组件调用 routeStore.setFromRoundTripForm 或 setFromSearchForm');
console.log('3. 2秒延迟后跳转到 bus-service-selection 页面');
console.log('4. BusHeader.vue 从 routeStore 获取数据');
console.log('');

console.log('🔍 可能的问题：');
console.log('1. 子组件的数据格式不正确');
console.log('2. store 更新时机问题');
console.log('3. BusHeader 初始化时机问题');
console.log('4. 数据覆盖问题');
console.log('');

console.log('🔍 需要检查的点：');
console.log('1. 子组件中的 localData 是否正确');
console.log('2. setFromRoundTripForm 是否正确处理数据');
console.log('3. BusHeader 是否正确获取数据');
console.log('4. 是否有其他代码覆盖了数据');
