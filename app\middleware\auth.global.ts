// 認可が必要なページのアクセス制御（meta.authRequired=true）
export default defineNuxtRouteMiddleware(async (to) => {
  const store = useUserStore()

  // 認証不要のページはそのまま通過
  const authRequired = to.meta?.authRequired === true
  if (!authRequired) return

  // 状態未確定の場合は一度だけセッションを同期
  if (!store.loggedIn) {
    try { await store.hydrateFromSession() } catch {}
  }

  // 未ログインの場合はログイン画面へリダイレクト（戻り先を付与）
  if (!store.loggedIn) {
    const redirect = encodeURIComponent(to.fullPath || '/')
    return navigateTo(`/login?redirect=${redirect}`)
  }
})


