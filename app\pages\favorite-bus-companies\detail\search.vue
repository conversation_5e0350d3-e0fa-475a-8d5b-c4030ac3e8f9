<script lang="ts" setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue'

	/* 全てのバス会社データを格納 */
	const allBusCompanies = ref < {
		id: number;name: string
	} [] > ([])

	/* 検索キーワード */
	const keyword = ref('')

	/* キーワードに基づいてフィルタリングされたバス会社リスト */
	const filteredCompanies = computed(() => {
		if (!keyword.value) return []
		return allBusCompanies.value.filter((c) => c.name.includes(keyword.value))
	})

	/* バス会社データの取得（API呼び出しのシミュレーション） */
	const fetchBusCompanies = async () => {
		allBusCompanies.value = [{
				id: 1,
				name: 'バス会社A'
			},
			{
				id: 2,
				name: 'バス会社B'
			},
			{
				id: 3,
				name: 'バス会社C'
			},
			{
				id: 4,
				name: 'バス会社D'
			},
			{
				id: 5,
				name: 'バス会社I'
			},
			{
				id: 6,
				name: 'バス会社J'
			},
			{
				id: 7,
				name: 'バス会社K'
			},
			{
				id: 8,
				name: 'バス会社L'
			},
			{
				id: 9,
				name: 'バス会社M'
			},
			{
				id: 10,
				name: 'バス会社N'
			}
		]
	}

	onMounted(() => {
		fetchBusCompanies()
	})

	/* お気に入りバス会社一覧ページに戻る */
	const handleBack = async () => {
		await navigateTo({
			path: '/favorite-bus-companies'
		})
	}

	/* 選択したバス会社の詳細ページに遷移 */
	const getCompanyDetail = async (id: number) => {
		await navigateTo({
			path: '/favorite-bus-companies/detail/' + id
		})
	}
</script>

<template>
	<div class="bus-company-search">
		<div class="header">
			<v-icon color="#26499d" size="30px" class="pl-4" @click="handleBack">
				mdi-chevron-left
			</v-icon>
			<h3 class="title">お気に入りバス会社</h3>
		</div>

		<div class="ml-5" style="color: #26499d; font-size: 14px">キーワード</div>
		<v-text-field v-model="keyword" placeholder="バス会社" variant="outlined" density="compact" color="#26499D" hide-details
		 class="search-input" />

		<v-list>
			<v-list-item v-for="company in filteredCompanies" :key="company.id" class="company-item" @click="getCompanyDetail(company.id)">
				<v-list-item-title>{{ company.name }}</v-list-item-title>
				<template #append>
					<v-icon color="#26499D">mdi-chevron-right</v-icon>
				</template>
			</v-list-item>
		</v-list>
	</div>
</template>

<style scoped>
	.bus-company-search {
		background: #fff;
		height: 100%;
	}

	.header {
		position: relative;
		display: flex;
		align-items: center;
		height: 48px;
		justify-content: space-between;
	}

	.back-btn {
		background: none;
		border: none;
		padding: 0 12px;
		display: flex;
		align-items: center;
	}

	.title {
		flex: 1;
		text-align: center;
		color: #26499d;
		font-size: 19px;
		font-weight: 400;
		margin: 0;
	}

	.search-input {
		margin: 1px 16px;
	}

	.company-item {
		border-bottom: 1px solid #eee;
	}

	.v-list-item {
		border-bottom: 1px solid #e0e0e0;
	}

	.v-list-item:first-child {
		border-top: 1px solid #e0e0e0;
	}
</style>
