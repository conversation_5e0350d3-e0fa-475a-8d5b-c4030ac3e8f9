<script lang="ts" setup>
import { ref, computed,onMounted } from 'vue'
import { useRouter ,useRoute} from 'vue-router'
import type { TouristSpot, TabItem } from '~/types/notifications'
const route = useRoute()
/* タブ項目 */
const tabs: TabItem[] = [
  { name: 'すべて', id: 1 },
  { name: 'メンテナンス', id: 2 },
  { name: '遅延・運休', id: 3 },
  { name: '販売情報', id: 4 },
  { name: 'グルメ', id: 5 }
]

const activeTab = ref('すべて')
const router = useRouter()

const noticeId = route.params.noticeId as string

/* 模擬データ */
const items = ref<TouristSpot[]>([
  {
    spot_id: 1,
    name: '2024.12.15',
    description: 'メンテナンス',
    notifcation: 'システムメンテナンスのお知らせ'
  },
  {
    spot_id: 2,
    name: '2024.12.18',
    description: '遅延・運休',
    notifcation: '台風の影響による運休情報'
  },
  {
    spot_id: 3,
    name: '2024.12.20',
    description: '販売情報',
    notifcation: '新商品の発売開始について'
  },
  {
    spot_id: 4,
    name: '2024.12.22',
    description: 'グルメ',
    notifcation: '期間限定グルメフェアのお知らせ'
  },
  {
    spot_id: 5,
    name: '2024.12.25',
    description: 'メンテナンス',
    notifcation: '年末システムメンテナンスのご案内'
  },
  {
    spot_id: 6,
    name: '2024.12.28',
    description: '遅延・運休',
    notifcation: '年末年始の一部列車運休情報'
  },
  {
    spot_id: 7,
    name: '2025.01.01',
    description: '販売情報',
    notifcation: 'お正月セール開始！'
  },
  {
    spot_id: 8,
    name: '2025.01.05',
    description: 'グルメ',
    notifcation: '冬の限定スイーツ販売開始'
  }
])

/* ページネーション */
const currentPage = ref(1)
const pageSize = 5
const totalPages = computed(() =>
  Math.ceil(filteredList.value.length / pageSize)
)

/* タブ切り替え */
function selectTab(tab: string) {
  activeTab.value = tab
  currentPage.value = 1
}

/* データフィルタリング */
const filteredList = computed(() => {
  return activeTab.value === 'すべて'
    ? items.value
    : items.value.filter((item) => item.description === activeTab.value)
})

// 現在のページデータ
const pagedList = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  return filteredList.value.slice(start, start + pageSize)
})

// ページをめくる
function goToPage(page: number) {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

function guangdi(id: number) {
  router.push({
    path: `/notifications/edit/${id}`
  })
}




const noticeIdNum = Number(noticeId)
onMounted(() => {
  console.log('当前通知ID:', noticeIdNum)
})
</script>

<template>
  <v-container class="touristPage" fluid>
    <div class="regionpadding">
      <BaseHeader title="お知らせ" :showBack="true" :showRightIcon="false" />
    </div>

    <div class="tabs scrollX createPad">
      <button
        v-for="(tab, index) in tabs"
        :key="tab.id"
        class="tabBtn"
        :class="{
          active: activeTab === tab.name,
          lastTab: index === tabs.length - 1
        }"
        @click="selectTab(tab.name)"
      >
        {{ tab.name }}
      </button>
    </div>

    <div class="cardGrid createBot">
      <div
        v-for="item in pagedList"
        :key="item.spot_id"
        class="card"
        elevation="1"
        @click="guangdi(item.spot_id)"
      >
        <div class="cardContent">
          <p>{{ item.name }}</p>
          <p class="maintenance">{{ item.description }}</p>
        </div>
        <div class="cardContentWrapper">
          <p class="clampedText">{{ item.notifcation }}</p>
        </div>
      </div>
    </div>

    <div class="pagination">
      <img
        src="~/assets/image/Icon.png"
        class="prevIcon"
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage === 1"
        alt="前のページへ"
      />

      <button
        v-for="page in totalPages"
        :key="page"
        :class="{ active: currentPage === page }"
        @click="goToPage(page)"
        class="pageBtn"
      >
        {{ page }}
      </button>

      <img
        src="~/assets/image/Icon.png"
        class="nextIcon"
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage === totalPages"
        alt="画像の読み込みに失敗しました"
      />
    </div>
  </v-container>
</template>

<style lang="scss" scoped>
.prevIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(270deg);
  transition: transform 0.3s ease;
}

.nextIcon {
  width: 22px;
  height: 22px;
  margin-left: 10px;
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

.touristPage {
  padding: 0px !important;
  background-color: #fff;
}

.searchBar {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.tabs.scrollX {
  display: flex;
  gap: 0;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 16px;
  padding-bottom: 8px;

  &::-webkit-scrollbar {
    display: none;
  }
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.pageBtn {
  width: 30px;
  height: 30px;
  color: #26499d;
  margin: 0 5px;
  transition: all 0.3s ease;
}

.pageBtn.active {
  background-color: #e7f2fa;
  color: #26499d;
  border-color: #2d5199;
  border-radius: 50%;
}

.tabBtn {
  flex: 0 0 auto;
  background: none;
  border: none;
  padding: 6px 16px;
  font-size: 14px;
  color: #000;
  font-weight: bold;
  position: relative;
  transition: all 0.2s;
  height: 40px;
  text-align: center;

  &:not(.lastTab) {
    border-right: 1px solid #7d7d7d;
  }

  &.active {
    color: #000;
    font-weight: bold;
    background-color: #acd1ed;

    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 15px solid #acd1ed;
    }
  }
}

.card {
  overflow: hidden;
  height: 100%;
  border-bottom: 1px solid #dfdfdf;
  padding: 12px 0;

  .maintenance {
    border: 1px solid #ed785f;
    padding: 0 8px;
    color: #ed785f;
  }

  h3 {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 4px;
  }

  p {
    font-size: 12px;
    color: #555;
    margin-right: 10px;
  }
}

.cardContent {
  display: flex;
}

.cardContentWrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.cardText {
  padding: 10px 10px 0 10px;
  flex: 1;
}

.imageContainer {
  position: relative;
  height: 120px;
}

.imageLocation {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  z-index: 1;
}

.clampedText {
  padding-top: 8px;
  color: #000 !important;
}
</style>
