<script lang="ts" setup>
import { ref, computed } from 'vue'
import Pagination from './BusPagination.vue'
import { useRouter } from 'vue-router'
import type { TransportRoute } from '~/types/easy-choice'
const router = useRouter()

/* * プロパティとイベントの定義 */
const props = defineProps<{
  filteredList: TransportRoute[]
  tabs: { name: string; id: number }[]
}>()

const emit = defineEmits<{
  (e: 'tab-change', tabName: string): void
  (e: 'page-change', page: number): void
}>()

/* * 状態管理 */
const activeTab = ref('発車時間順')
const currentPage = ref(1)
const itemsPerPage = 5

/* * 乗継の空席判定 */
const routeHasVacantTransfer = (route: TransportRoute) => {
  const transfers = route.schedule?.[0]?.transfers || []
  return transfers.some((t) => t.isVacant)
}

/* * 展開状態の管理 */
const expandedMap = ref<Record<number, boolean>>({})

/* * 展開/折りたたみ処理 */
const toggleExpand = (id: number) => {
  expandedMap.value[id] = !expandedMap.value[id]
}

/* * タブによるソート処理 */
const sortedList = computed(() => {
  const listCopy = [...props.filteredList]
  switch (activeTab.value) {
    case '発車時間順':
      return listCopy.sort((a, b) =>
        a.departureTime.localeCompare(b.departureTime)
      )
    case '安い順':
      return listCopy.sort((a, b) => a.price - b.price)
    case '到着早い順':
      return listCopy.sort((a, b) => a.arrivalTime.localeCompare(b.arrivalTime))
    case '乗継':
      return listCopy.sort((a, b) => {
        const countA = a.transferCount === 'なし' ? 0 : Number(a.transferCount)
        const countB = b.transferCount === 'なし' ? 0 : Number(b.transferCount)
        return countA - countB
      })
    default:
      return listCopy
  }
})

/* * ページネーション処理 */
const paginatedList = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage
  return sortedList.value.slice(startIndex, startIndex + itemsPerPage)
})

const totalPages = computed(() => {
  return Math.ceil(sortedList.value.length / itemsPerPage)
})

/* * ページ変更処理 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page)
}

/* * 所要時間の解析処理 */
const parseDuration = (duration: string) => {
  const match = duration.match(/(\d+)時間(\d+)分/)
  if (!match) return { hours: '', minutes: '' }
  return { hours: match[1], minutes: match[2] }
}

/* * タブ変更処理 */
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  currentPage.value = 1
  emit('tab-change', tabName)
}

/* * 動的料金計算処理 */
const calculateFinalPrice = (route: TransportRoute) => {
  if (route.discountRate && route.originalPrice) {
    return Math.floor(route.originalPrice * (1 - route.discountRate / 100))
  }
  return route.price
}

/* * 詳細ページに遷移 */
const goToDetail = (id: number) => {
  // router.push({
  //   path: '/passenger-selection',
  //   query: { id: String(id) } 
  // })
}

/* * 外部公開メソッド */
defineExpose({
  handlePageChange,
  currentPage,
  totalPages
})
</script>

<template>
  <v-container class="touristPage" fluid>
    <div class="tabs scrollX">
      <div style="display: flex">
        <div v-for="(tab, index) in tabs" :key="tab.id" class="tabBtn" :class="{
          active: activeTab === tab.name,
          lastTab: index === tabs.length - 1
        }" @click="handleTabChange(tab.name)">
          {{ tab.name }}
        </div>
      </div>
    </div>

    <div class="flightList">
      <div v-for="route in paginatedList" :key="route.id" class="flightCard" :class="{ soldOut: route.vacant }"
        @click="goToDetail(route.id)">
        <div class="routeHeader">
          <div class="routeInfoWithBorder">
            <div class="routeInfo">
              <template v-for="(point, idx) in route.routePoints" :key="idx">
                <span class="cityName">{{ point.departure }}</span>

                <span v-if="point.isFavorite" class="mdi mdi-chevron-double-right arrow"
                  :class="{ redArrow: routeHasVacantTransfer(route) }"></span>

                <span v-if="idx < route.routePoints!.length - 1 && point.isFavorite !== true"
                  class="mdi mdi-chevron-double-right arrow"></span>
              </template>
            </div>
          </div>
        </div>

        <div class="bottomBar">
          <div class="bottomLine"></div>
          <div class="expandIcon" @click="toggleExpand(route.id)" v-if="!expandedMap[route.id]">
            <span class="mdi mdi-chevron-up-circle-outline"></span>
          </div>
        </div>

        <div v-if="expandedMap[route.id]" class="schedule-container">
          <div v-for="(seg, idx) in route.schedule" :key="idx" class="schedulebox">
            <div class="scheduleItem">
              <p class="date">{{ seg.date }}</p>
              <p class="departure-time">{{ seg.departureTime }} 発</p>
            </div>

            <template v-for="(t, tIdx) in seg.transfers" :key="tIdx">
              <div class="divider" :class="{ 'red-divider': t.isVacant }"></div>
              <div class="transfer-point">
                <p>{{ t.arrival }} 着</p>
                <div class="dot" :class="{ 'red-dot': t.isVacant }"></div>
                <p>{{ t.departure }} 発</p>
              </div>
            </template>

            <div class="divider"></div>

            <div class="scheduleItem">
              <p class="date">{{ seg.date }}</p>
              <p class="arrival-time">{{ seg.arrivalTime }} 着</p>
            </div>
          </div>

          <div v-if="expandedMap[route.id]" class="bottomBar">
            <div class="bottomLine"></div>
            <div class="expandIcon" @click="toggleExpand(route.id)">
              <span class="mdi mdi-chevron-down-circle-outline"></span>
            </div>
          </div>
        </div>

        <div class="mainContent">
          <div class="topInfo">
            <div class="leftSection">
              乗継: {{ route.transferCount }}
              <template v-if="route.transferCount !== 'なし'">回</template>

              <div class="duration">
                <span class="durationNumber">
                  {{ parseDuration(route.duration).hours }}
                </span>
                <span class="durationUnit">時間</span>
                <span class="durationNumber">
                  {{ parseDuration(route.duration).minutes }}
                </span>
                <span class="durationUnit">分</span>
              </div>

              <div class="soldOutBadge" v-if="route.vacant">
                {{ route.vacant }}
              </div>
            </div>
          </div>

          <div class="anzele-tags">
            <span class="anzele-tag" v-for="tag in route.tags" :key="tag">
              {{ tag }}
            </span>
          </div>

          <div class="flightDetails">
            <div class="timeSection">
              <div class="timeContent">
                <div class="flightTime">
                  <div class="date">{{ route.statdate }}</div>
                  <div class="time">{{ route.departureTime }}</div>
                </div>
                <div class="dateSeparator">_</div>
                <div class="flightTime">
                  <div class="date">
                    {{ route.entdate }}
                  </div>
                  <div class="time">{{ route.arrivalTime }}</div>
                </div>
              </div>
              <div class="verticalLine"></div>
            </div>

            <div class="priceSection">
              <div v-if="route.discountRate" class="discountBadge">
                -{{ route.discountRate }}% 早割料金適用中
              </div>
              <div v-if="route.originalPrice" class="originalPrice">
                {{ route.originalPrice.toLocaleString() }}円～
              </div>
              <div class="finalPrice">
                <span class="passengerInfo">大人1人</span>
                <span class="price">
                  ¥{{ calculateFinalPrice(route).toLocaleString() }}
                </span>
                <span class="priceSymbol">円～</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Pagination :current-page="currentPage" :total-pages="totalPages" @page-change="handlePageChange"
      :show-page-numbers="true" :max-visible-pages="5" />
  </v-container>
</template>

<style lang="scss" scoped>
.touristPage {
  padding: 20px 0 !important;
  background-color: #e7f2fa;
}

.tabs.scrollX {
  display: flex;
  overflow-x: hidden;
  margin-bottom: 16px;
  padding-bottom: 8px;

  &::-webkit-scrollbar {
    display: none;
  }

  >div {
    display: flex;
    flex: 1;
  }
}

.tabBtn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: #fff;
  border: none;
  padding: 6px 0;
  font-size: 14px;
  font-weight: bold;
  height: 40px;
  position: relative;
  transition: all 0.2s;

  &:not(.lastTab) {
    &::after {
      content: '';
      position: absolute;
      top: 20%;
      right: 0;
      height: 60%;
      width: 1px;
      border-right: 1px solid #7d7d7d;
    }
  }

  &.active {
    background-color: #acd1ed;

    &::before {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 15px solid #acd1ed;
    }

    &:not(.lastTab)::after {
      display: none;
    }
  }

  &:has(+ .active):not(.lastTab)::after {
    display: none;
  }
}

.flightList {
  margin: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.flightCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #99b7cd;

  &.soldOut {
    background-color: #dbdcdc;
    pointer-events: none;
  }
}

.routeHeader {
  padding: 10px 16px 0 16px;
  display: flex;
  align-items: center;
  position: relative;

  .routeInfoWithBorder {
    flex: 1;
  }

  .routeInfo {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .cityName {
      font-weight: 500;
      color: #000;
      font-size: 20px;
    }

    .arrow {
      width: 20px;
      height: 10px;
      line-height: 10px;
    }

    .redArrow {
      color: red;
    }
  }

  .expandIcon {
    color: #6b7280;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
    position: relative;
    top: 20px;
  }
}

.botBox {
  display: flex;
  border-bottom: 1px solid #f60;

  .bottomDivider {
    border-bottom: 1px solid #dedede;
    margin: 0 16px 10px;
  }
}

.mainContent {
  padding: 0 16px 14px 16px;

  .topInfo {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    margin-bottom: 8px;

    .soldOutBadge {
      position: absolute;
      right: 40px;
      top: 0;
      background-color: #e53935;
      color: #fff;
      padding: 4px 8px;
      font-size: 12px;
      font-weight: bold;
      white-space: nowrap;
    }

    .leftSection {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .duration {
      font-size: 12px;
      color: #244188;
      background-color: #e5eff7;
      border-radius: 30px;
      padding: 2px 8px;
      margin-left: 20px;

      .durationNumber {
        font-weight: 500;
      }

      .durationUnit {
        font-size: 11px;
        margin-right: 3px;
      }
    }
  }

  .anzele-tags {
    display: flex;
    gap: 8px;
    padding-bottom: 5px;

    .anzele-tag {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      white-space: nowrap;

      &:nth-child(1) {
        background-color: #d16f5b;
        color: #fff;
      }

      &:nth-child(2) {
        background-color: #5cc1c8;
        color: #fff;
      }

      &:nth-child(3) {
        background-color: #73c086;
        color: #fff;
      }
    }
  }

  .flightDetails {
    display: flex;
    align-items: end;
    justify-content: space-between;

    .timeSection {
      display: flex;
      align-items: stretch;
      gap: 15px;
      position: relative;

      .timeContent {
        display: flex;
        align-items: center;
        gap: 15px;
        padding-right: 15px;
      }

      .flightTime {
        text-align: center;

        .date {
          font-size: 11px;
          color: #000;
          margin-bottom: 3px;
        }

        .time {
          font-size: 20px;
          font-weight: bold;
          color: #1f2937;
          line-height: 1;
        }
      }

      .dateSeparator {
        color: #99b7cd;
        padding: 0 5px;
        align-self: baseline;
        margin-bottom: 10px;
      }

      .verticalLine {
        width: 1px;
        background-color: #9ab8ce;
        align-self: stretch;
      }
    }

    .priceSection {
      text-align: right;
      padding-left: 15px;

      .discountBadge {
        font-size: 12px;
        color: #036eb7;
        padding: 3px 6px;
        border-radius: 3px;
        margin-bottom: 3px;
        display: inline-block;
        white-space: nowrap;
      }

      .originalPrice {
        font-size: 11px;
        color: #6b7280;
        text-decoration: line-through;
        margin-bottom: 2px;
      }

      .finalPrice {
        display: flex;
        align-items: baseline;
        gap: 3px;
        justify-content: flex-end;

        .passengerInfo {
          font-size: 11px;
          color: #000;
        }

        .price {
          font-size: 20px;
          font-weight: bold;
          color: #000;
        }

        .priceSymbol {
          font-size: 11px;
          color: #000;
        }
      }
    }
  }
}

.schedule-container {
  padding: 10px;

  .schedulebox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .divider {
    height: 1px;
    border-bottom: 1px dashed #999;
    width: 31px;
  }
}

.scheduleItem {
  text-align: center;
}

.date {
  font-size: 14px;
  color: #333;
}

.departure-time,
.arrival-time {
  width: 63px;
  font-weight: bold;
  color: #000;
}

.transfer-point {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 54px;

  p {
    font-size: 10px;
    color: #666;
    margin: 2px 0;
  }

  .dot {
    width: 10px;
    height: 10px;
    background-color: teal;
    border-radius: 50%;
    margin: 5px 0;
  }
}

.bottomBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;

  .bottomLine {
    padding-top: 10px;
    flex: 1;
    border-bottom: 1px solid #dedede;
    height: 1px;
  }

  .expandIcon {
    color: #6b7280;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
    position: relative;
    top: 4px;
  }
}
</style>
