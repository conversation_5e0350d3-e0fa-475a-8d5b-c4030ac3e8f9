<script lang="ts" setup>
definePageMeta({
  footerPlaceholderHeight: 82
})

const router = useRouter()
const route = useRoute()

const ticketType = computed(() => route.params.type as string)

const showDetailDialog = ref(false)

const expandedAccordions = ref({
  stops: false,
  facilities: false,
  notices: false
})

// バス停留所データ
const busStops = ref([
  {
    id: 1,
    time: '23:20',
    name: '岡山駅 西口ああああああああああああああああああああああああああああ'
  },
  {
    id: 2,
    time: '24:00',
    name: '岡山大学前'
  },
  {
    id: 3,
    time: '07:25',
    name: 'なんばOCAT'
  },
  {
    id: 4,
    time: '08:30',
    name: '大阪駅JR高速BT'
  },
  {
    id: 5,
    time: '08:45',
    name: 'ユニバーサルスタジオジャパン'
  }
])

// 座席番号データ
const seatNumbers = ref(['B1', 'B2', 'B3', 'C1', 'C2', 'C3', 'C4'])

// バス設備データ
const busFacilities = ref([
  {
    id: 1,
    name: '4列シート',
    size: 'normal'
  },
  {
    id: 2,
    name: 'トイレ付',
    size: 'normal'
  },
  {
    id: 3,
    name: 'Wi-Fi',
    size: 'normal'
  },
  {
    id: 4,
    name: 'XXXXXXXXXX',
    size: 'long'
  },
  {
    id: 5,
    name: 'XXXXX',
    size: 'medium'
  },
  {
    id: 6,
    name: 'XXXXXXXX',
    size: 'long'
  },
  {
    id: 7,
    name: 'XXX',
    size: 'small'
  },
  {
    id: 8,
    name: 'XXXXXXX',
    size: 'medium'
  },
  {
    id: 9,
    name: 'XXXXXXXXXXXX',
    size: 'extra-long'
  },
  {
    id: 10,
    name: 'XXXXX',
    size: 'medium'
  }
])

const handleBack = (): void => {
  window.history.back()
}

const handleManualBoarding = (): void => {
  router.push('/ticket/manual-boarding')
}

const toggleAccordion = (key: keyof typeof expandedAccordions.value): void => {
  expandedAccordions.value[key] = !expandedAccordions.value[key]
}
</script>
<template>
  <div class="ticket-page">
    <div class="page-title-header">
      <button class="back-button" @click="handleBack">
        <img src="~/assets/image/Icon.png" alt="戻る" class="back-icon" />
      </button>
      <h1 class="page-title">乗車券</h1>
    </div>
    <v-main class="main-content">
      <v-container class="pa-6">
        <v-card class="ticket-card" elevation="0" rounded="lg">
          <div class="ticket-header">
            <div class="route-info">
              <span class="station-name">岡山</span>
              <div class="route-arrow">
                <div class="triangle-icon"></div>
                <div class="triangle-icon"></div>
              </div>
              <span class="station-name">大阪</span>
            </div>
          </div>

          <div class="ticket-body">
            <div class="ticket-details">
              <div v-if="ticketType === 'qr'" class="time-display">
                <div class="qr-center">
                  <v-icon size="88" color="#000">mdi-qrcode</v-icon>
                </div>
                <div class="time-info">
                  <v-icon size="16" color="#26499D">mdi-reload</v-icon>
                  <span class="time-text">04:56</span>
                </div>
              </div>

              <div v-if="ticketType === 'qr'" class="manual-boarding-btn">
                <v-btn
                  color="#E7F2FA"
                  variant="outlined"
                  size="small"
                  class="text-blue manual-btn"
                  append-icon="mdi-chevron-right"
                  @click="handleManualBoarding"
                >
                  手動で乗車
                </v-btn>
              </div>

              <div v-if="ticketType === 'manual'" class="manual-display">
                乗車済
              </div>

              <div class="info-list">
                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      乗車日
                    </v-chip>
                  </div>
                  <div class="time-large">3/21（金）</div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      発車
                    </v-chip>
                  </div>
                  <div class="info-content">
                    <div class="time-large">23:20</div>
                    <div class="location-text">
                      岡山駅 西口ああああああああああ
                    </div>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      到 着
                    </v-chip>
                  </div>
                  <div class="info-content">
                    <div class="arrival-info">
                      <div class="date-small">3/22（土）</div>
                      <div class="time-large">08:30</div>
                    </div>
                    <div class="location-text">
                      大阪駅JR高速BTあああああああ
                    </div>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      座席番号
                    </v-chip>
                  </div>
                  <div class="seat-numbers">
                    <v-chip
                      v-for="seat in seatNumbers"
                      :key="seat"
                      size="small"
                      color="#E7F2FA"
                      class="ma-1"
                    >
                      {{ seat }}
                    </v-chip>
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      乗車時間
                    </v-chip>
                  </div>
                  <div class="info-value">9時間10分</div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      路線名
                    </v-chip>
                  </div>
                  <div class="info-value">
                    岡山～大阪あああああああああああああああああ線
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      運行会社
                    </v-chip>
                  </div>
                  <div class="info-value">中国ハイウェイバス</div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      乗車便名
                    </v-chip>
                  </div>
                  <div class="info-value">○○○○号　1号車</div>
                </div>

                <div class="divider-container">
                  <div class="qr-decoration">
                    <div class="qr-holes">
                      <div class="hole"></div>
                      <div class="hole"></div>
                      <div class="arrival-hole"></div>
                    </div>
                  </div>
                  <div class="custom-dashed-line my-4"></div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      予約情報
                    </v-chip>
                  </div>
                  <div class="info-value">
                    99912045678
                    <br />
                    リョウビ　タロウ　様
                  </div>
                </div>

                <div class="info-row">
                  <div class="info-label">
                    <v-chip size="small" color="#E7F2FA" text-color="#26499D">
                      料金
                    </v-chip>
                  </div>
                  <div class="info-value">
                    大人4名　子供3名
                    <br />
                    00,00000円
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-card>

        <v-btn
          color="white"
          variant="outlined"
          block
          class="mt-3 detail-btn"
          append-icon="mdi-chevron-right"
          @click="showDetailDialog = true"
        >
          乗車便詳細
        </v-btn>
      </v-container>
    </v-main>

    <div v-if="ticketType === 'qr'" class="floating-trigger-area">
      <div class="trigger-content">
        <div class="main-message">QRコードを乗務員に提示してください</div>
        <div class="warning-message">
          <v-icon size="24" color="#D00000" class="warning-icon">
            mdi-alert
          </v-icon>
          <span class="warning-text">乗車券は再利用できません。</span>
        </div>
      </div>
    </div>

    <div v-if="ticketType === 'manual'" class="floating-boarded-area">
      <div class="boarded-content">
        <div class="boarded-message">このバスは乗車済みです</div>
      </div>
    </div>

    <v-dialog
      v-model="showDetailDialog"
      max-width="375"
      height="600"
      persistent
    >
      <v-card class="detail-dialog" rounded="lg">
        <div class="dialog-header">
          <h2 class="dialog-title">乗車便詳細</h2>
        </div>

        <div class="dialog-content">
          <div class="accordion-list">
            <div class="accordion-item">
              <v-card
                class="accordion-card"
                variant="outlined"
                rounded="lg"
                @click="toggleAccordion('stops')"
              >
                <div class="accordion-header">
                  <v-icon size="24" color="#26499D" class="accordion-icon">
                    mdi-tune
                  </v-icon>
                  <span class="accordion-title">停留所</span>
                  <v-icon
                    size="24"
                    color="#26499D"
                    class="chevron-icon"
                    :class="{ rotated: expandedAccordions.stops }"
                  >
                    mdi-chevron-down
                  </v-icon>
                </div>
                <!-- <v-expand-transition> -->
                <div
                  v-show="expandedAccordions.stops"
                  class="accordion-content"
                >
                  <v-timeline
                    density="compact"
                    side="end"
                    align="start"
                    line-color="#9CBCD4"
                    dot-color="#9CBCD4"
                    class="stops-timeline"
                  >
                    <v-timeline-item
                      v-for="stop in busStops"
                      :key="stop.id"
                      dot-color="#9CBCD4"
                      size="x-small"
                    >
                      <div class="d-flex justify-space-between flex-grow-1">
                        <div class="timeline-time">{{ stop.time }}</div>
                        <div class="timeline-station">{{ stop.name }}</div>
                      </div>
                    </v-timeline-item>
                  </v-timeline>
                </div>
              </v-card>
            </div>

            <div class="accordion-item">
              <v-card
                class="accordion-card"
                variant="outlined"
                rounded="lg"
                @click="toggleAccordion('facilities')"
              >
                <div class="accordion-header">
                  <v-icon size="24" color="#26499D" class="accordion-icon">
                    mdi-tune
                  </v-icon>
                  <span class="accordion-title">バス設備</span>
                  <v-icon
                    size="24"
                    color="#26499D"
                    class="chevron-icon"
                    :class="{ rotated: expandedAccordions.facilities }"
                  >
                    mdi-chevron-down
                  </v-icon>
                </div>
                <v-expand-transition>
                  <div
                    v-show="expandedAccordions.facilities"
                    class="accordion-content"
                  >
                    <div class="facility-tags">
                      <v-chip
                        v-for="facility in busFacilities"
                        :key="facility.id"
                        size="small"
                        variant="outlined"
                        class="facility-chip"
                      >
                        {{ facility.name }}
                      </v-chip>
                    </div>
                  </div>
                </v-expand-transition>
              </v-card>
            </div>

            <div class="accordion-item">
              <v-card
                class="accordion-card"
                variant="outlined"
                rounded="lg"
                @click="toggleAccordion('notices')"
              >
                <div class="accordion-header">
                  <v-icon size="24" color="#26499D" class="accordion-icon">
                    mdi-tune
                  </v-icon>
                  <span class="accordion-title">ご利用上の注意事項</span>
                  <v-icon
                    size="24"
                    color="#26499D"
                    class="chevron-icon"
                    :class="{ rotated: expandedAccordions.notices }"
                  >
                    mdi-chevron-down
                  </v-icon>
                </div>
                <v-expand-transition>
                  <div
                    v-show="expandedAccordions.notices"
                    class="accordion-content"
                  >
                    <div class="notice-item">
                      <p>・乗車券は乗車日当日まで有効です</p>
                      <p>・座席の指定はできません</p>
                      <p>・遅延・運休の場合は払い戻しいたします</p>
                      <p>
                        ・車内での飲食は可能ですが、他のお客様のご迷惑にならないようお願いします
                      </p>
                    </div>
                  </div>
                </v-expand-transition>
              </v-card>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <v-btn
            color="#E7F2FA"
            variant="flat"
            block
            class="close-btn"
            @click="showDetailDialog = false"
          >
            閉じる
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>
<style scoped>
.ticket-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  background-color: #e7f2fa;
  border-top: 1px solid #9cbcd4;
  padding-top: 0;
}

.ticket-card {
  overflow: visible;
  box-shadow: 0px 0px 10px 0px rgba(173, 190, 203, 1) !important;
  position: relative;
}

.ticket-header {
  background: #26499d;
  color: white;
  padding: 8px;
  border-bottom: 1px solid #9cbcd4;
  border-radius: 10px 10px 0px 0px;
}

.route-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.station-name {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.36;
}

.route-arrow {
  display: flex;
  gap: 2px;
  width: 15.38px;
  height: 8.89px;
}

.triangle-icon {
  width: 6.38px;
  height: 8.89px;
  background: #9cbcd4;
  clip-path: polygon(0 0, 100% 50%, 0 100%);
}

.ticket-body {
  position: relative;
  background: white;
  padding: 16px 20px;
  border-radius: 0px 0px 10px 10px;
}

.divider-container {
  position: relative;
  margin: 0 -20px;
}

.qr-decoration {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 20px;
  background: white;
  transform: translateY(-50%);
  z-index: 1;
  overflow: hidden;
}

.qr-holes {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
}

.hole {
  width: 17px;
  height: 17px;
  border-radius: 50%;
  background: #e7f2fa;
  border: none;
  box-shadow: inset 0 2px 4px rgba(156, 188, 212, 0.5),
    inset 0 -1px 2px rgba(156, 188, 212, 0.3);
  position: relative;
  z-index: 2;
  margin-left: -8.5px;
  margin-right: -8.5px;
}

.hole:first-child {
  margin-left: -8.5px;
}

.hole:last-child {
  margin-right: -8.5px;
}

.arrival-hole {
  position: absolute;
}

.time-display {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #9cbcd4;
  border-radius: 5px;
  padding: 16px;
  margin-bottom: 16px;
  height: 120px;
}

.qr-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-info {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-text {
  font-size: 12px;
  font-weight: 500;
  color: #7d7d7d;
  line-height: 1.2;
}

.manual-boarding-btn {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.manual-btn {
  height: 32px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  padding: 6px 10px;
  background-color: #e7f2fa;
  border: 1px solid #9cbcd4;
  border-radius: 5px !important;
}

.manual-display {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #9cbcd4;
  border-radius: 5px;
  padding: 16px;
  margin-bottom: 16px;
  height: 120px;
  font-size: 24px;
  font-weight: 700;
  color: #26499d;
  background-color: #eeeeef;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-label {
  min-width: 68px;
}

.info-label .v-chip {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  padding: 2px 3px;
  height: auto;
  border-radius: 2px;
  background-color: #e7f2fa;
  color: #26499d !important;
}

.info-value {
  font-size: 12px;
  line-height: 1.67;
  flex: 1;
  font-weight: 400;
}

.info-content {
  flex: 1;
  display: flex;
}

.time-large {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.36;
  margin-bottom: 2px;
}

.date-small {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 2px;
  white-space: nowrap;
}

.location-text {
  font-size: 12px;
  line-height: 1.67;
  color: #000;
  font-weight: 400;
  margin-left: 10px;
}

.arrival-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 4px;
}

.seat-numbers {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

.seat-numbers .v-chip {
  height: 24px;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.375;
  padding: 0px 12px;
  border-radius: 4px;
}

.text-blue {
  color: #26499d !important;
}

.detail-btn {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d !important;
  padding: 14px;
  border-color: #9cbcd4;
  border-radius: 8px !important;
  height: 48px;
  background-color: #ffffff;
}

.detail-btn :deep(.v-btn__append) {
  color: #26499d !important;
}

.custom-dashed-line {
  width: 89%;
  height: 1px;
  border-top: 1px dashed #9cbcd4;
  margin: 0 auto !important;
  position: relative;
  z-index: 11;
}

.page-title-header {
  background-color: #ffffff;
  padding: 12px 16px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 16px;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: rgba(38, 73, 157, 0.1);
}

.back-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  transform: rotate(-90deg);
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}

:deep(.v-chip--variant-tonal .v-chip__underlay) {
  background-color: #e7f2fa;
}

:deep(.v-chip__underlay) {
  background-color: #e7f2fa;
}

.seat-numbers :deep(.v-chip) {
  color: #000 !important;
  background-color: #e7f2fa !important;
}

.detail-dialog {
  background: white;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
  height: 600px;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: 24px 16px 0px;
  text-align: center;
  flex-shrink: 0;
}

.dialog-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000;
  margin: 0;
}

.dialog-content {
  padding: 24px 16px;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.accordion-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.accordion-item {
  width: 100%;
}

.accordion-card {
  border: 1px solid #9cbcd4 !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

.accordion-card:hover {
  background-color: #f8fbfd;
}

.accordion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  min-height: 50px;
}

.accordion-icon {
  flex-shrink: 0;
}

.accordion-title {
  flex: 1;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #000;
}

.chevron-icon {
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.chevron-icon.rotated {
  transform: rotate(180deg);
}

.accordion-content {
  padding: 0 16px 16px;
  border-top: 1px solid #f0f0f0;
}

/* タイムライン関連のスタイル */
.stops-timeline {
  padding: 16px 0;
}

.timeline-time {
  font-size: 16px;
  font-weight: 700;
  text-align: right;
  padding-right: 8px;
}

.timeline-station {
  font-size: 14px;
  font-weight: 400;
  color: #000;
  padding-left: 8px;
}

/* Vuetifyタイムラインのカスタマイズ */
.stops-timeline :deep(.v-timeline-item__body) {
  padding-top: 0;
  padding-bottom: 16px;
}

.stops-timeline :deep(.v-timeline-item__opposite) {
  padding-top: 0;
  padding-bottom: 16px;
}

.stops-timeline :deep(.v-timeline-divider__dot) {
  background-color: #9cbcd4 !important;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(156, 188, 212, 0.3);
}

.stops-timeline :deep(.v-timeline-divider__line) {
  background-color: #9cbcd4 !important;
  width: 2px;
}

.facility-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px 0;
}

.facility-chip {
  height: 32px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  color: #000 !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 16px !important;
  background-color: #ffffff !important;
  padding: 0 16px !important;
  /* 幅を自動調整するため、min-width を削除 */
  width: auto !important;
  flex-shrink: 0;
}

.notice-item p {
  font-size: 12px;
  line-height: 1.67;
  color: #000;
  margin: 4px 0;
}

.dialog-footer {
  padding: 0 16px 24px;
  flex-shrink: 0;
}

.close-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d !important;
  border: 1px solid #9cbcd4;
  border-radius: 4px !important;
  background-color: #e7f2fa !important;
}

.floating-trigger-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #cccbca;
  box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.trigger-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px 16px;
}

.main-message {
  font-weight: bold;
  font-size: 16px;
  line-height: 1.5625;
  color: #000000;
  text-align: center;
}

.warning-message {
  display: flex;
  align-items: center;
  gap: 8px;
}

.warning-icon {
  flex-shrink: 0;
}

.warning-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5714285714285714;
  color: #d00000;
}

.ticket-page .main-content {
  padding-bottom: 80px;
}

.floating-boarded-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 83px;
  background: #ffffff;
  border-top: 1px solid #cccbca;
  box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px 14px;
}

.boarded-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.boarded-message {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
  color: #7d7d7d;
  text-align: center;
}
</style>
