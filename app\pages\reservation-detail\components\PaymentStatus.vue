<script lang="ts" setup>
interface Props {
  // ステータス（URLクエリパラメータから取得）
  status?: string
  // 支払いステータステキスト
  statusText?: string
  // 期限テキスト
  deadlineText?: string
  // 説明テキスト
  description?: string
}

// プロパティのデフォルト値を設定
const props = withDefaults(defineProps<Props>(), {
  status: '決済待ち',
  statusText: '決済待ち',
  deadlineText: '決済期限：2024年2月6日（木）',
  description:
    'この予約は未決済です。期日までに決済がされない場合、自動的にキャンセルとなります。'
})

// コンポーネントを表示するかどうかの判定
const shouldShowComponent = computed(() => {
  return props.status !== '決済済'
})

// 予約ボタンのクリック処理
const handleReservation = () => {
  // 同じ内容で予約する処理をここに実装
  console.log('同じ内容で予約がクリックされました')
  // 実際の実装では予約ページへの遷移や予約データの処理を行う
}
</script>
<template>
  <div v-if="shouldShowComponent" class="payment-status">
    <!-- 決済待ちの場合の表示 -->
    <div v-if="status === '決済待ち'" class="payment-pending">
      <h3 class="status-title">
        {{ statusText }}
        <br />
        {{ deadlineText }}
      </h3>
      <p class="status-description">
        {{ description }}
      </p>
    </div>

    <!-- 乗車済・キャンセル済の場合のボタン表示 -->
    <div
      v-else-if="status === '乗車済' || status === 'キャンセル済'"
      class="action-button-container"
    >
      <v-btn
        class="reservation-button"
        variant="outlined"
        color="#26499D"
        size="large"
        @click="handleReservation"
      >
        同じ内容で予約
      </v-btn>
    </div>
  </div>
</template>
<style scoped>
.payment-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 5px 0;
}

.payment-pending {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.status-title {
  font-size: 20px;
  font-weight: 400;
  color: #d00000;
  text-align: center;
  line-height: 1.2;
  margin: 0;
}

.status-description {
  font-size: 14px;
  line-height: 1.57;
  color: #d00000;
  text-align: left;
  width: 310px;
  margin: 0;
}

/* ボタン表示用のコンテナ */
.action-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
}

/* 予約ボタンのスタイル */
.reservation-button {
  background-color: #e7f2fa !important;
  border: 1px solid #26499d !important;
  color: #26499d !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  padding: 12px 24px !important;
  border-radius: 8px !important;
  min-width: 200px !important;
  height: 48px !important;
}

.reservation-button:hover {
  background-color: #d1e7f5 !important;
}
</style>
