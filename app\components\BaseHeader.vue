<script lang="ts" setup>
/**
 * ヘッダー共通コンポーネント
 * - 左側に戻るボタン（任意）
 * - 中央にタイトル
 * - 右側にアイコン（任意）
 */
interface Props {
  title: string
  showBack?: boolean
  showRightIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBack: true,
  showRightIcon: false
})

/* 戻るボタンの処理 */
const emit = defineEmits<{
  (e: 'back'): void
  (e: 'right-click'): void
}>()

const handleBack = () => {
  emit('back')
  if (!props.showBack) return
  if (window.history.length > 1) {
    window.history.back()
  }
}
</script>

<template>
  <div class="header">
    <button v-if="showBack" class="back-Btn" @click="handleBack">
      <img
        alt="戻るボタン"
        src="~/assets/image/Icon.png"
        class="nextTripIcon"
      />
    </button>

    <h1 class="title">{{ title }}</h1>

    <span
      v-if="showRightIcon"
      class="mdi mdi-alert-circle-outline"
      @click="$emit('right-click')"
    ></span>
  </div>
</template>

<style lang="scss" scoped>
.header {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 10px 0;
  .backBtn {
    background: none;
    color: #264591;
    width: 24px;
    height: 24px;
  }

  .title {
    flex: 1;
    text-align: center;
    font-size: 16px;
    color: #264591;
    font-weight: 600;
    margin: 0;
  }

  .mdi {
    color: #26499d;
    width: 24px;
    height: 24px;
  }

  .nextTripIcon {
    width: 22px;
    height: 22px;
    transform: rotate(-90deg);
    transition: transform 0.3s ease;
  }
}
</style>
