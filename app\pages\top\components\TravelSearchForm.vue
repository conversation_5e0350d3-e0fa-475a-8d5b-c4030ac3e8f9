<script lang="ts" setup>
import { ref, computed, provide, watch, onMounted } from 'vue'
import { useDisplay } from 'vuetify'
import { useRoute, useRouter } from 'vue-router'
import { useDateTimeHandler } from '../../../composables/business/useDateTimeHandler'
import {
  useWaypointStore,
  useOriginStore,
  useSpotStore,
  clearAllTouristData,
  useDateTimeStore
} from '~/stores/tourist-facilities'
import { useTabStore } from '~/stores/tabStore'
import { useRouteStore } from '~/stores/bus-service-selection'
/* ストアの初期化 */
const waypointStore = useWaypointStore()
const originStore = useOriginStore()
const spotStore = useSpotStore()
const tabStore = useTabStore()
const dateTimeStore = useDateTimeStore()
const routeStore = useRouteStore()

/* Loading状態管理 */
const isDialogActive = ref(false)

/* フォームデータのインターフェース定義 */
interface OneWayFormData {
  departure: string
  destination: string
  date: string
  time: string
  direction: 'departure' | 'arrival'
  waypoints: Array<{ id: number; location: string }>
}

interface RoundTripFormData {
  departure: string
  destination: string
  outbound: {
    date: string
    time: string
    direction: 'departure' | 'arrival'
  }
  return: {
    date: string
    time: string
    direction: 'departure' | 'arrival'
  }
  waypoints: Array<{ id: number; location: string }>
}

interface ItineraryItem {
  departure: string
  destination: string
  date: string
  time: string
  tripType: 'departure' | 'arrival'
}

interface MyRouteFormData {
  itineraries: ItineraryItem[]
}

interface FormData {
  oneway: OneWayFormData
  roundtrip: RoundTripFormData
  myroute: MyRouteFormData
}

interface SearchData {
  type: string
  [key: string]: any
}

/* コンポーネントのインポート */
import OneWayForm from './OneWayForm.vue'
import RoundTripForm from './RoundTripForm.vue'
// import MyRouteForm from './MyRouteForm.vue'
import DatePicker from '~/components/DatePicker.vue'
import TimePicker from '~/components/TimePicker.vue'
import LoadingSpinner from './LoadingSpinner.vue'

/* 子コンポーネントの参照 */
const oneWayFormRef = ref<InstanceType<typeof OneWayForm> | null>(null)
const roundTripFormRef = ref<InstanceType<typeof RoundTripForm> | null>(null)

/* 初期設定 */
const { mobile } = useDisplay()
const route = useRoute()
const router = useRouter()
const {
  datePickerOpen,
  timePickerOpen,
  currentDateType,
  currentTimeType,
  currentIndex,
  closeDatePicker,
  closeTimePicker
} = useDateTimeHandler()

/* プロパティ定義 */
type TabName = '片道' | '往復' | 'Myルート作成'

interface Props {
  initialDeparture?: string
  initialDestination?: string
  defaultTab?: TabName
}

const props = withDefaults(defineProps<Props>(), {
  initialDeparture: '',
  initialDestination: '',
  defaultTab: '片道'
})

const emit = defineEmits<{
  search: [data: SearchData]
  locationSwap: [data: any]
  waypointToggle: []
  dateSelect: [date: string]
  tabClick: [tab: string]
}>()
const activeTab = computed({
  get(): TabName {
    return tabStore.activeTab
  },
  set(value: TabName) {
    tabStore.setActiveTab(value)
  }
})

/* フォームデータの初期化 */
const formData = ref<FormData>({
  oneway: {
    departure: props.initialDeparture,
    destination: props.initialDestination,
    date: '',
    time: '',
    direction: 'departure',
    waypoints: []
  },
  roundtrip: {
    departure: props.initialDeparture,
    destination: props.initialDestination,
    outbound: {
      date: '',
      time: '',
      direction: 'departure'
    },
    return: {
      date: '',
      time: '',
      direction: 'departure'
    },
    waypoints: []
  },
  myroute: {
    itineraries: [
      {
        departure: '',
        destination: '',
        date: '',
        time: '',
        tripType: 'departure'
      }
    ]
  }
})

/* タブアイコンの取得 */
const getTabIcon = (tabName: string): string | undefined => {
  if (mobile.value) return undefined

  const iconMap: Record<string, string> = {
    片道: 'mdi-arrow-right-thin',
    往復: 'mdi-swap-horizontal',
    Myルート作成: 'mdi-map-marker-path'
  }
  return iconMap[tabName]
}

/* 現在選択されている日付と時刻の計算 */
const currentSelectedDate = computed((): string => {
  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    return formData.value.myroute.itineraries[currentIndex.value]?.date || ''
  }

  switch (currentDateType.value) {
    case 'oneway':
      return formData.value.oneway.date
    case 'outbound':
      return formData.value.roundtrip.outbound.date
    case 'return':
      return formData.value.roundtrip.return.date
    default:
      return ''
  }
})

const currentSelectedTime = computed((): string => {
  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    return formData.value.myroute.itineraries[currentIndex.value]?.time || ''
  }

  switch (currentTimeType.value) {
    case 'oneway':
      return formData.value.oneway.time
    case 'outbound':
      return formData.value.roundtrip.outbound.time
    case 'return':
      return formData.value.roundtrip.return.time
    default:
      return ''
  }
})

/* スイッチすべてのストアデータを消去 */
const handleTabClick = (value: TabName): void => {
  if (value !== activeTab.value) {
    clearAllTouristData()

    activeTab.value = value
    emit('tabClick', value)
  }
}

/* すべてのストアデータを消去 */
watch(
  () => [
    dateTimeStore.selectedDate,
    dateTimeStore.selectedTime,
    dateTimeStore.direction
  ],
  ([newDate, newTime, newDirection]) => {
    formData.value.oneway.date = newDate ?? ''
    formData.value.oneway.time = newTime ?? ''
    formData.value.oneway.direction =
      newDirection === 'departure' || newDirection === 'arrival'
        ? newDirection
        : 'departure'
  },
  { immediate: true, deep: true }
)

/* 往返行程的 outbound 数据同步 */
watch(
  () => [
    dateTimeStore.outbound.date,
    dateTimeStore.outbound.time,
    dateTimeStore.outbound.direction
  ],
  ([newDate, newTime, newDirection]) => {
    formData.value.roundtrip.outbound.date = newDate ?? ''
    formData.value.roundtrip.outbound.time = newTime ?? ''
    formData.value.roundtrip.outbound.direction =
      newDirection === 'departure' || newDirection === 'arrival'
        ? newDirection
        : 'departure'
  },
  { immediate: true, deep: true }
)

/* 往返行程的 return 数据同步 */
watch(
  () => [
    dateTimeStore.return.date,
    dateTimeStore.return.time,
    dateTimeStore.return.direction
  ],
  ([newDate, newTime, newDirection]) => {
    formData.value.roundtrip.return.date = newDate ?? ''
    formData.value.roundtrip.return.time = newTime ?? ''
    formData.value.roundtrip.return.direction =
      newDirection === 'departure' || newDirection === 'arrival'
        ? newDirection
        : 'departure'
  },
  { immediate: true, deep: true }
)

/* タブ変更の監視 */
watch(
  () => tabStore.activeTab,
  (newTab, oldTab) => {
    if (newTab !== oldTab) {
      emit('tabClick', newTab)
    }
  }
)

/* 日付選択時の処理 */
const handleDateSelected = (dateString: string): void => {
  if (!dateString) return

  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    if (formData.value.myroute.itineraries[currentIndex.value]) {
      formData.value.myroute.itineraries[currentIndex.value]!.date = dateString
    }
  } else {
    switch (currentDateType.value) {
      case 'oneway':
        formData.value.oneway.date = dateString
        break
      case 'outbound':
        formData.value.roundtrip.outbound.date = dateString
        if (
          formData.value.roundtrip.return.date &&
          new Date(formData.value.roundtrip.return.date) <= new Date(dateString)
        ) {
          const nextDay = new Date(dateString)
          nextDay.setDate(nextDay.getDate() + 1)
          formData.value.roundtrip.return.date = nextDay
            .toISOString()
            .split('T')[0]!
        }
        break
      case 'return':
        formData.value.roundtrip.return.date = dateString
        break
    }
  }

  emit('dateSelect', dateString)
  closeDatePicker()
}

/* 時刻選択時の処理 */
const handleTimeSelected = (timeString: string): void => {
  if (!timeString) return

  if (currentIndex.value >= 0 && activeTab.value === 'Myルート作成') {
    if (formData.value.myroute.itineraries[currentIndex.value]) {
      formData.value.myroute.itineraries[currentIndex.value]!.time = timeString
    }
  } else {
    switch (currentTimeType.value) {
      case 'oneway':
        formData.value.oneway.time = timeString
        break
      case 'outbound':
        formData.value.roundtrip.outbound.time = timeString
        break
      case 'return':
        formData.value.roundtrip.return.time = timeString
        break
    }
  }

  closeTimePicker()
}

/* 検索ボタンの無効状態の計算 */
const isSearchDisabled = computed(() => {
  if (activeTab.value === '片道') {
    return (
      !formData.value.oneway.departure ||
      !formData.value.oneway.destination ||
      !formData.value.oneway.date ||
      !formData.value.oneway.time ||
      !formData.value.oneway.direction
    )
  } else if (activeTab.value === '往復') {
    return (
      !formData.value.roundtrip.departure ||
      !formData.value.roundtrip.destination ||
      !formData.value.roundtrip.outbound.date ||
      !formData.value.roundtrip.outbound.time ||
      !formData.value.roundtrip.return.date ||
      !formData.value.roundtrip.return.time
    )
  }
  return true
})

/* 統一検索処理 */
const handleUnifiedSearch = async (): Promise<void> => {
  try {
    let result: { valid: boolean; searchData?: SearchData } = { valid: false }

    // 現在のタブに応じて対応する子コンポーネントの検証メソッドを呼び出し
    if (activeTab.value === '片道' && oneWayFormRef.value) {
      result = await oneWayFormRef.value.handleSearch()
    } else if (activeTab.value === '往復' && roundTripFormRef.value) {
      result = await roundTripFormRef.value.handleSearch()
    }

    if (!result.valid) {
      // 検証失敗、エラーメッセージを表示（ここにエラー表示ロジックを追加可能）
      console.warn('フォーム検証に失敗しました')
      return
    }

    if (result.searchData) {
      // loading開始
      isDialogActive.value = true
      
      // 确保数据正确传递到 routeStore
      console.log('TravelSearchForm - 确保数据传递到 routeStore')
      console.log('TravelSearchForm - routeStore 当前状态:', routeStore.routeSelected.routeList)
      
      // 2秒のloading後に遷移をシミュレート
      setTimeout(() => {
        isDialogActive.value = false
        emit('search', {
          type: activeTab.value,
          ...result.searchData
        })
        console.log('result.searchData', result.searchData)
        console.log('activeTab.value', activeTab.value)
        console.log('TravelSearchForm - 跳转前 routeStore 状态:', routeStore.routeSelected.routeList)
      }, 2000)
    }
  } catch (error) {
    console.error('検索処理でエラーが発生しました:', error)
    isDialogActive.value = false
  }
}


/* ロケーションスワップ処理 */
const handleLocationSwap = (swapData: any): void => {
  emit('locationSwap', swapData)
}

/* マウント時の初期化 */
onMounted(() => {
  if (!['片道', '往復', 'Myルート作成'].includes(tabStore.activeTab)) {
    tabStore.setActiveTab(props.defaultTab)
  }
})

/* 子コンポーネントへの提供 */
provide('dateTimeHandler', {
  datePickerOpen,
  timePickerOpen,
  currentDateType,
  currentTimeType,
  currentIndex,
  openDatePicker: (type: string, index: number = -1) => {
    currentDateType.value = type
    currentIndex.value = index
    datePickerOpen.value = true
  },
  openTimePicker: (type: string, index: number = -1) => {
    currentTimeType.value = type
    currentIndex.value = index
    timePickerOpen.value = true
  }
})


</script>

<template>
  <div class="travel-search-wrapper">
    <v-card class="travel-search-card" elevation="8" rounded="xl">
      <div class="flex margin">
        <div class="tabs-wrapper flex">
          <v-btn :color="activeTab === '片道' ? '#26499D' : '#fff'"
            :class="['tab-button', { 'tab-active': activeTab === '片道' }]" :prepend-icon="getTabIcon('片道')"
            @click="handleTabClick('片道')">
            片道
          </v-btn>

          <v-btn :color="activeTab === '往復' ? '#26499D' : '#fff'"
            :class="['tab-button', { 'tab-active': activeTab === '往復' }]" :prepend-icon="getTabIcon('往復')"
            @click="handleTabClick('往復')">
            往復
          </v-btn>
        </div>
        <!-- <v-btn
            :color="activeTab === 'Myルート作成' ? '#26499D' : '#fff'"
            :class="[
            'tab-but tab-button',
            { 'tab-active': activeTab === 'Myルート作成' }
          ]"
            @click="handleTabClick('Myルート作成')"
        >
          <img
              v-if="!mobile"
              :src="ForwardArrowIcon"
              alt="tab-icon"
              class="tab-icon"
          />
          Myルート作成
        </v-btn> -->
      </div>

      <v-card-text class="form-container">
        <OneWayForm 
          v-if="activeTab === '片道'" 
          ref="oneWayFormRef"
          v-model="formData.oneway" 
          trip-type="oneway" 
          @location-swap="handleLocationSwap" 
        />
        <RoundTripForm 
          v-if="activeTab === '往復'" 
          ref="roundTripFormRef"
          v-model="formData.roundtrip" 
          trip-type="roundtrip"
          @location-swap="handleLocationSwap" 
        />
        <!-- <MyRouteForm
          v-if="activeTab === 'Myルート作成'"
          v-model="formData.myroute"
        /> -->
      </v-card-text>

      <!-- 統一検索ボタン -->
      <v-card-actions class="justify-center pa-4">
        <v-btn
          size="large"
          :color="isSearchDisabled ? '#fbe4df' : '#ed785f'"
          variant="flat"
          class="search-button"
          :disabled="isSearchDisabled"
          @click="handleUnifiedSearch"
        >
          検索
          <v-icon icon="mdi-magnify" class="ml-2" />
        </v-btn>
      </v-card-actions>
      <v-overlay v-model="datePickerOpen" class="align-center justify-center">
        <DatePicker :selectedDateValue="currentSelectedDate" @date-selected="handleDateSelected"
          @close="closeDatePicker" />
      </v-overlay>

      <v-overlay v-model="timePickerOpen" class="align-center justify-center">
        <TimePicker :selectedTimeValue="currentSelectedTime" @time-selected="handleTimeSelected"
          @close="closeTimePicker" />
      </v-overlay>
    </v-card>
    
    <!-- LoadingSpinnerコンポーネント -->
    <LoadingSpinner :isActive="isDialogActive" @close="isDialogActive = false" />
  </div>
</template>

<style scoped>
.v-dialog>.v-overlay__content>.v-card>.v-card-text,
.v-dialog>.v-overlay__content>form>.v-card>.v-card-text {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent !important;
}

.tabs-wrapper {
  gap: 8px;
  background-color: #d9d9d9;
  padding: 10px;
  border-radius: 40px;
  margin: 6px 8px 6px 0;
}

button.tab-button.tab-active {
  color: white !important;
  border: 1px solid #000;
}

.tab-button {
  min-width: 140px;
  height: 35px;
  font-size: 12px;
  border: 1px solid #26499d;
  color: #26499d !important;
}

.tab-but {
  padding: 0 8px;
  font-size: 12px;
  border: 1px solid #26499d;
  width: 113px;
  color: #26499d !important;
}

.travel-search-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px;
  background-color: #fff7ec;
}

.travel-search-card {
  width: 100%;
  max-width: 380px;
  background: white !important;
  overflow: hidden;
}

.form-container {
  padding: 24px 20px 20px !important;
}

.tab-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

@media (min-width: 960px) {
  .tab-button {
    font-size: 16px;
    height: 40px;
    width: auto;
    padding: 0 16px;
    min-width: 200px;
    font-weight: 600;
  }

  .tab-button :deep(.v-btn__prepend) {
    font-size: 20px;
  }

  .tab-but {
    font-size: 16px;
    height: 40px;
    width: auto;
    padding: 0 16px;
  }

  .travel-search-card {
    max-width: 60%;
  }

  .tabs-wrapper {
    gap: 20px;
    margin: 6px 20px 6px 0;
  }

  .tab-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
  }
}

.search-button {
  width: 330px !important;
  border-radius: 24px !important;
  height: 50px !important;
  font-size: 16px;
  color: #fff !important;
}

.search-button :deep(.v-btn__prepend) {
  margin-inline-end: 8px;
}

@media (max-width: 959px) {
  .search-button {
    width: 330px !important;
    margin: 0 auto;
  }
}
</style>
