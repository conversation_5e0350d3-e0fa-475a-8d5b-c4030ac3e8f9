export interface TouristSpot {
  spot_id: number
  name: string
  description: string
  notifcation: string
}

export interface TabItem {
  name: string
  id: number
}

export interface Touris {
  spot_id: number
  name: string
  description: string
  notifcation: string
}



export interface BusSchedule {
  date: string
  name: string
  route: string
  departureTime: string
  destination: string
  arrivalTime: string
  platform: string
  status: string
  delayInfo: string
}
