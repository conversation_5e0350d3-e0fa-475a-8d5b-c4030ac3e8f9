import { defineStore } from 'pinia'
import type { LoginData, LoginRes, UserState } from '~/types/login'
import { login as userLogin, logout as apiLogout } from '~/composables/api/useLogin'
import { fetchMe } from '~/composables/api/useMe'
import { useCsrf } from '~/composables/api/useCsrf'
// import { decrypt, encrypt } from '~/utils/crypto'

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    user_name: undefined,
    email: undefined,
    user_id: undefined,
    loggedIn: false
  }),

  getters: {
    userProfile(state: UserState): UserState {
      return { ...state }
    }
  },

  actions: {
    setInfo(partial: LoginRes) {
      this.user_id = partial.userId
      this.user_name = partial.userName
    },

    resetInfo() {
      this.$reset()
    },

    async login(loginForm: LoginData) {
      const result = await userLogin(loginForm)
      const maybe = result?.data as Partial<LoginRes> | undefined
      if (maybe?.userId || maybe?.userName) {
        this.setInfo(maybe as LoginRes)
        this.loggedIn = true
      }
      return result
    },

    async logout() {
      try { 
        await apiLogout() 
      } catch (error) {
        console.error('Logout API failed:', error)
      }
      
      const { clearCsrf } = useCsrf()
      clearCsrf()
      this.resetInfo()
      this.loggedIn = false
    },

    async hydrateFromSession() {
      try {
        const me = await fetchMe()
        this.setInfo({ userId: me.userId, userName: me.userName, email: me.email } as LoginRes)
        this.loggedIn = true
      } catch {
        //  todo 
        // this.loggedIn = false
        this.loggedIn = true
      }
    }
  }
})
