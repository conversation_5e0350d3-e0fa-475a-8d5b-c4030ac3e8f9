<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import DatePicker from './components/DatePicker.vue'
import FilterDialog from './components/FilterDialog.vue'
import StationDetailDialog from '~/components/StationDetailDialog.vue'

definePageMeta({
  footerPlaceholderHeight: 100
})

const router = useRouter()

/* 状態管理 */
const selectedDate = ref('2025-03-21')
const selectedSortType = ref('安い順')
const expandedAmenities = ref<{ [key: number]: boolean }>({})
const expandedSchedules = ref<{ [key: number]: boolean }>({})
const expandedSelectedTrips = ref<{ [key: string]: boolean }>({})
const datePickerOpen = ref(false)
const filterDialogOpen = ref(false)
const stationDetailOpen = ref(false)
const selectedStationName = ref('')

/* 便データ */
interface TripData {
  id: string
  title: string
  operator: string
  departureTime: string
  arrivalTime: string
  duration: string
  price: string
  isSelected: boolean
  isReserved: boolean
  amenities: Array<{
    name: string
    type: 'day' | 'night' | 'normal' | 'premium'
    icon?: string
    iconColor?: string
  }>
  departureStop: string
  arrivalStop: string
  route: string
  badges: Array<{
    type: 'safety' | 'early' | 'comfort'
    label: string
    color: string
  }>
  detailedSchedule: Array<{
    time: string
    stationName: string
    distance: string
    isRecommended?: boolean
    isStart?: boolean
    isEnd?: boolean
  }>
}

const tripData = ref<TripData[]>([
  {
    id: '1',
    title: '備前ウィング大阪線 岡山→大阪',
    operator: '〇〇交通',
    departureTime: '23:05',
    arrivalTime: '07:59',
    duration: '000時間00分',
    price: '000,000',
    isSelected: true,
    isReserved: true,
    amenities: [
      {
        name: '昼行便',
        type: 'day',
        icon: 'mdi-white-balance-sunny',
        iconColor: '#F2B774'
      },
      { name: '4列シート', type: 'normal' },
      { name: 'トイレ付', type: 'normal' },
      { name: '座席指定', type: 'normal' },
      { name: 'Wi-Fi', type: 'normal' },
      { name: '女性安心', type: 'normal' }
    ],
    departureStop: '東京駅八重洲通',
    arrivalStop: 'あべの橋駅',
    route: '岡山→大阪',
    badges: [
      { type: 'safety', label: '安', color: '#ED785F' },
      { type: 'early', label: '早', color: '#49C7DE' },
      { type: 'comfort', label: '良', color: '#BA7CE3' }
    ],
    detailedSchedule: [
      { time: '00:00', stationName: 'バス停名', distance: '14.8 km' },
      { time: '00:00', stationName: 'バス停名', distance: '13.5 km' },
      { time: '00:00', stationName: 'バス停名', distance: '9.5 km' },
      { time: '00:00', stationName: 'バス停名', distance: '8.0 km' },
      {
        time: '23:05',
        stationName: '湊町バスターミナル（OCAT）',
        distance: '徒歩00分',
        isRecommended: true,
        isStart: true
      },
      { time: '00:00', stationName: 'バス停名', distance: '13.0 km' },
      { time: '00:00', stationName: 'バス停名', distance: '' },
      { time: '00:00', stationName: 'バス停名', distance: '' },
      { time: '00:00', stationName: 'バス停名', distance: '' },
      { time: '07:59', stationName: '栄駅', distance: '', isEnd: true }
    ]
  },
  {
    id: '2',
    title: '山陽ブルーライン号 岡山→大阪',
    operator: '〇〇交通',
    departureTime: '23:20',
    arrivalTime: '08:30',
    duration: '000時間00分',
    price: '000,000',
    isSelected: false,
    isReserved: false,
    amenities: [
      {
        name: '昼行便',
        type: 'day',
        icon: 'mdi-white-balance-sunny',
        iconColor: '#F2B774'
      },
      { name: '4列シート', type: 'normal' },
      { name: 'トイレ付', type: 'normal' },
      { name: '座席指定', type: 'normal' },
      { name: 'Wi-Fi', type: 'normal' },
      { name: '女性安心', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXX', type: 'normal' }
    ],
    departureStop: '岡山駅西口',
    arrivalStop: '湊町バスターミナル（OCAT）',
    route: '岡山→大阪',
    badges: [
      { type: 'safety', label: '安', color: '#ED785F' },
      { type: 'comfort', label: '良', color: '#BA7CE3' }
    ],
    detailedSchedule: [
      { time: '00:00', stationName: 'バス停名', distance: '12.3 km' },
      { time: '00:00', stationName: 'バス停名', distance: '11.2 km' },
      {
        time: '23:20',
        stationName: '岡山駅西口',
        distance: '徒歩00分',
        isStart: true
      },
      { time: '00:00', stationName: 'バス停名', distance: '8.5 km' },
      {
        time: '08:30',
        stationName: '湊町バスターミナル（OCAT）',
        distance: '',
        isEnd: true
      }
    ]
  },
  {
    id: '3',
    title: '岡山リバーサイド号 岡山→大阪',
    operator: '〇〇交通',
    departureTime: '23:20',
    arrivalTime: '08:30',
    duration: '000時間00分',
    price: '000,000',
    isSelected: false,
    isReserved: false,
    amenities: [
      {
        name: '昼行便',
        type: 'day',
        icon: 'mdi-white-balance-sunny',
        iconColor: '#F2B774'
      },
      { name: '4列シート', type: 'normal' },
      { name: 'トイレ付', type: 'normal' },
      { name: '座席指定', type: 'normal' },
      { name: 'Wi-Fi', type: 'normal' },
      { name: '女性安心', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXXXXX', type: 'normal' },
      { name: 'XXXXX', type: 'normal' }
    ],
    departureStop: '岡山駅西口',
    arrivalStop: '湊町バスターミナル（OCAT）',
    route: '岡山→大阪',
    badges: [{ type: 'safety', label: '安', color: '#ED785F' }],
    detailedSchedule: [
      { time: '00:00', stationName: 'バス停名', distance: '15.1 km' },
      {
        time: '23:20',
        stationName: '岡山駅西口',
        distance: '徒歩00分',
        isStart: true
      },
      { time: '00:00', stationName: 'バス停名', distance: '10.2 km' },
      {
        time: '08:30',
        stationName: '湊町バスターミナル（OCAT）',
        distance: '',
        isEnd: true
      }
    ]
  }
])

/* 計算プロパティ */
const selectedDateDisplay = computed(() => {
  const date = new Date(selectedDate.value)
  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekdays = ['日', '月', '火', '水', '木', '金', '土']
  const weekday = weekdays[date.getDay()]
  return `${month}/${day}(${weekday})`
})

const selectedYear = computed(() => {
  return new Date(selectedDate.value).getFullYear()
})

const totalAmount = computed(() => {
  const selectedTrip = tripData.value.find((t) => t.isSelected)
  if (selectedTrip) {
    return selectedTrip.price.replace(/,/g, '')
  }
  return '0,000,000'
})

const totalDuration = computed(() => {
  const selectedTrip = tripData.value.find((t) => t.isSelected)
  if (selectedTrip) {
    return selectedTrip.duration
  }
  return '11時間35分'
})

const sortOptions = ['安い順', '到着早い順', '評価良い順']

/* メソッド */
const goBack = () => {
  router.back()
}

const handleDateChange = () => {
  datePickerOpen.value = true
}

const handleDateSelected = (dateString: string) => {
  selectedDate.value = dateString
  datePickerOpen.value = false
}

const closeDatePicker = () => {
  datePickerOpen.value = false
}

const handleFilter = () => {
  filterDialogOpen.value = true
}

const handleApplyFilter = (filters: any) => {
  // 絞り込み処理
  console.log('絞り込み適用:', filters)
  filterDialogOpen.value = false
}

const closeFilterDialog = () => {
  filterDialogOpen.value = false
}

const handleSortChange = (sortType: string) => {
  selectedSortType.value = sortType
  // ソート処理
  console.log('ソート変更:', sortType)
}

const toggleAmenities = (tripIndex: number) => {
  expandedAmenities.value[tripIndex] = !expandedAmenities.value[tripIndex]
}

const getDisplayedAmenities = (amenities: any[], tripIndex: number) => {
  const isExpanded = expandedAmenities.value[tripIndex] || false
  if (isExpanded || amenities.length <= 6) {
    return amenities
  }
  return amenities.slice(0, 6)
}

const handleTempSave = () => {
  // 一時保存処理
  console.log('一時保存')
}

const handleShare = () => {
  // 共有処理
  console.log('共有')
}

const handleCheckAvailability = (tripId: string) => {
  // 空席確認処理
  console.log('空席確認:', tripId)
}

const handleReserveTrip = (tripId: string) => {
  // 便予約処理
  console.log('便予約:', tripId)

  // 選択状態を更新 - 他の便の選択を解除し、選択した便を設定
  tripData.value.forEach((trip) => {
    if (trip.id === tripId) {
      trip.isSelected = true
      trip.isReserved = true
    } else {
      trip.isSelected = false
      trip.isReserved = false
    }
  })

  // 少し遅延してから次のページに遷移（ユーザーが選択を確認できるように）
  setTimeout(() => {
    router.push('/passenger-selection')
  }, 500)
}

const handleChangeReservation = (tripId: string) => {
  // 予約変更処理
  console.log('予約変更:', tripId)
}

const handleShowOtherStops = (tripId: string) => {
  // 他バス停表示処理
  console.log('他バス停表示:', tripId)
  const tripIndex = tripData.value.findIndex((trip) => trip.id === tripId)
  if (tripIndex !== -1) {
    expandedSchedules.value[tripIndex] = !expandedSchedules.value[tripIndex]
  }
}

/* 停留所詳細ダイアログ関連 */
const handleStationClick = (stationName: string) => {
  selectedStationName.value = stationName
  stationDetailOpen.value = true
}

const handleStationDialogGoBack = () => {
  // 戻るボタンの処理（現在のダイアログのみ閉じる）
  stationDetailOpen.value = false
}

const handleStationDialogCloseAll = () => {
  // 閉じるボタンの処理（すべてのダイアログを閉じる）
  stationDetailOpen.value = false
}

const proceedToNextStep = () => {
  // 次のステップに進む処理
  router.push('/passenger-selection')
}

/* 選択中便の展開/収起 */
const toggleSelectedTrip = (tripId: string) => {
  expandedSelectedTrips.value[tripId] = !expandedSelectedTrips.value[tripId]
}

/* 返回顶部按钮 */
const showBackToTop = ref(false)

const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

useHead({
  title: '便選択'
})
</script>

<template>
  <div class="trip-selection-page">
    <!-- ヘッダー -->
    <div class="page-header">
      <div class="header-content">
        <v-btn icon variant="text" @click="goBack" class="back-button">
          <v-icon color="#26499D" size="24">mdi-arrow-left</v-icon>
        </v-btn>
        <div class="header-info">
          <div class="page-title">便選択</div>
        </div>
      </div>
    </div>

    <!-- ガイダンス -->
    <div class="guidance-section">
      <div class="guidance-content">
        <p class="guidance-text">乗車する2便を区間ごとに選択してください。</p>
      </div>
    </div>

    <!-- 選択中ルート -->
    <div class="selected-route-section">
      <div class="route-header">
        <div class="route-title">選択中のルート</div>
        <div class="route-path">
          <span class="route-city">岡山</span>
          <v-icon color="#9CBCD4" size="20">mdi-arrow-right</v-icon>
          <span class="route-city">大阪</span>
          <v-icon color="#9CBCD4" size="20">mdi-arrow-right</v-icon>
          <span class="route-city">東京</span>
        </div>
      </div>

      <div class="route-divider"></div>

      <div class="route-details">
        <div class="transfer-info">
          <div class="transfer-icon">
            <v-icon color="#9CBCD4" size="20">mdi-airplane</v-icon>
          </div>
          <span class="transfer-text">乗継：１回</span>
        </div>
        <div class="discount-badge">
          <span class="discount-text">5%乗継割引適用中</span>
        </div>
      </div>
    </div>

    <!-- アクションボタン -->
    <div class="action-buttons">
      <v-btn
        variant="outlined"
        class="action-btn temp-save-btn"
        @click="handleTempSave"
      >
        <v-icon left color="#26499D" size="24">mdi-content-save</v-icon>
        一時保存
      </v-btn>
      <v-btn
        variant="outlined"
        class="action-btn share-btn"
        @click="handleShare"
      >
        <v-icon left color="#26499D" size="24">mdi-share</v-icon>
        共有
      </v-btn>
    </div>

    <!-- 便選択セクション -->
    <div class="trip-selection-section">
      <!-- 選択済み便 -->
      <div class="selected-trips">
        <div
          v-for="(trip, index) in tripData.filter((t) => t.isSelected)"
          :key="trip.id"
          class="trip-card selected-trip"
        >
          <div class="trip-header selected-header">
            <div class="trip-date-section">
              <div class="trip-date">3/22(金)</div>
              <v-btn
                icon
                variant="outlined"
                size="small"
                class="calendar-btn"
                @click="handleDateChange"
              >
                <v-icon color="#26499D" size="24">mdi-calendar</v-icon>
              </v-btn>
            </div>
            <div class="trip-route-section">
              <span class="route-from">岡山</span>
              <v-icon color="#9CBCD4" size="20">mdi-arrow-right</v-icon>
              <span class="route-to selected-to">大阪</span>
            </div>
            <v-btn
              icon
              variant="text"
              size="small"
              class="expand-btn"
              @click="toggleSelectedTrip(trip.id)"
            >
              <v-icon color="#000000" size="24">
                {{
                  expandedSelectedTrips[trip.id]
                    ? 'mdi-chevron-up'
                    : 'mdi-chevron-down'
                }}
              </v-icon>
            </v-btn>
          </div>

          <!-- 展開時の詳細情報 -->
          <div
            v-if="expandedSelectedTrips[trip.id]"
            class="selected-trip-details"
          >
            <div class="trip-detail-item">
              <span class="detail-date">03/21(金)</span>
              <span class="detail-time">23:20</span>
              <span class="detail-station">岡山駅西口</span>
            </div>
            <div class="trip-detail-item">
              <span class="detail-date">03/22(土)</span>
              <span class="detail-time">08:30</span>
              <span class="detail-station">湊町バスターミナル（OCAT）</span>
            </div>
            <div class="waiting-time">
              <span class="waiting-label">待ち時間：</span>
              <span class="waiting-value">--</span>
            </div>
          </div>

          <div class="selected-status">
            <span class="status-text">便を選び直す</span>
          </div>
        </div>

        <!-- 未選択便 -->
        <div
          v-for="(trip, index) in tripData.filter((t) => !t.isSelected)"
          :key="trip.id"
          class="trip-card unselected-trip"
        >
          <div class="trip-header unselected-header">
            <div class="trip-date-section">
              <div class="trip-date">3/21(金)</div>
              <v-btn
                icon
                variant="outlined"
                size="small"
                class="calendar-btn"
                @click="handleDateChange"
              >
                <v-icon color="#26499D" size="24">mdi-calendar</v-icon>
              </v-btn>
            </div>
            <div class="trip-route-section">
              <span class="route-from">大阪</span>
              <v-icon color="#9CBCD4" size="20">mdi-arrow-right</v-icon>
              <span class="route-to unselected-to">東京</span>
            </div>
            <v-btn icon variant="text" size="small" class="expand-btn">
              <v-icon color="#000000" size="24">mdi-keyboard-arrow-up</v-icon>
            </v-btn>
          </div>
          <div class="unselected-status">
            <span class="status-text">便を選び直す</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 日付・フィルター・ソートセクション -->
    <div class="filter-sort-section">
      <div class="date-filter-container">
        <!-- 日付選択 -->
        <div class="date-selection">
          <div class="date-info">
            <span class="date-label">出発日</span>
            <div class="date-display">
              <div class="year-text">{{ selectedYear }}年</div>
              <div class="date-text">{{ selectedDateDisplay }}</div>
            </div>
          </div>
          <v-btn
            variant="outlined"
            class="date-change-btn"
            @click="handleDateChange"
          >
            <v-icon left color="#26499D" size="20">mdi-calendar</v-icon>
            日付変更
          </v-btn>
        </div>

        <!-- フィルターボタン -->
        <div class="filter-section">
          <v-btn variant="outlined" class="filter-btn" @click="handleFilter">
            <v-icon left color="#26499D" size="24">mdi-filter-variant</v-icon>
            絞り込み
          </v-btn>
        </div>
      </div>

      <!-- ソートタブ -->
      <div class="sort-tabs">
        <div
          v-for="(option, index) in sortOptions"
          :key="option"
          class="sort-tab"
          :class="{ active: selectedSortType === option }"
          @click="handleSortChange(option)"
        >
          <span class="sort-text">{{ option }}</span>
          <div v-if="selectedSortType === option" class="sort-indicator"></div>
        </div>
      </div>
    </div>

    <!-- 便選択カードリスト -->
    <div class="trip-cards-section">
      <div
        v-for="(trip, index) in tripData"
        :key="trip.id"
        class="trip-selection-card"
        :class="{ reserved: trip.isReserved }"
      >
        <!-- 予約済みラベル -->
        <div v-if="trip.isReserved" class="reserved-label">
          <span class="reserved-text">選択中の便</span>
        </div>

        <!-- 便タイトル -->
        <h3 class="trip-title">{{ trip.title }}</h3>

        <!-- 運行会社 -->
        <p class="trip-operator">運行会社：{{ trip.operator }}</p>

        <!-- 設備情報 -->
        <div class="amenities-section">
          <div class="amenities-tags">
            <v-chip
              v-for="(amenity, amenityIndex) in getDisplayedAmenities(
                trip.amenities,
                index
              )"
              :key="amenity.name"
              color="#ffffff"
              :text-color="'#000000'"
              size="small"
              class="amenity-chip"
            >
              <v-icon
                v-if="amenity.icon"
                :color="amenity.iconColor || '#F2B774'"
                size="16"
                start
              >
                {{ amenity.icon }}
              </v-icon>
              {{ amenity.name }}
            </v-chip>
          </div>
          <div
            v-if="trip.amenities.length > 6"
            class="toggle-amenities"
            @click="toggleAmenities(index)"
          >
            {{ expandedAmenities[index] ? '閉じる' : '…すべて見る' }}
          </div>
        </div>

        <div class="trip-divider"></div>

        <!-- 時間・バッジセクション -->
        <div class="time-badge-section">
          <div class="duration-badge">
            <span class="duration-text">{{ trip.duration }}</span>
          </div>
          <div class="quality-badges">
            <div
              v-for="badge in trip.badges"
              :key="badge.type"
              class="quality-badge"
              :style="{ backgroundColor: badge.color }"
            >
              <span class="badge-text">{{ badge.label }}</span>
            </div>
          </div>
        </div>

        <!-- 時刻・バス停情報 -->
        <div class="schedule-section">
          <!-- 基本スケジュール表示 -->
          <div v-if="!expandedSchedules[index]" class="schedule-line">
            <div class="schedule-dots">
              <div class="dot start-dot"></div>
              <div class="connecting-line"></div>
              <div class="dot end-dot"></div>
            </div>
            <div class="schedule-info">
              <div class="departure-info">
                <div class="time-text">{{ trip.departureTime }}</div>
                <div class="stop-info">
                  <div
                    class="stop-name"
                    @click="handleStationClick(trip.departureStop)"
                  >
                    {{ trip.departureStop }}
                  </div>
                  <div class="stop-label">始発バス停</div>
                </div>
              </div>
              <div class="arrival-info">
                <div class="time-text">{{ trip.arrivalTime }}</div>
                <div class="stop-info">
                  <div class="stop-label">終着バス停</div>
                  <div
                    class="stop-name"
                    @click="handleStationClick(trip.arrivalStop)"
                  >
                    {{ trip.arrivalStop }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 詳細スケジュール表示 -->
          <div v-else class="detailed-schedule">
            <div class="detailed-schedule-header">
              <span class="schedule-title">経路詳細</span>
            </div>
            <div class="detailed-schedule-list">
              <div
                v-for="(stop, stopIndex) in trip.detailedSchedule"
                :key="stopIndex"
                class="schedule-stop-item"
                :class="{
                  'recommended-stop': stop.isRecommended,
                  'start-stop': stop.isStart,
                  'end-stop': stop.isEnd
                }"
              >
                <div class="stop-timeline">
                  <div
                    class="stop-dot"
                    :class="{
                      'start-dot': stop.isStart,
                      'end-dot': stop.isEnd,
                      'recommended-dot': stop.isRecommended
                    }"
                  ></div>
                  <div
                    v-if="stopIndex < trip.detailedSchedule.length - 1"
                    class="timeline-line"
                  ></div>
                </div>
                <div class="stop-details">
                  <div class="stop-time">{{ stop.time }}</div>
                  <div class="stop-info-detailed">
                    <div v-if="stop.isRecommended" class="recommended-label">
                      おすすめ乗車バス停
                    </div>
                    <div v-if="stop.isEnd" class="end-label">終着バス停</div>
                    <div
                      class="stop-name-detailed"
                      :class="{
                        'recommended-name':
                          stop.isRecommended || stop.isStart || stop.isEnd
                      }"
                    >
                      {{ stop.stationName }}
                    </div>
                    <div v-if="stop.isRecommended" class="walk-info">
                      <span class="walk-time">{{ stop.distance }}</span>
                      <v-icon color="#26499D" size="16" class="walk-icon">
                        mdi-walk
                      </v-icon>
                    </div>
                  </div>
                </div>
                <div
                  v-if="stop.distance && !stop.isRecommended"
                  class="stop-distance"
                >
                  {{ stop.distance }}
                </div>
              </div>
            </div>
          </div>

          <div v-if="!expandedSchedules[index]" class="schedule-duration-badge">
            <span class="schedule-duration-text">{{ trip.duration }}</span>
          </div>
        </div>

        <!-- 他バス停ボタン -->
        <div class="other-stops-section">
          <v-btn
            variant="outlined"
            class="other-stops-btn"
            @click="handleShowOtherStops(trip.id)"
          >
            <v-icon left color="#26499D" size="16">
              {{
                expandedSchedules[index] ? 'mdi-chevron-up' : 'mdi-chevron-down'
              }}
            </v-icon>
            {{ expandedSchedules[index] ? '閉じる' : '他バス停' }}
          </v-btn>
        </div>

        <div class="trip-divider"></div>

        <!-- 料金・アクションセクション -->
        <div class="price-action-section">
          <div class="price-info">
            <div class="price-label">1人あたりの大人料金</div>
            <div class="price-amount">{{ trip.price }} 円</div>
          </div>
          <div class="action-buttons-section">
            <v-btn
              variant="outlined"
              class="availability-btn"
              @click="handleCheckAvailability(trip.id)"
            >
              空席
              <span>100</span>
            </v-btn>
            <v-btn
              v-if="trip.isReserved"
              class="change-btn"
              @click="handleChangeReservation(trip.id)"
            >
              内容を変更
            </v-btn>
            <v-btn
              v-else
              class="reserve-btn"
              @click="handleReserveTrip(trip.id)"
            >
              この便を予約
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <div v-show="showBackToTop" class="back-to-top-btn" @click="scrollToTop">
      <div class="back-to-top-icon">
        <div class="icon-line"></div>
        <div class="icon-arrow">
          <div class="arrow-stem"></div>
        </div>
      </div>
    </div>

    <!-- 底部固定容器 -->
    <div class="bottom-fixed-container">
      <div class="bottom-content">
        <div class="total-summary">
          <div class="total-amount-section">
            <div class="total-fixed">
              <div class="total-title">合計金額</div>
              <div class="total-amount-display">{{ totalAmount }}</div>
              <div class="total-title">円</div>
            </div>
            <div class="total-note">※便を選択すると表示されます</div>
            <div class="total-time">合計時間: {{ totalDuration }}</div>
          </div>
        </div>
        <div class="confirm-button-section">
          <v-btn
            class="confirm-btn"
            :class="{ disabled: !tripData.some((t) => t.isSelected) }"
            :disabled="!tripData.some((t) => t.isSelected)"
            @click="proceedToNextStep"
          >
            予約確認
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 日付選択ダイアログ -->
    <v-overlay
      v-model="datePickerOpen"
      class="align-center justify-center"
      @click:outside="closeDatePicker"
    >
      <DatePicker
        :selectedDateValue="selectedDate"
        @date-selected="handleDateSelected"
        @close="closeDatePicker"
      />
    </v-overlay>

    <!-- フィルターダイアログ -->
    <v-overlay
      v-model="filterDialogOpen"
      class="align-center justify-center"
      @click:outside="closeFilterDialog"
    >
      <FilterDialog
        @apply-filter="handleApplyFilter"
        @close="closeFilterDialog"
      />
    </v-overlay>

    <!-- 停留所詳細ダイアログ -->
    <StationDetailDialog
      v-model="stationDetailOpen"
      :station-name="selectedStationName"
      @go-back="handleStationDialogGoBack"
      @close-all="handleStationDialogCloseAll"
    />
  </div>
</template>

<style scoped>
.trip-selection-page {
  background-color: #ffffff;
  min-height: 100vh;
}

/* ページヘッダー */
.page-header {
  background-color: #ffffff;
  border-bottom: 0.5px solid #dfdfdf;
  padding: 12px 16px;
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  max-width: 343px;
  margin: 0 auto;
}

.back-button {
  position: absolute;
  left: 0;
  width: 24px;
  height: 24px;
  min-width: 24px;
}

.header-info {
  text-align: center;
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}

/* ガイダンスセクション */
.guidance-section {
  background-color: #ffffff;
  padding: 8px 16px;
}

.guidance-content {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 343px;
  margin: 0 auto;
}

.guidance-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #ed785f;
  margin: 0;
  text-align: center;
}

/* 選択中ルートセクション */
.selected-route-section {
  background-color: #ffffff;
  border-bottom: 1px solid #dfdfdf;
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
}

.route-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 200px;
}

.route-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  color: #7d7d7d;
}

.route-path {
  display: flex;
  align-items: center;
  gap: 2px;
  flex-wrap: wrap;
}

.route-city {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.375;
  color: #000000;
}

.route-divider {
  width: 1px;
  height: 36px;
  background-color: #9cbcd4;
  margin: 8px 0;
}

.route-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.transfer-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.transfer-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transfer-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.571;
  color: #232323;
}

.discount-badge {
  background-color: #ffe1e1;
  border-radius: 19px;
  padding: 2px 8px;
}

.discount-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #d00000;
}

/* アクションボタン */
.action-buttons {
  display: flex;
  gap: 8px;
  padding: 0px 16px;
  margin-bottom: 16px;
  margin-top: 10px;
}

.action-btn {
  height: 32px !important;
  padding: 6px 16px !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  background-color: #e7f2fa !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  text-transform: none !important;
  color: #26499d !important;
  flex: 1;
}

/* 便選択セクション */
.trip-selection-section {
  padding: 0 16px;
  margin-bottom: 16px;
}

.selected-trips {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trip-card {
  border-radius: 5px;
  overflow: hidden;
}

.selected-trip {
  border: 4px solid #26499d;
  border-bottom: none;
}

.unselected-trip {
  border: 1px solid #9cbcd4;
  border-bottom: none;
}

.trip-header {
  padding: 12px 12px 8px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.selected-header {
  background-color: #e7f2fa;
}

.unselected-header {
  background-color: #ffffff;
}

.trip-date-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trip-date {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.333;
  color: #000000;
  width: 80px;
}

.calendar-btn {
  width: 32px !important;
  height: 32px !important;
  border: 1px solid #9cbcd4 !important;
  background-color: #ffffff !important;
}

.trip-route-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.route-from {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
}

.route-to {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
}

.selected-to {
  color: #26499d;
}

.unselected-to {
  color: #26499d;
}

.expand-btn {
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
}

.selected-status {
  background-color: #26499d;
  border-radius: 0px 0px 5px 5px;
  padding: 16px 10px;
  text-align: center;
}

.unselected-status {
  background-color: #e7f2fa;
  border: 1px solid #9cbcd4;
  border-radius: 0px 0px 5px 5px;
  padding: 16px 10px;
  text-align: center;
}

.status-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
}

.selected-status .status-text {
  color: #ffffff;
}

.unselected-status .status-text {
  color: #000000;
}

/* 選択済み便の詳細情報 */
.selected-trip-details {
  background-color: #ffffff;
  padding: 16px 12px;
  border-top: 1px solid #dfdfdf;
}

.trip-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.trip-detail-item:last-of-type {
  margin-bottom: 12px;
}

.detail-date {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  width: 80px;
}

.detail-time {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
  color: #000000;
  width: 60px;
}

.detail-station {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  flex: 1;
}

.waiting-time {
  display: flex;
  align-items: center;
  gap: 4px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.waiting-label {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #7d7d7d;
}

.waiting-value {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
}

/* 日付・フィルター・ソートセクション */
.filter-sort-section {
  background-color: #e7f2fa;
  border-top: 1px solid #9cbcd4;
  padding: 12px 0px 24px;
}

.date-filter-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 0 16px;
  margin-bottom: 20px;
}

.date-selection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 14px;
  width: 342px;
}

.date-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-label {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
}

.date-display {
  background-color: #ffffff;
  border-radius: 5px;
  padding: 4px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.year-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #000000;
}

.date-text {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.364;
  color: #000000;
}

.date-change-btn {
  height: 32px !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  background-color: #e7f2fa !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  text-transform: none !important;
  color: #26499d !important;
}

.filter-section {
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.filter-btn {
  background-color: #ffffff !important;
  width: 160px !important;
  height: 50px !important;
  padding: 12px 16px !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  text-transform: none !important;
  color: #000000 !important;
}

.sort-tabs {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px 6px;
  position: relative;
}

.sort-tab {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 10px;
  flex: 1;
  height: 48px;
  cursor: pointer;
  position: relative;
  background-color: #fff;
}

.sort-tab.active {
  background-color: #acd1ed;
}

.sort-text {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.375;
  color: #272727;
}

.sort-indicator {
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 27.5px;
  height: 21.81px;
  background-color: #acd1ed;
  clip-path: polygon(50% 100%, 0 0, 100% 0);
}

.sort-divider {
  position: absolute;
  right: 0;
  top: 8px;
  width: 0;
  height: 32px;
  border-right: 2px solid #7d7d7d;
}

/* 便選択カードリスト */
.trip-cards-section {
  background-color: #e7f2fa;
  padding: 0px 0px 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.trip-selection-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 16px 12px;
  width: 343px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reserved-label {
  margin-bottom: 4px;
}

.reserved-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #26499d;
}

.trip-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  margin: 0;
  width: 319px;
}

.trip-operator {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.571;
  color: #000000;
  margin: 0;
  width: 319px;
}

.amenities-section {
  position: relative;
  width: 319px;
}

.amenities-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.amenity-chip {
  border: 1px solid #7d7d7d !important;
  border-radius: 26px !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  background-color: #ffffff !important;
}

.toggle-amenities {
  position: absolute;
  right: 0;
  top: 40px;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.2;
  color: #26499d;
  text-align: right;
  cursor: pointer;
  user-select: none;
}

.toggle-amenities:hover {
  color: #1e3a7a;
}

.trip-divider {
  width: 319px;
  height: 1px;
  background-color: #d9d9d9;
}

.time-badge-section {
  display: flex;
  align-items: center;
  width: 319px;
  margin: 8px 0;
}

.duration-badge {
  background-color: #e7f2fa;
  border-radius: 19px;
  padding: 2px 8px;
  white-space: nowrap;
  flex-shrink: 0;
}

.duration-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #26499d;
}

.quality-badges {
  display: flex;
  align-items: center;
  gap: 2px;
}

.quality-badge {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.badge-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #ffffff;
}

.schedule-section {
  width: 319px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 0px 0px 8px;
}

/* 詳細スケジュール */
.detailed-schedule {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detailed-schedule-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #d9d9d9;
}

.schedule-title {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.detailed-schedule-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.schedule-stop-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  position: relative;
}

.stop-timeline {
  position: relative;
  width: 12px;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.stop-dot {
  width: 12px;
  height: 12px;
  border: 2px solid #9cbcd4;
  border-radius: 50%;
  background-color: #ffffff;
  z-index: 2;
  position: relative;
}

.stop-dot.start-dot,
.stop-dot.end-dot,
.stop-dot.recommended-dot {
  border-color: #26499d;
  background-color: #26499d;
}

.timeline-line {
  position: absolute;
  top: 12px;
  left: 5px;
  width: 2px;
  height: 32px;
  background-color: #9cbcd4;
  z-index: 1;
}

.stop-details {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.stop-time {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  width: 60px;
  flex-shrink: 0;
}

.stop-info-detailed {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.recommended-label {
  font-size: 12px;
  font-weight: 400;
  color: #26499d;
  line-height: 1.2;
}

.end-label {
  font-size: 12px;
  font-weight: 400;
  color: #26499d;
  line-height: 1.2;
}

.stop-name-detailed {
  font-size: 14px;
  font-weight: 400;
  color: #000000;
  line-height: 1.2;
}

.stop-name-detailed.recommended-name {
  color: #26499d;
  font-weight: 500;
  text-decoration: underline;
  cursor: pointer;
}

.walk-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
}

.walk-time {
  font-size: 12px;
  font-weight: 400;
  color: #26499d;
}

.walk-icon {
  margin-left: 2px;
}

.stop-distance {
  font-size: 14px;
  font-weight: 400;
  color: #7d7d7d;
  width: 60px;
  text-align: right;
  flex-shrink: 0;
}

.schedule-stop-item.recommended-stop {
  background-color: #f8fbff;
  border-radius: 4px;
  padding: 12px 8px;
  margin: 0 -8px;
}

.schedule-stop-item.start-stop .stop-time,
.schedule-stop-item.end-stop .stop-time {
  font-size: 20px;
  font-weight: 700;
}

.schedule-stop-item.recommended-stop .stop-time {
  font-size: 20px;
  font-weight: 700;
}

.schedule-line {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex: 1;
}

.schedule-duration-badge {
  padding: 2px 8px;
  background-color: #e7f2fa;
  border-radius: 19px;
  white-space: nowrap;
  flex-shrink: 0;
  margin-top: 5px;
}

.schedule-duration-text {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #26499d;
}

.schedule-dots {
  position: relative;
  width: 12px;
  height: 51px;
  flex-shrink: 0;
}

.connecting-line {
  position: absolute;
  left: 5px;
  top: 11px;
  width: 2px;
  height: 34px;
  background-color: #9cbcd4;
}

.dot {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #9cbcd4;
  border-radius: 50%;
  background-color: #ffffff;
}

.start-dot {
  top: 0;
}

.end-dot {
  bottom: 0;
}

.schedule-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 180px;
  flex: 1;
}

.departure-info,
.arrival-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-text {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.364;
  color: #000000;
  width: 60px;
}

.stop-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.stop-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  color: #26499d;
  text-decoration: underline;
  cursor: pointer;
}

.stop-name:hover {
  color: #1e3a7a;
}

.stop-label {
  font-size: 10px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
}

.other-stops-section {
  width: 319px;
  height: 40px;
  position: relative;
}

.other-stops-btn {
  position: absolute;
  right: 0;
  top: 0;
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 29px !important;
  padding: 10px !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  text-transform: none !important;
  color: #26499d !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.price-action-section {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 319px;
}

.price-info {
  width: 115px;
  height: 47px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.784px;
}

.price-label {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #232323;
}

.price-amount {
  font-size: 22px;
  font-weight: 700;
  line-height: 1.364;
  color: #000000;
}

.action-buttons-section {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.availability-btn {
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  padding: 12px 6px !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  text-transform: none !important;
  color: #26499d !important;
  width: 76px !important;
  height: 48px !important;
}

.reserve-btn,
.change-btn {
  background-color: #26499d !important;
  border-radius: 4px !important;
  padding: 14px 16px !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  text-transform: none !important;
  color: #ffffff !important;
  width: 110px !important;
  height: 48px !important;
}

.change-btn {
  background-color: #26499d !important;
}

/* 底部固定容器 */
.bottom-fixed-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1px solid #dfdfdf;
  box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.bottom-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
}

.total-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.total-amount-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.total-title {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.333;
  color: #000000;
}

.total-amount-display {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  margin: 4px 0;
  margin-left: 7px;
}

.total-note {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.2;
  color: #7d7d7d;
}

.total-time {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.375;
  color: #000000;
}

.confirm-button-section {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.confirm-btn {
  width: 120px;
  height: 48px;
  background-color: #26499d !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  color: #ffffff !important;
  text-transform: none !important;
  box-shadow: 0px 2px 4px 0px rgba(38, 73, 157, 0.3) !important;
}

.confirm-btn:hover {
  background-color: #1e3a7a !important;
  box-shadow: 0px 4px 8px 0px rgba(38, 73, 157, 0.4) !important;
}

/* 置灰状态的按钮 */
.confirm-btn.disabled,
.confirm-btn:disabled {
  background-color: #c4c4c4 !important;
  color: #7d7d7d !important;
  box-shadow: none !important;
  cursor: not-allowed !important;
  opacity: 1 !important;
}

.confirm-btn.disabled:hover,
.confirm-btn:disabled:hover {
  background-color: #c4c4c4 !important;
  color: #7d7d7d !important;
  box-shadow: none !important;
  transform: none !important;
}

.total-fixed {
  display: flex;
  align-items: center;
}

/* 返回顶部按钮 */
.back-to-top-btn {
  position: fixed;
  bottom: 120px;
  right: 0px;
  width: 45px;
  height: 45px;
  background-color: #26499d;
  border: 1px solid #9cbcd4;
  border-right: none;
  border-radius: 5px 0px 0px 5px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.back-to-top-btn:hover {
  background-color: #1e3a7a;
  transform: translateY(-2px);
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.3);
}

.back-to-top-btn:active {
  transform: translateY(0);
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
}

.back-to-top-icon {
  position: relative;
  width: 23.33px;
  height: 26.25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon-line {
  width: 20px;
  height: 3px;
  background-color: #ffffff;
  border-radius: 1.5px;
  margin-bottom: 4px;
}

.icon-arrow {
  position: relative;
  width: 16px;
  height: 16px;
}

.icon-arrow::before,
.icon-arrow::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 10px;
  background-color: #ffffff;
  border-radius: 1.5px;
}

.icon-arrow::before {
  transform: rotate(45deg);
  left: 3px;
  top: 2px;
}

.icon-arrow::after {
  transform: rotate(-45deg);
  right: 3px;
  top: 2px;
}

.icon-arrow .arrow-stem {
  position: absolute;
  width: 3px;
  height: 12px;
  background-color: #ffffff;
  border-radius: 1.5px;
  left: 50%;
  top: 6px;
  transform: translateX(-50%);
}

/* レスポンシブ対応 */
@media (min-width: 768px) {
  .guidance-content,
  .date-filter-container,
  .trip-cards-section {
    max-width: 100%;
  }

  .trip-selection-card {
    width: 100%;
    max-width: 600px;
  }
}

:deep(.v-btn) {
  text-transform: none !important;
}

:deep(.v-btn--variant-outlined) {
  border-width: 1px !important;
}

:deep(.v-chip__content) {
  color: #000000 !important;
}
</style>
