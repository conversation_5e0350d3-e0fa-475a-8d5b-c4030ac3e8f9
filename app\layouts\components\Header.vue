<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { isLogin } from '~/utils/auth'
import { useUserStore } from '~/stores/login'
import headerImageUrl from '~/assets/image/header.png'
import menuImageUrl from '~/assets/image/Menu.png'
import type { MenuItem } from '~/types/header'

/* *
 * ルーターの初期化
 * */
const router = useRouter()

/* *
 * ストアの初期化
 * */
const userStore = useUserStore()

/* *
 * 状態管理
 * */
const leftDrawer = ref(false)
const rightDrawer = ref(false)
const isLoggedIn = computed(() => isLogin())

/* *
 * 静的リソース
 * */
const headerImage = headerImageUrl
const menuImage = menuImageUrl

/* *
 * フィルタリングされたメニューアイテム
 * */
const filteredLeftMenuItems = computed(() =>
  getFilteredMenuItems(leftMenuItems, isLoggedIn.value)
)

const filteredUserMenuItems = computed(() =>
  getFilteredMenuItems(userMenuItems, isLoggedIn.value)
)

/* *
 * 左ドロワーのトグル処理
 * */
const toggleLeftDrawer = () => {
  if (rightDrawer.value) {
    rightDrawer.value = false
  }
  leftDrawer.value = !leftDrawer.value
}

/* *
 * 右ドロワーのトグル処理
 * */
const toggleRightDrawer = () => {
  if (isLoggedIn.value) {
    if (leftDrawer.value) {
      leftDrawer.value = false
    }
    rightDrawer.value = !rightDrawer.value
  } else {
    router.push('/login')
  }
}

/* *
 * メニューアイテムクリック処理
 * */
const handleMenuItemClick = (item: MenuItem) => {
  if (!item.children) {
    if (item.action) {
      item.action()
    } else if (item.path) {
      router.push(item.path)
      leftDrawer.value = false
      rightDrawer.value = false
    }
  }
}

/* *
 * ログアウト処理
 * */
const handleLogout = async () => {
  await userStore.logout()
  leftDrawer.value = false
  rightDrawer.value = false
  // Redirect to login page or home page after logout
  router.push('/login')
}

/* *
 * ホームページへのナビゲーション
 * */
const navigateToHome = () => {
  router.push('/')
}

/* *
 * FAQページへのナビゲーション
 * */
const navigateToFAQ = () => {
  router.push('/faq')
}

/* *
 * お問い合わせページへのナビゲーション
 * */
const navigateToContact = () => {
  router.push('/contact')
}

/* *
 * メニューアイテムのフィルタリング
 * */
const getFilteredMenuItems = (
  items: MenuItem[],
  isLoggedIn: boolean
): MenuItem[] => {
  return items.filter((item) => {
    if (item.hide) return false
    return true
  })
}

/* *
 * 左メニューアイテム設定
 * */
const leftMenuItems: MenuItem[] = [
  // {
  //   id: 'for-beginners',
  //   title: '初めての方へ',
  //   children: [
  //     {
  //       id: 'about-konemobi',
  //       title: 'コネモビとは',
  //       path: '/about'
  //     },
  //     {
  //       id: 'reservation-flow',
  //       title: '予約から乗車までの流れ',
  //       path: '/reservation-flow'
  //     },
  //     {
  //       id: 'member-service',
  //       title: '会員サービス',
  //       path: '/member-service'
  //     }
  //   ]
  // },
  {
    id: 'highway-bus-search',
    title: '高速バス検索',
    children: [
      // {
      //   id: 'special-products',
      //   title: 'お得な商品',
      //   path: '/special-products'
      // },
      {
        id: 'tourist-facilities',
        title: '観光地・施設から検索',
        path: '/tourist-facilities'
      },
      {
        id: 'special-events',
        title: '期間限定・イベントから検索',
        path: '/special-events'
      }
    ]
  },
  // {
  //   id: 'campaign',
  //   title: 'キャンペーン',
  //   path: '/campaign'
  // },
  {
    id: 'notifications',
    title: 'お知らせ',
    path: '/notifications'
  },
  {
    id: 'operation-status',
    title: '運行状況',
    path: '/operation-status'
  },
  // {
  //   id: 'inquiry',
  //   title: 'お問い合わせ',
  //   children: [
  //     {
  //       id: 'faq',
  //       title: 'FAQ',
  //       path: '/faq'
  //     },
  //     {
  //       id: 'member-bus-companies',
  //       title: '加盟バス会社一覧',
  //       path: '/member-bus-companies'
  //     },
  //     {
  //       id: 'operating-company',
  //       title: '運営会社（連絡先）',
  //       path: '/operating-company'
  //     }
  //   ]
  // }
]

/* *
 * 右ユーザーメニュー設定
 * */
const userMenuItems: MenuItem[] = [
  {
    id: 'appointment-confirm-change',
    title: '予約確認・変更・キャンセル',
    icon: 'mdi-calendar-edit',
    path: '/appointment-confirm-change'
  },
  // {
  //   id: 'saved-routes',
  //   title: '一時保存した型を見る',
  //   icon: 'mdi-bookmark-outline',
  //   path: '/saved-routes'
  // },
  // {
  //   id: 'favorite-routes',
  //   title: 'お気に入りルート',
  //   icon: 'mdi-heart-outline',
  //   path: '/favorite-routes'
  // },
  // {
  //   id: 'bus-navi-map',
  //   title: 'バスなびマップ',
  //   icon: 'mdi-map-outline',
  //   path: '/bus-navi-map'
  // },
  {
    id: 'coupon-list',
    title: 'クーポン一覧',
    icon: 'mdi-ticket-percent-outline',
    path: '/coupon-list'
  },
  {
    id: 'registration-info',
    title: '登録情報確認・変更',
    icon: 'mdi-account-edit-outline',
    children: [
      {
        id: 'member-info',
        title: '会員情報',
        path: '/member-info'
      },
      // {
      //   id: 'favorite-bus-stops',
      //   title: 'お気に入りバス停',
      //   path: '/favorite-bus-stops'
      // },
      {
        id: 'favorite-bus-companies',
        title: 'お気に入りバス会社',
        path: '/favorite-bus-companies'
      }
    ]
  }
]
</script>

<template>
  <div>
    <!-- ヘッダーナビゲーションバー -->
    <v-app-bar
      :elevation="0"
      color="white"
      height="50"
      fixed
      app
      class="appBarShadow"
    >
      <v-container class="header" fluid>
        <v-row align="center" no-gutters>
          <!-- 左メニューボタン -->
          <v-col
            xs="1"
            sm="2"
            md="2"
            lg="2"
            xl="2"
            xxl="2"
            class="header-container"
          >
            <v-btn
              icon
              variant="text"
              size="small"
              @click="toggleLeftDrawer"
              class="menu-button"
            >
              <img :src="menuImage" alt="menu" class="logo-menu" />
            </v-btn>
          </v-col>

          <!-- ロゴ -->
          <v-col
            xs="10"
            sm="6"
            md="6"
            lg="6"
            xl="6"
            xxl="6"
            class="logo-section"
            @click="navigateToHome"
          >
            <img :src="headerImage" alt="コネモビ" class="logo-image clickable-logo" />
          </v-col>

          <!-- 右ボタンエリア -->
          <v-col
            xs="1"
            sm="4"
            md="4"
            lg="4"
            xl="4"
            xxl="4"
            class="d-flex justify-end d-md-inline"
            style="text-align: end"
          >
            <span class="text d-none d-md-inline" @click="navigateToFAQ">
              よくある質問
            </span>
            <span class="text d-none d-md-inline" @click="navigateToContact">
              お問い合わせ
            </span>
            <v-btn
              v-if="!isLoggedIn"
              @click="toggleRightDrawer"
              rounded
              color="primary"
              variant="flat"
              size="x-small"
            >
              ログイン
            </v-btn>
            <v-btn
              v-else
              size="x-small"
              prepend-icon="mdi-account"
              @click="toggleRightDrawer"
              stacked
              color="primary"
              style="text-transform: none !important; font-weight: 700"
            >
              Myメニュー
            </v-btn>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>

    <!-- 左ドロワーメニュー -->
    <v-navigation-drawer
      v-model="leftDrawer"
      location="left"
      temporary
      width="300"
      class="left-drawer"
      active-color="primary"
    >
      <v-divider></v-divider>

      <v-list nav density="compact">
        <template v-for="(item, index) in filteredLeftMenuItems" :key="item.id">
          <v-divider v-if="index > 0" class="my-1"></v-divider>

          <!-- 直接リンクアイテム -->
          <v-list-item
            v-if="!item.children"
            :prepend-icon="item.icon"
            :title="item.title"
            @click="handleMenuItemClick(item)"
            class="drawer-item direct-link"
          ></v-list-item>

          <!-- グループアイテム -->
          <v-list-group v-else :value="item.id" class="menu-group">
            <template v-slot:activator="{ props }">
              <v-list-item
                v-bind="props"
                :prepend-icon="item.icon"
                :title="item.title"
                class="drawer-item group-header"
                append-icon="mdi-chevron-down"
              ></v-list-item>
            </template>
            <!-- サブメニューアイテム -->
            <v-list-item
              v-for="child in item.children"
              :key="child.id"
              :title="child.title"
              @click="handleMenuItemClick(child)"
              class="drawer-sub-item"
            ></v-list-item>
          </v-list-group>
        </template>
      </v-list>
    </v-navigation-drawer>

    <!-- 右ドロワーメニュー -->
    <v-navigation-drawer
      v-model="rightDrawer"
      location="right"
      temporary
      width="300"
      class="right-drawer"
    >
      <v-divider></v-divider>
      <v-list nav density="compact">
        <template v-for="(item, index) in filteredUserMenuItems" :key="item.id">
          <v-divider v-if="index > 0" class="my-1"></v-divider>
          <!-- 直接リンクアイテム -->
          <v-list-item
            v-if="!item.children"
            :title="item.title"
            @click="handleMenuItemClick(item)"
            class="drawer-item direct-link"
          ></v-list-item>

          <!-- グループアイテム -->
          <v-list-group v-else :value="item.id" class="menu-group">
            <template v-slot:activator="{ props }">
              <v-list-item
                v-bind="props"
                :title="item.title"
                class="drawer-item group-header"
                append-icon="mdi-chevron-down"
              ></v-list-item>
            </template>

            <!-- サブメニューアイテム -->
            <v-list-item
              v-for="child in item.children"
              :key="child.id"
              :title="child.title"
              @click="handleMenuItemClick(child)"
              class="drawer-sub-item"
            ></v-list-item>
          </v-list-group>
        </template>

        <!-- ログアウトオプション -->
        <template v-if="isLoggedIn">
          <v-divider class="my-2"></v-divider>
          <v-list-item
            title="ログアウト"
            @click="handleLogout"
            class="drawer-item logout-item direct-link"
          ></v-list-item>
        </template>
      </v-list>
    </v-navigation-drawer>
  </div>
</template>

<style scoped lang="scss">
/* ヘッダースタイル */
.header-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0 0;
  padding: 12px 16px;
}

.logo-menu {
  width: 26px;
  height: 26px;
}

.logo-section {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  .logo-image {
    height: 26px;
  }
}

.clickable-logo {
  cursor: pointer;
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 0.8;
  }
}

.menu-button {
  margin-right: 16px;
}

.text {
  font-size: 14px;
  margin-right: 16px;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #26499d;
  }
}

.login-button {
  line-height: 0;
  width: 70px;
  border-radius: 50px;
  font-size: 12px;
}

/* ドロワースタイル */
.left-drawer,
.right-drawer {
  :deep(.direct-link) {
    transition: all 0.2s ease;
    cursor: pointer;

    .v-list-item-title {
      font-weight: 700 !important;
      font-size: 14px !important;
    }

    &:hover {
      background-color: rgba(38, 73, 157, 0.1);
    }
  }

  :deep(.group-header) {
    transition: all 0.2s ease;
    cursor: pointer;
    font-weight: 700 !important;

    &:hover {
      background-color: rgba(38, 73, 157, 0.05);
    }

    .v-list-item-title {
      font-weight: 700 !important;
      font-size: 14px !important;
    }
  }

  .drawer-sub-item {
    padding: 8px 20px 8px 50px;
    font-size: 16px;
    transition: all 0.2s ease;
    cursor: pointer;
    color: rgb(var(--v-theme-primary));

    &:hover {
      background-color: rgba(38, 73, 157, 0.08);
    }
  }

  .menu-group {
    .v-list-group__items {
      .v-list-item {
        padding-left: 0;
      }
    }
  }

  .logout-item {
    color: #d32f2f;

    &:hover {
      background-color: rgba(211, 47, 47, 0.1);
    }
  }

  .v-list-group--active {
    .group-header {
      .v-list-item__append {
        .v-icon {
          transform: rotate(180deg);
          transition: transform 0.2s ease;
        }
      }
    }
  }

  .group-header {
    .v-list-item__append {
      .v-icon {
        transition: transform 0.2s ease;
      }
    }
  }
}

/* レスポンシブデザイン */
@media (max-width: 768px) {
  :deep(.v-container--fluid) {
    max-width: 100%;
  }

  .left-drawer,
  .right-drawer {
    width: 280px !important;
  }
}
</style>
