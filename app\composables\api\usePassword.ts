import type { PasswordChangeResponse } from '~/types/password'

export const usePassword = () => {
  const changePassword = async (
    currentPassword: string,
    newPassword: string
  ): Promise<PasswordChangeResponse> => {
    return await $fetch<PasswordChangeResponse>('/api/password/change', {
      method: 'POST',
      body: {
        currentPassword,
        newPassword
      }
    })
  }

  return {
    changePassword
  }
}
