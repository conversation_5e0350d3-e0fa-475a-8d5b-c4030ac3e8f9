<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import easyHeader from './components/BusHeader.vue'
import easyTab from './components/BusList.vue'
import type { ProductItem } from '~/types/bus-service-selection'
import { useRouter } from 'vue-router'

import {
  useSpotStore as useTouristSpotStore,
  useOriginStore as useTouristOriginStore,
  useDateTimeStore
} from '~/stores/tourist-facilities'

import {
  useSpecialSpotStore as useSpotStore,
  useSpecialOriginStore as useOriginStore,
  useSpecialWaypointStore as useWaypointStore,
  useSpecialAppStore as useAppStore,
  useTimeStore
} from '~/stores/special-events'
import { useRouteStore } from '~/stores/bus-service-selection'

const router = useRouter()
const touristSpotStore = useTouristSpotStore()
const touristOriginStore = useTouristOriginStore()
const dateTimeStore = useDateTimeStore()
const timeStore = useTimeStore()

const spotStore = useSpotStore()
const originStore = useOriginStore()
const waypointStore = useWaypointStore()
const appStore = useAppStore()
const routeStore = useRouteStore()
// ===================
/* 交通ルートのデータ */
import type { RouteInfo } from '~/stores/bus-service-selection'

interface TransportRoute {
  id: number
  departure: string
  destination: string
  departureTime: string
  arrivalTime: string
  price: number
  originalPrice?: number
  statdate: string
  entdate?: string
  transferCount: number | string
  duration?: string
  discountRate?: number
  routePoints: { departure: string; isFavorite: boolean }[]
  isFavorite: boolean
  vacant?: string
  tags: string[]
  schedule: {
    date: string
    departureTime: string
    transfers: { arrival: string; departure: string }[]
    arrivalTime: string
  }[]
}

function transformRouteOutboundToTransportRoutes(
  routeOutbound: { routes: RouteInfo[] }
): TransportRoute[] {
  const routes = routeOutbound.routes || []

  if (routes.length === 0) return []

  const validRoutes = routes.filter(r => r.busService?.length)
  if (validRoutes.length === 0) return []

  // 使用 store 中的折扣信息配置
  const discountInfo = routeStore.discountInfo

  // 计算总原价
  const totalOriginalPrice = validRoutes.reduce(
    (sum, r) => sum + Number(r.busService?.[0]?.price?.replace(/,/g, '') || 0),
    0
  )

  // 根据换乘次数获取折扣率
  const transferCount = validRoutes.length - 1
  const discountConfig = discountInfo.find(d => d.count === transferCount)
  const discountRate = discountConfig ? parseInt(discountConfig.rate) : 0

  // 计算折扣后的价格
  const discountedPrice = Math.round(totalOriginalPrice * (1 - discountRate / 100))

  const transportRoute: TransportRoute = {
    id: 1,
    departure: validRoutes[0]?.departureName || '',
    destination: validRoutes[validRoutes.length - 1]?.destinationName || '',
    departureTime: validRoutes[0]?.busService?.[0]?.departureTime || '',
    arrivalTime: validRoutes[validRoutes.length - 1]?.busService?.[0]?.departureTime || '',
    price: discountedPrice, // 使用折扣后的价格
    originalPrice: totalOriginalPrice, // 原价
    statdate: validRoutes[0]?.busService?.[0]?.datePicker || '',
    entdate: '',
    transferCount: transferCount,
    duration: '',
    discountRate: discountRate, // 折扣率
    routePoints: [
      ...validRoutes.map(r => ({ departure: r.departureName || '', isFavorite: false })),
      { departure: validRoutes[validRoutes.length - 1]?.destinationName || '', isFavorite: false }
    ],
    isFavorite: false,
    vacant: '',
    tags: [],
    schedule: [
      {
        date: validRoutes[0]?.busService?.[0]?.datePicker || '',
        departureTime: validRoutes[0]?.busService?.[0]?.departureTime || '',
        transfers: validRoutes.slice(1).map(r => ({
          arrival: r.busService?.[0]?.departureTime || '',
          departure: r.busService?.[0]?.departureTime || ''
        })),
        arrivalTime: validRoutes[validRoutes.length - 1]?.busService?.[0]?.departureTime || ''
      }
    ]
  }

  return [transportRoute]
}

// 使用 ref 来存储转换后的数据，使其可修改
const transportRoutes = ref<TransportRoute[]>([])

// 监听 routeStore 的变化并更新 transportRoutes
watch(
  () => routeStore.routeSelected.routeOutbound,
  (newRouteOutbound) => {
    transportRoutes.value = transformRouteOutboundToTransportRoutes(newRouteOutbound)
  },
  { immediate: true, deep: true }
)

const formattedTransportRoutes = computed(() => JSON.stringify(transportRoutes.value, null, 2))
// ===================
/* ヘッダー関連の設定 */
const departureCity = ref(touristOriginStore.getSelectedOrigin?.name ?? '')
const arrivalCity = ref(touristSpotStore.getSelectedSpot?.name ?? '')
const departureTime = ref('')

// 初始化时间
departureTime.value = dateTimeStore.selectedTime

// 从 route store 获取统一的路由数据
const routeData = computed(() => routeStore.routeSelected.routeList)

/* 乗継回数の選択肢 */
const transferCounts = [
  { id: 0, title: '指定なし' },
  { id: 1, title: '直行便' },
  { id: 2, title: '1回まで' },
  { id: 3, title: '2回まで' },
  { id: 4, title: '3回以上' }
]

/* 乗継条件の選択肢 */
const transferConditions = [
  { id: 0, title: '最短時間' },
  { id: 1, title: '最少料金' },
  { id: 2, title: '同一端末' },
  { id: 3, title: '同一航空会社' }
]

const easyTabRef = ref<InstanceType<typeof easyTab>>()

/* フィルター条件 */
const selectedTransferCount = ref<string | undefined>(undefined)
const selectedTransferCondition = ref<string | undefined>(undefined)

/* フォームデータの設定 */
const localFormData = reactive({
  date: dateTimeStore.selectedDate || '',
  outbound: { date: '', time: '', direction: 'departure' },
  return: { date: '', time: '', direction: 'return' }
})

/* 予約ステップの定義 */
const reservationSteps = ref([
  {
    number: 'ルート選択',
    description:
      '目的地まで経由地の違いでルートを提案します。料金や所要時間などを考慮して選択してください。'
  },
  {
    number: '1.便選択',
    description:
      '選択したルートで、車両装備、出発時間/到着時間の違いで便がいくつか表示されるので希望の便を選択してください。'
  },
  {
    number: '2.予約確認',
    description:
      '目的地まで経由地の違いでルートを提案します。料金や所要時間などを考慮して選択してください。'
  },
  {
    number: '3.予約確定',
    description:
      '支払い方法を選択して、仮予約が完了します。入金確認後、予約が確定します。'
  }
])

/* ページネーション処理 */
const handlePrevCandidate = () => {
  if (easyTabRef.value) {
    let currentPage = easyTabRef.value.currentPage
    if (currentPage > 1) {
      easyTabRef.value.handlePageChange(currentPage - 1)
    }
  }
}

const handleNextCandidate = () => {
  if (easyTabRef.value) {
    let currentPage = easyTabRef.value.currentPage
    let totalPages = easyTabRef.value.totalPages
    if (currentPage < totalPages) {
      easyTabRef.value.handlePageChange(currentPage + 1)
    }
  }
}

/* 出発地をクリアする */
const onResetCondition = () => {
  // 始発地をクリアする
  originStore.clearCurrentOriginData()
  // 到着地をクリアする
  spotStore.clearCurrentTouristData()
  // すべての経由地をクリアする
  waypointStore.clearAllWaypoints()

  selectedTransferCount.value = undefined
  selectedTransferCondition.value = undefined
  router.push('/easy-choice/easy-searchFiltersReset')
}

/* 乗継回数変更処理 */
const handleTransferCountChange = (value: string) => {
  selectedTransferCount.value = String(value)
}

/* 乗継条件変更処理 */
const handleTransferConditionChange = (value: string) => {
  selectedTransferCondition.value = value
}

/* タブ設定 */
const tabs = [
  { name: '発車時間順', id: 1 },
  { name: '安い順', id: 2 },
  { name: '到着早い順', id: 3 },
  { name: '乗継少ない順', id: 4 }
]

const handleTabChange = (tabName: string) => {}
const handlePageChange = (page: number) => {}

/* 商品リストデータ */
const productList = ref<ProductItem[]>([
  {
    id: 1,
    origin: '京都',
    destination: '东京',
    route: ['冈山', '广岛', '东京'],
    title: '高速バス冈山～广岛线と广岛～岩国线の片道乘車券がセット',
    description:
      '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    image: 'https://picsum.photos/400/200?grayscale&blur=2',
    duration: 15,
    transfers: 0,
    price: 0,
    date: '2025年12月24日（水）'
  },
  {
    id: 2,
    origin: '冈山',
    destination: '大阪',
    route: ['冈山', '京都', '大阪', '东京'],
    title: '高速バス冈山～广岛线と广岛～岩国线の片道乘車券がセット',
    description:
      '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    image: 'https://picsum.photos/400/200?grayscale&blur=3',
    duration: 15,
    transfers: 0,
    price: 0,
    date: '2025年12月24日（水）'
  },
  {
    id: 3,
    origin: '铁山',
    destination: '大阪',
    route: ['冈山', '京都', '大阪', '东京'],
    title: '高速バス冈山～广岛线と广岛～岩国线の片道乘車券がセット',
    description:
      '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    image: 'https://picsum.photos/400/200?grayscale&blur=4',
    duration: 15,
    transfers: 0,
    price: 0,
    date: '2025年12月24日（水）'
  },
  {
    id: 4,
    origin: '金泽',
    destination: '大阪',
    route: ['冈山', '京都', '大阪', '东京'],
    title: '高速バス冈山～广岛线と广岛～岩国线の片道乘車券がセット',
    description:
      '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    image: 'https://picsum.photos/400/200?grayscale&blur=5',
    duration: 15,
    transfers: 0,
    price: 0,
    date: '2025年12月24日（水）'
  },
  {
    id: 5,
    origin: '静冈',
    destination: '大阪',
    route: ['冈山', '京都', '大阪', '东京'],
    title: '高速バス冈山～广岛线と广岛～岩国线の片道乘車券がセット',
    description:
      '说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明说明',
    image: 'https://picsum.photos/400/200?grayscale&blur=1',
    duration: 15,
    transfers: 0,
    price: 0,
    date: '2025年12月24日（水）'
  }
])

const filteredList = computed<TransportRoute[]>(() => {
  return transportRoutes.value.filter((route) => {
    // 出発地と目的地のチェック
    if (departureCity.value && route.departure !== departureCity.value) {
      return false
    }
    if (arrivalCity.value && route.destination !== arrivalCity.value) {
      return false
    }

    // 乗継回数のチェック
    if (!selectedTransferCount.value) return true

    const sel = String(selectedTransferCount.value)
    let routeCount =
      route.transferCount === 'なし' ? 0 : Number(route.transferCount)

    switch (sel) {
      case '指定なし':
        return true
      case '直行便':
        return routeCount === 0
      case '1回まで':
        return routeCount === 1
      case '2回まで':
        return routeCount === 2
      case '3回以上':
        return routeCount >= 3
      default:
        return true
    }
  })
})

const clear = () => {
  selectedTransferCount.value = undefined
}

/* 最安・最早・最少乗継タグを動的に付与する処理 */
const timeToAbsoluteMinutes = (depTime: string, arrTime: string) => {
  const parse = (t: string) => {
    const [hh = 0, mm = 0] = (t || '00:00').split(':').map((s) => Number(s))
    return hh * 60 + mm
  }
  const depMin = parse(depTime)
  let arrMin = parse(arrTime)
  if (arrMin < depMin) arrMin += 24 * 60
  return { depMin, arrMin }
}

// transferCount を数値化 */
const normalizeTransferCount = (count: number | string | undefined) => {
  if (count === undefined || count === null) return Number.MAX_SAFE_INTEGER
  if (typeof count === 'string' && count.trim() === 'なし') return 0
  const n = Number(count)
  if (Number.isFinite(n)) return n
  return Number.MAX_SAFE_INTEGER
}

/* 全ルートを走査して基準値（最安・最早・最少乗継）を算出し、tags をセットする関数 */
const updateTags = () => {
  const routes = transportRoutes.value
  if (!routes || routes.length === 0) return

  // 価格の最小値
  const prices = routes.map((r) =>
    typeof r.price === 'number' ? r.price : Number(r.price || Infinity)
  )
  const minPrice = Math.min(...prices)

  // 到着時間の最小値（絶対分）
  const arrivalMinutes = routes.map((r) => {
    const dep = String(r.departureTime || '00:00')
    const arr = String(r.arrivalTime || '00:00')
    return timeToAbsoluteMinutes(dep, arr).arrMin
  })
  const minArrival = Math.min(...arrivalMinutes)

  // 乗継回数の最小値
  const transferNums = routes.map((r) =>
    normalizeTransferCount(r.transferCount)
  )
  const minTransfer = Math.min(...transferNums)

  routes.forEach((r, idx) => {
    const tags: string[] = []

    // 安：価格が最小（同額の複数に付与）
    const priceVal =
      typeof r.price === 'number' ? r.price : Number(r.price || Infinity)
    if (priceVal === minPrice && Number.isFinite(priceVal)) tags.push('安')

    // 早：到着が最早（同時刻の複数に付与）
    const arrMin = arrivalMinutes[idx]
    if (arrMin === minArrival) tags.push('早')

    // 乐：乗継最少
    const tnum = transferNums[idx]
    if (tnum === minTransfer) tags.push('楽')

    // 上書き（または空配列を代入）
    r.tags = tags
  })
}

/* 初期化時とデータ変更時に自動で更新 */
onMounted(() => {
  updateTags()
})

/*  transportRoutes が変わったら再計算*/
watch(transportRoutes, () => {
  updateTags()
})

const getActiveDate = () => {
  return timeStore.chosenDate || dateTimeStore.selectedDate || ''
}

const getActiveTime = () => {
  return timeStore.chosenTime || dateTimeStore.selectedTime || ''
}

watch(
  [() => dateTimeStore.selectedDate, () => timeStore.chosenDate],
  () => {
    const activeDate = getActiveDate()
    if (activeDate && localFormData.date !== activeDate) {
      localFormData.date = activeDate
    }
  },
  { immediate: true }
)

watch(
  [() => dateTimeStore.selectedTime, () => timeStore.chosenTime],
  () => {
    const activeTime = getActiveTime()
    if (activeTime && departureTime.value !== activeTime) {
      departureTime.value = activeTime
    }
  },
  { immediate: true }
)


</script>

<template>
  <div>
    <div class="choose">
      <easyHeader/>
    </div>
    
  <div class="">
      <easyTab
        :filtered-list="filteredList"
        :tabs="tabs"
        @tab-change="handleTabChange"
        @page-change="handlePageChange"
        ref="easyTabRef"
      />
    </div>

     <div class="">
      <easyDetails :products="productList" />
    </div> 
      
  </div>
</template>
<style lang="scss" scoped>
.choose {
  padding: 0 20px;
}
</style>
