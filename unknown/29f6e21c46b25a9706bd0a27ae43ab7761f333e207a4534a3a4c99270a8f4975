<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import {
  useRouteStore,
  type PassengerCounts
} from '~/stores/bus-service-selection'

// プロップスとエミット
interface Props {
  modelValue: boolean
  initialCounts?: PassengerCounts
  routeId?: number
  serviceIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  routeId: 4,
  serviceIndex: 0,
  initialCounts: () => ({
    adult: { male: 1, female: 1 },
    child: { male: 1, female: 1 },
    student: { male: 0, female: 0 },
    disabilityAdult: { male: 0, female: 0 },
    disabilityChild: { male: 0, female: 0 },
    disability: { male: 0, female: 0 }
  })
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'passenger-selected': [counts: PassengerCounts]
  'reserve-without-seat': [
    data: { counts: PassengerCounts; formattedInfo: string }
  ]
  'proceed-to-seat': [counts: PassengerCounts]
}>()

// Store の初期化
const routeStore = useRouteStore()

// リアクティブデータ
const dialogModel = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const passengerCounts = ref<PassengerCounts>({ ...props.initialCounts })

// メソッド
const incrementCount = (
  type: keyof PassengerCounts,
  gender: 'male' | 'female'
) => {
  passengerCounts.value[type][gender]++
}

const decrementCount = (
  type: keyof PassengerCounts,
  gender: 'male' | 'female'
) => {
  if (passengerCounts.value[type][gender] > 0) {
    passengerCounts.value[type][gender]--
  }
}

const closeDialog = () => {
  emit('update:modelValue', false)
}

const reserveWithoutSeatSelection = () => {
  // 乗客情報のフォーマット処理
  const passengerInfo = formatPassengerInfo(passengerCounts.value)

  // Pinia store に人数大于0的数据を同期
  routeStore.updatePassengerCounts(
    props.routeId,
    props.serviceIndex,
    passengerCounts.value,
    passengerInfo
  )

  emit('reserve-without-seat', {
    counts: { ...passengerCounts.value },
    formattedInfo: passengerInfo
  })
  closeDialog()
}

// 性別詳細を生成するヘルパー関数
const createGenderDetails = (male: number, female: number): string[] => {
  const details: string[] = []
  if (male > 0) details.push(`男性${male}名`)
  if (female > 0) details.push(`女性${female}名`)
  return details
}

// 乗客カテゴリ情報を生成するヘルパー関数
const createCategoryInfo = (
  label: string,
  male: number,
  female: number
): string | null => {
  const total = male + female
  if (total === 0) return null

  const details = createGenderDetails(male, female)
  return `${label}：${details.join('、')}`
}

// 乗客情報をフォーマットする関数
const formatPassengerInfo = (counts: PassengerCounts) => {
  const info: string[] = []

  // カテゴリ設定
  const categories = [
    { key: 'adult', label: '大人' },
    { key: 'child', label: '子供' },
    { key: 'student', label: '学生' },
    { key: 'disabilityAdult', label: '障害者大人' },
    { key: 'disabilityChild', label: '障害者子供' },
    { key: 'disability', label: '障がい者割引' }
  ]

  // 各カテゴリの情報を生成
  categories.forEach(({ key, label }) => {
    const categoryData = counts[key as keyof PassengerCounts]
    if (categoryData) {
      const categoryInfo = createCategoryInfo(
        label,
        categoryData.male || 0,
        categoryData.female || 0
      )
      if (categoryInfo) {
        info.push(categoryInfo)
      }
    }
  })

  return info.join(' / ')
}

const proceedToSeatSelection = () => {
  // emit('proceed-to-seat', { ...passengerCounts.value })
  closeDialog()
}

// 初期値の監視
watch(
  () => props.initialCounts,
  (newCounts) => {
    if (newCounts) {
      passengerCounts.value = { ...newCounts }
    }
  },
  { deep: true }
)
</script>
<template>
  <v-dialog
    v-model="dialogModel"
    max-width="375px"
    persistent
    scrollable
    class="passenger-selection-dialog"
  >
    <v-card class="dialog-card">
      <!-- ヘッダー -->
      <div class="dialog-header">
        <v-btn icon variant="text" @click="closeDialog" class="back-button">
          <v-icon color="#26499D" size="24">mdi-arrow-left</v-icon>
        </v-btn>
        <div class="header-title">
          <span class="title-text">人数指定</span>
        </div>
      </div>
      <v-card-text class="dialog-content">
        <div class="passenger-section">
          <div class="section-title">人数指定</div>
          <!-- 大人 -->
          <div class="passenger-group">
            <div class="passenger-type-row">
              <div class="type-label">大人</div>
              <div class="gender-controls">
                <div class="gender-group">
                  <span class="gender-label">男性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('adult', 'male')"
                      :disabled="passengerCounts.adult.male <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.adult.male }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('adult', 'male')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
                <div class="gender-group">
                  <span class="gender-label">女性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('adult', 'female')"
                      :disabled="passengerCounts.adult.female <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.adult.female }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('adult', 'female')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 子供 -->
          <div class="passenger-group">
            <div class="passenger-type-row">
              <div class="type-label">子供</div>
              <div class="gender-controls">
                <div class="gender-group">
                  <span class="gender-label">男性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('child', 'male')"
                      :disabled="passengerCounts.child.male <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.child.male }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('child', 'male')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
                <div class="gender-group">
                  <span class="gender-label">女性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('child', 'female')"
                      :disabled="passengerCounts.child.female <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.child.female }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('child', 'female')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分割線 -->
          <div class="divider"></div>
        </div>

        <div class="disability-section">
          <div class="section-title">各種料金プラン</div>

          <div class="passenger-group">
            <div class="passenger-type-row">
              <div class="type-label">
                早割引
                <span>6,900</span>
              </div>
              <div class="gender-controls">
                <div class="gender-group">
                  <span class="gender-label">男性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('disabilityAdult', 'male')"
                      :disabled="passengerCounts.disabilityAdult.male <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.disabilityAdult.male }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('disabilityAdult', 'male')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
                <div class="gender-group">
                  <span class="gender-label">女性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('disabilityAdult', 'female')"
                      :disabled="passengerCounts.disabilityAdult.female <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.disabilityAdult.female }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('disabilityAdult', 'female')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="passenger-group">
            <div class="passenger-type-row">
              <div class="type-label">
                学生割引
                <span>6,900</span>
              </div>
              <div class="gender-controls">
                <div class="gender-group">
                  <span class="gender-label">男性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('disabilityChild', 'male')"
                      :disabled="passengerCounts.disabilityChild.male <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.disabilityChild.male }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('disabilityChild', 'male')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
                <div class="gender-group">
                  <span class="gender-label">女性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('disabilityChild', 'female')"
                      :disabled="passengerCounts.disabilityChild.female <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.disabilityChild.female }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('disabilityChild', 'female')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 障がい者割引 -->
          <div class="passenger-group">
            <div class="passenger-type-row">
              <div class="type-label">
                障がい者割引
                <span>6,900</span>
              </div>
              <div class="gender-controls">
                <div class="gender-group">
                  <span class="gender-label">男性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('disability', 'male')"
                      :disabled="passengerCounts.disability.male <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.disability.male }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('disability', 'male')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
                <div class="gender-group">
                  <span class="gender-label">女性</span>
                  <div class="counter-controls">
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn minus-btn"
                      @click="decrementCount('disability', 'female')"
                      :disabled="passengerCounts.disability.female <= 0"
                    >
                      <span class="counter-text">－</span>
                    </v-btn>
                    <div class="count-display">
                      {{ passengerCounts.disability.female }}
                    </div>
                    <v-btn
                      icon
                      size="small"
                      variant="outlined"
                      class="counter-btn plus-btn"
                      @click="incrementCount('disability', 'female')"
                    >
                      <span class="counter-text">＋</span>
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </v-card-text>

      <!-- フッターボタン -->
      <div class="dialog-footer">
        <v-btn
          variant="outlined"
          class="footer-btn primary-btn"
          @click="reserveWithoutSeatSelection"
          block
        >
          人数を確定
        </v-btn>
        <v-btn
          class="footer-btn secondary-btn"
          @click="proceedToSeatSelection"
          block
        >
          キャンセル
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>
<style scoped>
.passenger-selection-dialog {
  z-index: 1000;
}

.dialog-card {
  margin: 0;
  border-radius: 0;
  max-height: 100vh;
  overflow: hidden;
  max-width: 100%;
  width: 100%;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 0.5px solid #dfdfdf;
}

.back-button {
  position: absolute;
  left: 16px;
  width: 24px;
  height: 24px;
  min-width: 24px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 14px;
}

.title-text {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.2;
  color: #26499d;
  text-align: center;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #dfdfdf;
  margin: 0 auto;
}

.dialog-content {
  padding: 16px;
  background-color: #ffffff;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

.passenger-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
  width: 100%;
  box-sizing: border-box;
}

.disability-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
  color: #000000;
  margin-bottom: 8px;
}

.passenger-group {
  padding: 6px 0;
  width: 100%;
  box-sizing: border-box;
}

.passenger-type-row {
  gap: 12px;
  width: 100%;
  box-sizing: border-box;
}

.type-label {
  height: 36px;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5625;
  text-align: left;
  flex-shrink: 0;
  color: #26499d;
}
.type-label span {
  font-size: 18px;
  font-weight: bold;
  margin-left: 5px;
  color: #000;
}
.gender-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.gender-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.gender-label {
  font-weight: 400;
  font-size: 11px;
  line-height: 1.6667;
  color: #000000;
  width: 100%;
  text-align: left;
}

.counter-controls {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0.8;
}

.counter-btn {
  width: 36px !important;
  height: 36px !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 5px !important;
  background-color: #e7f2fa !important;
  min-width: 36px !important;
}

.counter-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2;
  color: #26499d !important;
}

.count-display {
  width: 30px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f3f3;
  border-radius: 5px;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2;
  color: #000000;
}

/* フッター */
.dialog-footer {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 32px 16px;
  background-color: #ffffff;
}

.footer-btn {
  height: 48px !important;
  border-radius: 4px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  text-transform: none !important;
}

.secondary-btn {
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  color: #26499d !important;
  box-shadow: none !important;
}

.primary-btn {
  background-color: #26499d !important;
  color: #ffffff !important;
}

.secondary-btn:hover {
  background-color: #d1e7f5 !important;
}

.primary-btn:hover {
  background-color: #1e3a8a !important;
}

/* Vuetifyのボタンスタイルオーバーライド */
:deep(.v-btn) {
  text-transform: none !important;
}

:deep(.v-btn--variant-outlined) {
  border-width: 1px !important;
}

:deep(.counter-btn.v-btn--disabled) {
  opacity: 0.4 !important;
  background-color: #f3f3f3 !important;
  border-color: #e2e8f0 !important;
}

:deep(.counter-btn.v-btn--disabled .counter-text) {
  color: #94a3b8 !important;
}

/* スクロールバー */
.dialog-content::-webkit-scrollbar {
  width: 4px;
}

.dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
