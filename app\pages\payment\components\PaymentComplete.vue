<script lang="ts" setup>
const props = defineProps<{
  modelValue: boolean
  reservationNo: string
}>()

const emit = defineEmits(['update:modelValue'])

const close = () => {
  emit('update:modelValue', false)
}

const goToTop = async () => {
  close()
  await navigateTo({
    path: '/'
  })
}
</script>

<template>
  <v-dialog v-model="props.modelValue" max-width="400">
    <v-card>
      <v-card-text class="px-3">
        <h3 class="text-h6 font-weight-regular mb-3">決済完了</h3>
        <p class="mb-1 text-body-1 mb-4">予約番号：{{ reservationNo }}</p>
        <p class="mb-1 text-body-2">決済が完了しました。</p>
        <p class="mb-1 text-body-2">決済完了メールをお送りしましたので、ご確認ください。</p>
        <p class="mb-4 text-body-2">
          予約情報はマイページの予約履歴からご確認いただけます。
        </p>

        <v-btn block rounded="xs" color="#26499d" class="white--text" @click="goToTop">
          TOPへ
        </v-btn>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
