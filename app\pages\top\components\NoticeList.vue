<script lang="ts" setup>
import { useRouter } from 'vue-router'
/* *
 * お知らせデータのインターフェース定義
 * */
interface NoticeApiResponse {
  id: number
  title: string
  categoryId: number
  categoryName: string
  publishStartDate: string
  publishEndDate: string
  isPublished: boolean
  createdDateTime: string
  createdById: string
  updatedDateTime: string
  updatedById: string
  excerpt?: string
}

/* *
 * APIレスポンスのインターフェース定義
 * */
interface ApiResponse {
  data: NoticeApiResponse[]
  total?: number
  page?: number
  pageSize?: number
}

const router = useRouter()

/* *
 * 状態管理用のリアクティブ変数
 * */
const notices = ref<NoticeApiResponse[]>([])
const pending = ref(false)
const showError = ref(false)
const errorMessage = ref('')

const props = defineProps<{
  maxItems?: number
  apiEndpoint?: string
}>()

/* *
 * お知らせデータを取得する関数
 * */
const fetchNotices = async () => {
  pending.value = true
  notices.value = getMockData()
  try {
    //todo api
  } catch (err) {
    console.error('fetchNotices error:', err)
    errorMessage.value = 'お知らせの取得に失敗しました'
    showError.value = true
  } finally {
    pending.value = false
  }
}

/* *
 * スナックバーを閉じる関数
 * */
const closeSnackbar = () => {
  showError.value = false
}

/* *
 * お知らせ詳細ページに遷移する関数
 * */
const navigateToDetail = (noticeId: number) => {
  router.push(`/notifications/edit/${noticeId}`)
}

/* *
 * お知らせ一覧ページに遷移する関数
 * */
const navigateToNoticeList = () => {
  router.push('/notifications')
}

/* *
 * 日付をフォーマットする関数
 * */
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const y = date.getFullYear()
  const m = `${date.getMonth() + 1}`.padStart(2, '0')
  const day = `${date.getDate()}`.padStart(2, '0')
  return `${y}.${m}.${day}`
}

/* *
 * バッジのクラスを決定する関数
 * */
const badgeClass = (categoryName: string) => {
  if (categoryName === 'ニュース') {
    return 'is-news'
  }
  return 'is-default'
}

/* *
 * モックデータを生成する関数
 * */
const getMockData = (): NoticeApiResponse[] => [
  {
    id: 1,
    title: 'システムメンテナンスのお知らせ',
    categoryId: 1,
    categoryName: 'メンテナンス',
    publishStartDate: '2025-01-15T00:00:00Z',
    publishEndDate: '2025-02-15T23:59:59Z',
    isPublished: true,
    createdDateTime: '2025-01-10T10:00:00Z',
    createdById: 'admin',
    updatedDateTime: '2025-01-10T10:00:00Z',
    updatedById: 'admin',
    excerpt:
      'システムメンテナンスにより、一時的にサービスがご利用いただけません。'
  },
  {
    id: 2,
    title: '新機能リリースのお知らせ',
    categoryId: 2,
    categoryName: 'ニュース',
    publishStartDate: '2025-01-10T00:00:00Z',
    publishEndDate: '2025-02-10T23:59:59Z',
    isPublished: true,
    createdDateTime: '2025-01-05T15:30:00Z',
    createdById: 'admin',
    updatedDateTime: '2025-01-05T15:30:00Z',
    updatedById: 'admin',
    excerpt: '新しい機能が追加されました。ぜひお試しください。'
  },
  {
    id: 3,
    title: '大雪による運行への影響について',
    categoryId: 3,
    categoryName: '運行状況',
    publishStartDate: '2025-01-08T00:00:00Z',
    publishEndDate: '2025-01-20T23:59:59Z',
    isPublished: true,
    createdDateTime: '2025-01-08T08:00:00Z',
    createdById: 'operator',
    updatedDateTime: '2025-01-08T08:00:00Z',
    updatedById: 'operator',
    excerpt: '大雪の影響により、一部路線で遅延や運休が発生しております。'
  }
]

onMounted(() => {
  fetchNotices()
})
</script>

<template>
  <section class="notice-section">
    <div class="notice-container">
      <h2 class="notice-title">お知らせ</h2>
      <div class="title-underline"></div>

      <div v-if="!notices.length && !pending" class="empty-container">
        <v-icon icon="mdi-information-outline" size="40" color="grey"></v-icon>
        <p class="empty-text">お知らせがありません</p>
      </div>

      <ul v-else class="notice-list">
        <li v-for="notice in notices" :key="notice.id" class="notice-item">
          <div class="row-head mobile-only">
            <time class="date" :datetime="notice.publishStartDate">
              {{ formatDate(notice.publishStartDate) }}
            </time>
            <span class="badge" :class="badgeClass(notice.categoryName)">
              {{ notice.categoryName }}
            </span>
          </div>

          <div class="row-body mobile-only">
            <a
              class="title"
              href="#"
              @click.prevent="navigateToDetail(notice.id)"
              :aria-label="`${notice.title}の詳細を見る`"
            >
              {{ notice.title }}
            </a>
          </div>

          <div class="row-desktop desktop-only">
            <div class="col-left">
              <time class="date" :datetime="notice.publishStartDate">
                {{ formatDate(notice.publishStartDate) }}
              </time>
              <span class="badge" :class="badgeClass(notice.categoryName)">
                {{ notice.categoryName }}
              </span>
            </div>
            <div class="col-right">
              <a
                class="title"
                href="#"
                @click.prevent="navigateToDetail(notice.id)"
                :aria-label="`${notice.title}の詳細を見る`"
              >
                {{ notice.title }}
              </a>
              <p class="excerpt" v-if="notice.excerpt">
                {{ notice.excerpt }}
              </p>
            </div>
          </div>

          <hr class="divider" />
        </li>
      </ul>
    </div>

    <div class="more-wrap" v-if="notices.length > 0">
      <v-btn
        @click="navigateToNoticeList"
        variant="outlined"
        :disabled="pending"
      >
        もっと見る
      </v-btn>
    </div>

    <v-snackbar
      v-model="showError"
      color="error"
      timeout="4000"
      variant="tonal"
    >
      {{ errorMessage }}
      <template v-slot:actions>
        <v-btn variant="text" @click="closeSnackbar">閉じる</v-btn>
      </template>
    </v-snackbar>
  </section>
</template>

<style scoped>
:root,
:host {
  --bg-page: #f0f0f0;
  --bg-card: #ffffff;
  --ink-strong: #0c2e8a;
  --ink: #222;
  --ink-weak: #666;
  --border: #e9e9ee;
  --badge-new-bg: #e86f5a;
  --badge-maint-bg: #ffecd9;
  --badge-maint-fg: #ff7f32;
  --badge-status-bg: #ffe6e6;
  --badge-status-fg: #d23c3c;
  --badge-event-bg: #e3f2fd;
  --badge-event-fg: #1976d2;
  --focus: #2b6cff;
}

.notice-section {
  background: var(--bg-page);
}

.notice-container {
  background: var(--bg-card);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  margin: 17px;
  min-height: 200px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-text {
  margin: 16px 0 8px;
  color: var(--ink-weak);
  font-size: 14px;
}

.notice-title {
  text-align: center;
  color: #26499d;
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  padding-top: 20px;
}

.title-underline {
  width: 40px;
  height: 2px;
  background: #26499d;
  margin: 0 auto 20px;
}

.notice-list {
  margin: 12px 0 8px;
  padding: 0;
  list-style: none;
}

.notice-item {
  padding: 8px 0 0;
}

.row-head {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date {
  font-size: 12px;
  color: #333;
  letter-spacing: 0.02em;
  margin-right: 16px;
}

.badge {
  display: inline-block;
  font-size: 11px;
  font-weight: 700;
  line-height: 1;
  padding: 6px 10px 5px;
  border: 1px solid transparent;
  border-radius: 4px;
  transform: translateY(-1px);
}

.badge.is-news {
  background: #ed785f;
  color: #fff;
  border: none;
}

.badge.is-default {
  background: #fff;
  color: #ed785f;
  border: 1px solid #ed785f;
}

.row-body .title,
.col-right .title {
  display: inline-block;
  margin-top: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--ink);
  text-decoration: none;
  transition: color 0.2s ease;
}

.row-body .title:hover,
.col-right .title:hover {
  color: #26499d;
  text-decoration: underline;
}

.divider {
  margin: 8px 0 0;
  border: 0;
  border-top: 1px solid #dfdfdf;
}

.desktop-only {
  display: none;
}

.mobile-only {
  display: block;
}

@media (min-width: 992px) {
  .notice-container {
    max-width: 58%;
    margin: 0 auto;
  }

  .notice-title {
    font-size: 22px;
  }

  .notice-list {
    margin-top: 24px;
  }

  .desktop-only {
    display: block;
  }

  .mobile-only {
    display: none;
  }

  .row-desktop {
    display: grid;
    grid-template-columns: 170px 1fr;
    column-gap: 28px;
    align-items: start;
  }

  .col-left .date {
    display: block;
    font-size: 12px;
    margin-bottom: 10px;
  }

  .col-left .badge {
    display: inline-block;
  }

  .col-right .title {
    margin-top: 0;
    margin-bottom: 6px;
    font-size: 16px;
    font-weight: 700;
  }

  .excerpt {
    margin: 0 0 6px;
    font-size: 13px;
    line-height: 1.7;
    color: var(--ink-weak);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
  }

  .divider {
    margin-top: 14px;
  }
}

.more-wrap {
  display: flex;
  justify-content: center;
  margin: 48px 0;
}

.more-btn button {
  padding: 12px 24px;
  border-radius: 24px;
  border: 1px solid #26499d;
  background: #fff;
  color: #26499d;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.more-btn button:hover:not(:disabled) {
  background: #26499d;
  color: #fff;
}

.more-btn button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
