<script lang="ts" setup>
	import {
		ref,
		onMounted
	} from 'vue'
	import {
		useRoute
	} from 'vue-router'

	/* ルートパラメータ */
	const route = useRoute()
	const companyId = ref < number | null > (null)

	/* 状態管理 */
	const company = ref < any > (null)
	const isFavorite = ref(false)
	const showNewsletter = ref(false)
	const isNewsletter = ref(false)

	/* バス会社詳細データを取得 */
	const fetchCompanyDetail = async (id: number) => {
		const res = {
			id,
			name: 'バス会社名',
			description: '信頼の運行歴50年、快適なプレミアムバスが人気！',
			rating: 4,
			reviewCount: 1000,
			favorite: true,
			newsletter: true
		}
		company.value = res
		isFavorite.value = res.favorite
		isNewsletter.value = res.newsletter
		showNewsletter.value = res.favorite
	}

	/* お気に入りの切り替え */
	const toggleFavorite = async () => {
		if (!isFavorite.value) {
			isFavorite.value = true
			showNewsletter.value = true
		} else {
			isFavorite.value = false
			showNewsletter.value = false
			isNewsletter.value = false
		}
	}

	/* メルマガの切り替え */
	const toggleNewsletter = async () => {

	}

	const handleBack = () => {
		window.history.back()
	}

	onMounted(() => {
		if (route.params.id) {
			companyId.value = Number(route.params.id)
			fetchCompanyDetail(companyId.value)
		}
	})
</script>

<template>
	<div class="company-detail">
		<div class="header">
			<v-icon color="#26499d" size="30px" class="pl-4" @click="handleBack">
				mdi-chevron-left
			</v-icon>
			<h3 class="title">バス会社詳細</h3>
		</div>

		<v-card flat>
			<v-card-text>
				<div class="d-flex align-center justify-space-between">
					<span class="company-name">{{ company?.name }}</span>
					<v-icon @click="toggleFavorite" :color="isFavorite ? 'red' : 'grey'">
						{{ isFavorite ? 'mdi-heart' : 'mdi-heart-outline' }}
					</v-icon>
				</div>

				<p class="description">{{ company?.description }}</p>

				<div v-if="showNewsletter" class="mt-4">
					<v-switch v-model="isNewsletter" color="primary" hide-details @change="toggleNewsletter">
						<template #label>
							<span :style="{ color: isNewsletter ? '#26499d' : 'grey' }" class="switch-label">
								メルマガを受け取る
							</span>
						</template>
					</v-switch>
				</div>

				<h4 class="section-title">会社情報</h4>
				<p class="description">
					内容内容内容内容内容内容内容内容内容内容内容内容内容
				</p>

				<h4 class="section-title">運行路線</h4>
				<p class="description">
					内容内容内容内容内容内容内容内容内容内容内容内容内容
				</p>

				<h4 class="section-title">バスタイプ</h4>
				<p class="description">
					内容内容内容内容内容内容内容内容内容内容内容内容内容
				</p>
			</v-card-text>
		</v-card>
	</div>
</template>

<style scoped>
	.company-detail {
		background: #fff;
	}

	.header {
		position: relative;
		display: flex;
		align-items: center;
		height: 48px;
	}

	.back-btn {
		background: none;
		border: none;
		padding: 0 12px;
		display: flex;
		align-items: center;
		position: absolute;
	}

	.title {
		flex: 1;
		text-align: center;
		color: #26499d;
		font-size: 19px;
		font-weight: 400;
		margin: 0;
		display: inline;
	}

	.company-name {
		font-size: 16px;
		font-weight: bold;
	}

	.company-name .v-btn {
		background-color: unset;
	}

	.description {
		margin: 8px 0;
		font-size: 14px;
		color: black;
	}

	.section-title {
		margin-top: 16px;
		font-weight: bold;
		font-size: 16px;
	}

	.switch-label {
		font-size: 14px;
	}
</style>
