<script lang="ts" setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { ValidationUtils } from '~/pages/member-registration/member-registration'
import type {
  VerificationFormData,
  CountdownState
} from '~/types/member-Registration'
import { useRouter } from 'vue-router'

definePageMeta({
  hideFooter: true
})

const router = useRouter()

/* フォームデータ */
const formData = reactive<VerificationFormData>({
  verificationCode: ''
})

/* カウントダウンの初期秒数 */
const INITIAL_SECONDS = 30

/* カウントダウン状態 */
const countdownState = reactive<CountdownState>({
  isActive: false,
  seconds: INITIAL_SECONDS,
  canResend: true
})

const validationError = ref<string>('')
const countdownTimer = ref<NodeJS.Timeout | null>(null)

/* 再送ボタンのテキスト */
const resendButtonText = computed(() => {
  if (!countdownState.canResend && countdownState.isActive) {
    return `${countdownState.seconds}秒後に再送できます`
  }
  if (
    countdownState.canResend &&
    !countdownState.isActive &&
    countdownState.seconds < INITIAL_SECONDS
  ) {
    return '認証コードを再送信'
  }
  return '30秒後に再送できます'
})

/* 入力時のバリデーション */
const handleInput = () => {
  const value = formData.verificationCode
  if (!/^\d*$/.test(value)) {
    validationError.value = '認証コードに誤りがあります。再確認してください。'
  } else {
    validationError.value = ''
  }
}

const handleBlur = () => validateForm()

/* 入力チェック */
const validateForm = (): boolean => {
  const result = ValidationUtils.validateVerificationCode(
    formData.verificationCode
  )
  validationError.value = result.isValid ? '' : result.message
  return result.isValid
}

/* 送信処理 */
const handleSubmit = () => {
  if (validateForm()) {
    router.push('/')
  }
}

/* 認証コードの再送処理 */
const handleResend = () => {
  if (!countdownState.canResend) return
  startCountdown()
}

/* カウントダウン開始 */
const startCountdown = () => {
  countdownState.isActive = true
  countdownState.canResend = false
  countdownState.seconds = INITIAL_SECONDS

  countdownTimer.value = setInterval(() => {
    countdownState.seconds--
    if (countdownState.seconds <= 0) resetCountdown()
  }, 1000)
}

/* カウントダウンリセット */
const resetCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  countdownState.isActive = false
  countdownState.canResend = true
}

/* コンポーネント破棄時にタイマーをクリア */
onUnmounted(() => {
  if (countdownTimer.value) clearInterval(countdownTimer.value)
})
</script>

<template>
  <div>
    <div class="regionpadding">
      <BaseHeader
        title="認証コード入力"
        :showBack="false"
        :showRightIcon="false"
      />
    </div>

    <div class="verificationContainer">
      <div class="verificationCard">
        <p class="description">
          ご登録メールアドレスに認証コードを送信しました。30分以内にメール記載の認証コードを入力してください。
        </p>

        <form @submit.prevent="handleSubmit" class="form">
          <div class="formGroup">
            <label for="verificationCode" class="formLabel">認証コード</label>
            <input
              id="verificationCode"
              v-model="formData.verificationCode"
              type="text"
              class="formInput"
              :class="{ error: validationError }"
              maxlength="6"
              @input="handleInput"
              @blur="handleBlur"
            />
            <div v-if="validationError" class="errorMessage">
              {{ validationError }}
            </div>
          </div>

          <div class="resendSection">
            <button
              type="button"
              @click="handleResend"
              class="resendButton"
              :disabled="!countdownState.canResend"
            >
              {{ resendButtonText }}
            </button>
          </div>

          <button
            type="submit"
            class="submitButton"
            :class="{ active: formData.verificationCode.length === 6 }"
          >
            送信する
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.verificationContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.verificationCard {
  background: white;
  padding: 40px 0;
  width: 100%;
  max-width: 400px;
}

.description {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin: 0 0 32px 0;
  text-align: left;
}

.form {
  .formGroup {
    margin-bottom: 24px;
  }

  .formLabel {
    display: block;
    font-size: 14px;
    color: #26499d;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .formInput {
    width: 100%;
    height: 48px;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 0 16px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    &.error {
      border-color: #dc3545;
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }
  }

  .errorMessage {
    font-size: 12px;
    color: #dc3545;
    margin-top: 4px;
  }
}

.resendSection {
  text-align: center;
  margin-bottom: 32px;

  .resendButton {
    background: none;
    border: none;
    font-size: 14px;
    color: #26499d;
    text-decoration: underline;
    padding: 8px;
    transition: opacity 0.3s ease;
  }
}

.submitButton {
  width: 100%;
  height: 48px;
  background: #fbe4df;
  border: none;
  border-radius: 24px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 154, 139, 0.3);

  &.active {
    background: #ed785f;
  }
}
</style>
