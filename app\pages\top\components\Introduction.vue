<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import connectiv from '~/assets/image/connectiv.png'
import connectivLeft from '~/assets/image/connectivLeft.png'
import connectivRight from '~/assets/image/connectivRight.png'

/* *
 * 画像リソースの定義
 * */
const connectivImage = connectiv
const connectivLeftImag = connectivLeft
const connectivRightImag = connectivRight

/* *
 * DOM参照とインターバルID
 * */
const wrapperRef = ref<HTMLElement | null>(null)
let intervalId: number | null = null

/* *
 * プロパティ定義
 * */
const props = defineProps({
  gallery: {
    type: Array as () => Array<{
      leftTopImage: string
      leftBottomImage: string
      rightImage: string
      leftTopAlt?: string
      leftBottomAlt?: string
      rightAlt?: string
    }>,
    required: true
  }
})

/* *
 * スクロールイベントを防止する関数
 * */
const preventScroll = (e: Event): boolean => {
  e.preventDefault()
  e.stopPropagation()
  return false
}

/* *
 * タッチイベントを防止する関数
 * */
const preventTouch = (e: Event): boolean => {
  e.preventDefault()
  return false
}

/* *
 * スムーズスクロールを実現する関数
 * */
const slowSmoothScrollTo = (
  element: HTMLElement,
  targetPosition: number,
  duration = 2000
): void => {
  const startPosition = element.scrollLeft
  const distance = targetPosition - startPosition
  const startTime = performance.now()

  const animateScroll = (currentTime: number): void => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    const easeProgress =
      progress < 0.5
        ? 16 * progress * progress * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 5) / 2

    element.scrollLeft = startPosition + distance * easeProgress

    if (progress < 1) {
      requestAnimationFrame(animateScroll)
    }
  }

  requestAnimationFrame(animateScroll)
}

/* *
 * コンポーネントマウント時の処理
 * */
onMounted(() => {
  const wrapper = wrapperRef.value
  if (!wrapper) return

  // スクロールイベントの防止
  wrapper.addEventListener('wheel', preventScroll, { passive: false })
  wrapper.addEventListener('mousewheel', preventScroll, { passive: false })
  wrapper.addEventListener('DOMMouseScroll', preventScroll, { passive: false })
  wrapper.addEventListener('touchstart', preventTouch, { passive: false })
  wrapper.addEventListener('touchmove', preventTouch, { passive: false })
  wrapper.addEventListener('touchend', preventTouch, { passive: false })

  // 自動スクロールの設定
  setTimeout(() => {
    let currentIndex = 0
    const total = props.gallery.length
    const scrollStep = (wrapper.children[0] as HTMLElement)?.offsetWidth || 600

    intervalId = window.setInterval(() => {
      currentIndex = (currentIndex + 1) % total
      const targetPosition = currentIndex * scrollStep

      slowSmoothScrollTo(wrapper, targetPosition, 4000)
    }, 5000)
  }, 500)
})

/* *
 * コンポーネントアンマウント時の処理
 * */
onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }

  const wrapper = wrapperRef.value
  if (wrapper) {
    wrapper.removeEventListener('wheel', preventScroll)
    wrapper.removeEventListener('mousewheel', preventScroll)
    wrapper.removeEventListener('DOMMouseScroll', preventScroll)
    wrapper.removeEventListener('touchstart', preventTouch)
    wrapper.removeEventListener('touchmove', preventTouch)
    wrapper.removeEventListener('touchend', preventTouch)
  }
})
</script>
<template>
  <div class="gallery-wrapper" ref="wrapperRef">
    <div
      class="image-gallery-container"
      v-for="(item, index) in gallery"
      :key="index"
    >
      <div class="left-images">
        <div class="top-image">
          <img :src="item.leftTopImage" :alt="item.leftTopAlt" class="image" />
        </div>

        <div class="bottom-image">
          <img
            :src="item.leftBottomImage"
            :alt="item.leftBottomAlt"
            class="image"
          />
        </div>
      </div>

      <div class="logo-container">
        <img :src="connectivImage" alt="Connectiv Logo" class="logo-tivImage" />
      </div>

      <div class="left-tivImage-container">
        <img
          :src="connectivLeftImag"
          alt="Connectiv Left"
          class="left-tivImage"
        />
      </div>

      <div class="right-images">
        <div class="horizontal-scroll-container">
          <img
            :src="item.rightImage"
            :alt="item.rightAlt"
            class="scrollable-image-horizontal"
          />

          <div class="right-tivImage-container">
            <img
              :src="connectivRightImag"
              alt="Connectiv Right"
              class="right-tivImage"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.gallery-wrapper {
  display: flex;
  align-items: flex-start;
  overflow-x: hidden;
  -ms-overflow-style: none;
  scrollbar-width: none;
  gap: 10px;
  padding-bottom: 5px;
}

.image-gallery-container {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
  width: 600px;
}

.left-images {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 223px;
  flex-shrink: 0;
  position: relative;
}

.logo-container {
  position: absolute;
  top: 130px;
  left: 50%;
  transform: translateX(-50%);
}

.logo-tivImage {
  width: 145px;
  height: 145px;
}

.left-tivImage-container {
  position: absolute;
  top: 270px;
  left: 10%;
  width: 80px;
  height: 104px;
}

.left-tivImage {
  width: 100%;
  height: 100%;
}

.right-tivImage-container {
  position: absolute;
  top: 270px;
  right: 10%;
  width: 80px;
  height: 104px;
}

.right-tivImage {
  width: 100%;
  height: 100%;
}

.top-image,
.bottom-image {
  width: 223px;
  height: 141px;
  border-radius: 12px;
  overflow: hidden;
}

.top-image img,
.bottom-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.right-images {
  flex: 1;
  border-radius: 12px;
  overflow: hidden;
  height: 288px;
}

.scrollable-image-horizontal {
  width: 100%;
  height: 288px;
  object-fit: cover;
}

@media (min-width: 1280px) {
  .left-tivImage-container {
    position: absolute;
    top: 270px;
    left: 29%;
    width: 80px;
    height: 104px;
  }
  .right-tivImage-container {
    position: absolute;
    top: 270px;
    right: 34%;
    width: 80px;
    height: 104px;
  }
}
</style>
