<script lang="ts" setup>
import { useFormStore } from '@/stores/member-alter'
import type { ConfirmFormData } from '~/types/member-alter'
import { useRouter } from 'vue-router'
import { ref, computed } from 'vue'

const router = useRouter()

const showDialog = ref(false)
const formStore = useFormStore()

/* フォームデータの型変換 */
const submittedData = formStore.submittedData as unknown as
  | ConfirmFormData
  | undefined

/* 性別のフォーマット変換 */
const formatGender = (gender?: string): string => {
  if (gender === 'male') return '男性'
  if (gender === 'female') return '女性'
  if (gender === 'answer') return '回答しない'
  return ''
}
const genderLabel = computed(() => formatGender(submittedData?.gender))

/* メール購読状態のフォーマット変換 */
const formatEmailSubscription = (isSubscribe?: boolean | null): string => {
  return isSubscribe ? '希望する' : '希望しない'
}

/* 前のページに戻る */
const goBack = (): void => {
  if (window.history.length > 1) {
    window.history.back()
  }
}

/* フォーム送信処理 */
const handleSubmit = (): void => {
  showDialog.value = true
}

/* ポップアップ確認ボタン処理 */
const confirmChange = () => {
  showDialog.value = false
  router.push('/member-info')
}
</script>

<template>
  <div class="registrationContainer">
     <div class="regionpadding">
      <BaseHeader
        title="会員情報変更確認"
        :showBack="true"
        :showRightIcon="false"
      />
    </div>


    <div class="formContentArea">
      <div class="instructionText">
        変更内容をご確認後、「変更する」ボタンを押してください。
      </div>

      <div class="formFieldsList">
        <!-- お名前 -->
        <div class="fieldGroup">
          <span class="fieldLabel">お名前</span>
          <div class="fieldValue">
            {{ submittedData?.lastName }} {{ submittedData?.firstName }}
          </div>
        </div>

        <!-- フリガナ -->
        <div class="fieldGroup">
          <span class="fieldLabel">フリガナ</span>
          <div class="fieldValue">
            {{ submittedData?.lastNameKana }} {{ submittedData?.firstNameKana }}
          </div>
        </div>

        <!-- 生年月日 -->
        <div class="fieldGroup">
          <span class="fieldLabel">生年月日</span>
          <div class="fieldValue">
            {{ submittedData?.birthYear }}年{{ submittedData?.birthMonth }}月{{
              submittedData?.birthDay
            }}日
          </div>
        </div>

        <!-- 性別 -->
        <div class="fieldGroup">
          <span class="fieldLabel">性別</span>
          <div class="fieldValue">
            {{ genderLabel }}
          </div>
        </div>

        <!-- 居住地 -->
        <div class="fieldGroup">
          <span class="fieldLabel">居住地</span>
          <div class="fieldValue">{{ submittedData?.residence?.label }}</div>
        </div>

        <!-- 市区町村・番地以降 -->
        <div class="fieldGroup">
          <span class="fieldLabel">市区町村・番地以降</span>
          <div class="fieldValue">{{ submittedData?.cityAddressDetail }}</div>
        </div>

        <!-- 職業 -->
        <div class="fieldGroup">
          <span class="fieldLabel">職業</span>
          <div class="fieldValue">{{ submittedData?.occupation?.label }}</div>
        </div>

        <!-- 電話番号 -->
        <div class="fieldGroup">
          <span class="fieldLabel">電話番号</span>
          <div class="fieldValue">{{ submittedData?.phoneNumber }}</div>
        </div>

        <!-- メールアドレス -->
        <div class="fieldGroup">
          <span class="fieldLabel">メールアドレス</span>
          <div class="fieldValue">{{ submittedData?.emailAddress }}</div>
        </div>
        <!-- メールマガジン配信 -->
        <div class="fieldGroup">
          <span class="fieldLabel">メールマガジン配信</span>
          <div class="fieldValue">
            {{ formatEmailSubscription(submittedData?.emailSubscription) }}
          </div>
        </div>
      </div>

      <div class="buttonArea">
        <button class="primaryButton" @click="handleSubmit">変更する</button>
        <button class="secondaryButton" @click="goBack">戻る</button>
      </div>
    </div>

    <v-dialog v-model="showDialog" max-width="500">
      <template v-slot:default>
        <v-card>
          <v-card-title>会員情報変更完了</v-card-title>
          <v-card-text>
            会員情報を変更しました。
            <br />
            お客様情報変更メールをお送りしましたので、ご確認ください。
          </v-card-text>
          <v-card-actions>
            <v-btn color="primary" @click="confirmChange" class="confirmBtn">
              会員情報へ
            </v-btn>
          </v-card-actions>
        </v-card>
      </template>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
.passwordToggle {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  color: #26499d;
}

.registrationContainer {
  min-height: 100vh;
  background-color: #f8f9fa;

  .regionHeaderTop {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background-color: #fff;
    border-bottom: 1px solid #e5e7eb;
    position: relative;

    .regionSelectorBackBtn {
      background: none;
      border: none;
      padding: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #f3f4f6;
        border-radius: 4px;
      }
    }
  }

  .formContentArea {
    .instructionText {
      background-color: #fff;
      padding: 16px;
      font-size: 14px;
      line-height: 1.6;
      color: #374151;
      padding-bottom: 40px;
    }

    .formFieldsList {
      background-color: #fff;
      overflow: hidden;
      padding: 0 10px 40px 10px;

      .fieldGroup {
        display: flex;
        flex-direction: column;
        padding: 16px 20px;
        border-bottom: 1px solid #f3f4f6 !important;

        &:last-child {
          border-bottom: none;
        }

        .fieldLabel {
          min-width: 120px;
          font-size: 14px;
          font-weight: 500;
          color: #26499d;
          padding-right: 16px;
          flex-shrink: 0;
        }

        .fieldValue {
          flex: 1;
          font-size: 14px;
          color: #1f2937;
          word-break: break-word;
        }
      }
    }

    .buttonArea {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 12px;
      background-color: #fff;
      margin-bottom: 24px;

      .primaryButton {
        width: 90%;
        height: 48px;
        background: #ed785f;
        color: #fff;
        border: none;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .secondaryButton {
        width: 90%;
        height: 48px;
        background-color: #e7f2fa;
        color: #26499d;
        border: 1px solid #9cbcd4;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        transition: all 0.3s ease;
      }
    }
  }
}

.confirmBtn {
  background-color: #26499d !important;
  color: white !important;
  width: 311px;
  height: 48px;
  border-radius: 8px !important;
  margin: 0 auto;
  margin-bottom: 24px;
}
</style>
