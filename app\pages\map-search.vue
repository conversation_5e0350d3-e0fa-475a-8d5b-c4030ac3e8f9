﻿<script lang="ts" setup>
declare global {
  interface Window {
    google: any
    initMap: () => void
  }
}

definePageMeta({
  hideFooter: true
})

import { useRuntimeConfig } from 'nuxt/app'
import OneWayRoundTripRouteSelect from '~/components/OneWayRoundTripRouteSelect.vue'
import { computed, onMounted, ref, nextTick } from 'vue'
import { useDisplay } from 'vuetify'

const config = useRuntimeConfig()

/*
 * マップ設定とズームレベル定義
 */
const MAP_CONFIG = {
  DEFAULT_ZOOM: 7,
  DEFAULT_CENTER: { lat: 35.6762, lng: 139.6503 },
  ZOOM_LEVELS: {
    PREFECTURE: { min: 5, max: 7 },
    AREA: { min: 8, max: 13 },
    BUS_STOP: { min: 14, max: 18 }
  }
}

/*
 * 型定義
 */
type MarkerRoleAlias = 'departure' | 'destination' | 'waypoint'

interface BusStop {
  busStopId: number
  busStopName: string
  busStopShortName: string
  hasToilet: boolean
  hasWifi: boolean
  mapInfo: string
  labels: string[]
  coordinates?: { lat: number; lng: number }
}

interface Area {
  areaId: number
  areaName: string
  displayOrder: number
  mapInfo: string
  busStops: BusStop[]
  coordinates?: { lat: number; lng: number }
}

interface Prefecture {
  prefectureId: number
  prefectureName: string
  displayOrder: number
  mapInfo: string
  areas: Area[]
  coordinates?: { lat: number; lng: number }
}

interface Region {
  regionId: number
  regionName: string
  displayOrder: number
  prefectures: Prefecture[]
}

type LocationType = 'prefecture' | 'area' | 'busStop'

interface MapMarker {
  id: string
  name: string
  type: LocationType
  coordinates: { lat: number; lng: number }
  role?: MarkerRoleAlias
  googleMarker?: any
  data: Prefecture | Area | BusStop
}

interface SelectedLocation {
  name: string
  id: string
  type: LocationType
  coordinates: { lat: number; lng: number }
  busStopInfo?: string
}

type LocationTypeAlias = '' | 'prefecture' | 'area' | 'busStop'

interface SearchFormData {
  tripType: 'oneway' | 'roundtrip'
  departure: string
  departureId: string
  departureType: LocationTypeAlias
  destination: string
  destinationId: string
  destinationType: LocationTypeAlias
  waypoints: Array<{
    id: string
    location: string
    locationId: string
    locationType: LocationTypeAlias
  }>
  date?: string
  time?: string
  direction?: 'departure' | 'arrival'
}

const { mobile } = useDisplay()

/*
 * レスポンシブデータ
 */
const mapContainer = ref<HTMLElement>()
const googleMap = ref<any>(null)
const isMapLoading = ref(true)
const regions = ref<Region[]>([])
const currentZoom = ref(MAP_CONFIG.DEFAULT_ZOOM)
const mapMarkers = ref<MapMarker[]>([])
const selectedMarker = ref<MapMarker | null>(null)
const showMarkerModal = ref(false)
const isPanelExpanded = ref(false)
const routePolyline = ref<any>(null)
const isUpdatingMarkers = ref(false)

const selectedLocations = ref({
  departure: null as SelectedLocation | null,
  destination: null as SelectedLocation | null,
  waypoints: [] as SelectedLocation[]
})

const formData = ref<SearchFormData>({
  tripType: 'oneway',
  departure: '',
  departureId: '',
  departureType: '',
  destination: '',
  destinationId: '',
  destinationType: '',
  waypoints: [],
  date: '',
  time: '',
  direction: 'departure'
})

/*
 * 計算プロパティ - マーカー情報表示
 */
const selectedLocationInfo = computed<string>(() => {
  if (!selectedMarker.value) return ''

  const marker = selectedMarker.value
  if (marker.type === 'busStop') {
    const busStop = marker.data as BusStop
    return `【バス停】${busStop.busStopName}`
  } else if (marker.type === 'area') {
    const area = marker.data as Area
    return `【エリア】${area.areaName}`
  } else if (marker.type === 'prefecture') {
    const prefecture = marker.data as Prefecture
    return `【都道府県】${prefecture.prefectureName}`
  }
  return ''
})

/*
 * 計算プロパティ - ズームレベルに応じた表示マーカー
 */
const visibleMarkers = computed<MapMarker[]>(() => {
  const zoom = currentZoom.value
  let baseVisibleMarkers: MapMarker[] = []

  if (
    zoom >= MAP_CONFIG.ZOOM_LEVELS.PREFECTURE.min &&
    zoom <= MAP_CONFIG.ZOOM_LEVELS.PREFECTURE.max
  ) {
    baseVisibleMarkers = mapMarkers.value.filter(
      (marker) => marker.type === 'prefecture'
    )
  } else if (
    zoom >= MAP_CONFIG.ZOOM_LEVELS.AREA.min &&
    zoom <= MAP_CONFIG.ZOOM_LEVELS.AREA.max
  ) {
    baseVisibleMarkers = mapMarkers.value.filter(
      (marker) => marker.type === 'area'
    )
  } else if (
    zoom >= MAP_CONFIG.ZOOM_LEVELS.BUS_STOP.min &&
    zoom <= MAP_CONFIG.ZOOM_LEVELS.BUS_STOP.max
  ) {
    baseVisibleMarkers = mapMarkers.value.filter(
      (marker) => marker.type === 'busStop'
    )
  }

  const selectedMarkers = mapMarkers.value.filter(
    (marker) =>
      marker.role === 'departure' ||
      marker.role === 'destination' ||
      marker.role === 'waypoint'
  )

  const allVisibleMarkers = [...baseVisibleMarkers]
  selectedMarkers.forEach((selectedMarker) => {
    if (!allVisibleMarkers.find((m) => m.id === selectedMarker.id)) {
      allVisibleMarkers.push(selectedMarker)
    }
  })

  return allVisibleMarkers
})

/*
 * テストデータ作成
 */
const createTestData = (): Region[] => {
  return [
    {
      regionId: 1,
      regionName: '関東',
      displayOrder: 1,
      prefectures: [
        {
          prefectureId: 13,
          prefectureName: '東京都',
          displayOrder: 1,
          mapInfo:
            '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.747252088458!2d139.6981904155935!3d35.68950168019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c00a789aef7%3A0x64f2139555f9285d!2z5pel5pys5pys5Lqs6YO95pys!5e0!3m2!1sja!2sjp!4v1694512345678!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
          areas: [
            {
              areaId: 1,
              areaName: '新宿区',
              displayOrder: 1,
              mapInfo:
                '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.6997678307626!2d139.69972241559353!3d35.68948718019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c4a600699eb%3A0x3202f530161cc70!2z5Lqs6YO95pys5Y-w5Yy6!5e0!3m2!1sja!2sjp!4v1694512456789!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
              busStops: [
                {
                  busStopId: 1,
                  busStopName: '新宿駅西口',
                  busStopShortName: '新宿西口',
                  hasToilet: true,
                  hasWifi: false,
                  mapInfo:
                    '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.696658068324!2d139.69936831559353!3d35.68948558019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c4a615f2a0b%3A0x507910f8cb060f0!2z5Lqs6YO95pys5Y-w5Yy65p2x5Lqs!5e0!3m2!1sja!2sjp!4v1694512567890!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
                  labels: ['map_device']
                },
                {
                  busStopId: 2,
                  busStopName: '新宿三丁目',
                  busStopShortName: '新宿3丁目',
                  hasToilet: false,
                  hasWifi: true,
                  mapInfo:
                    '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.780423121436!2d139.7074515155935!3d35.68957288019615!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c4e1e0f9b0f%3A0x318e5a6e51564e1b!2z5Lqs6YO95LiW55WM!5e0!3m2!1sja!2sjp!4v1694512678901!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
                  labels: ['map']
                }
              ]
            },
            {
              areaId: 2,
              areaName: '渋谷区',
              displayOrder: 2,
              mapInfo:
                '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.157263634711!2d139.7016128155938!3d35.65857618020021!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c98bd8b6c2f%3A0xc06096bc34ed4e4b!2z5bqc5Lqs5Lqe5bed!5e0!3m2!1sja!2sjp!4v1694512789012!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
              busStops: [
                {
                  busStopId: 3,
                  busStopName: '渋谷駅',
                  busStopShortName: '渋谷',
                  hasToilet: true,
                  hasWifi: true,
                  mapInfo:
                    '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3241.155666649352!2d139.7014268155938!3d35.65857458020021!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x60188c98a4e6146b%3A0x58283425193cc19c!2z5bqc5Lqs5Lqe5bed5rW3!5e0!3m2!1sja!2sjp!4v1694512890123!5m2!1sja!2sjp" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
                  labels: ['map_device']
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

/*
 * mapInfo文字列から座標を解析
 */
const parseCoordinatesFromMapInfo = (
  mapInfo: string
): { lat: number; lng: number } | null => {
  if (!mapInfo) return null

  try {
    const lngMatch = mapInfo.match(/!2d([0-9.-]+)/)
    const latMatch = mapInfo.match(/!3d([0-9.-]+)/)

    if (
      lngMatch &&
      latMatch &&
      typeof lngMatch[1] === 'string' &&
      typeof latMatch[1] === 'string'
    ) {
      return {
        lng: parseFloat(lngMatch[1]),
        lat: parseFloat(latMatch[1])
      }
    }
  } catch (error) {
    console.error('Failed to parse coordinates from mapInfo:', error)
  }

  return null
}

/*
 * データからマーカーオブジェクト作成
 */
const createMarkerFromData = (
  data: Prefecture | Area | BusStop,
  type: 'prefecture' | 'area' | 'busStop'
): MapMarker | null => {
  const coords = parseCoordinatesFromMapInfo(data.mapInfo)
  if (!coords) return null

  const getId = () => {
    if (type === 'prefecture') return (data as Prefecture).prefectureId
    if (type === 'area') return (data as Area).areaId
    return (data as BusStop).busStopId
  }

  const getName = () => {
    if (type === 'prefecture') return (data as Prefecture).prefectureName
    if (type === 'area') return (data as Area).areaName
    return (data as BusStop).busStopName
  }

  return {
    id: `${type}-${getId()}`,
    name: getName(),
    type,
    coordinates: coords,
    data
  }
}

/*
 * マーカー処理とデータ読み込み
 */
function addBusStopMarkers(busStops: BusStop[], markers: MapMarker[]) {
  busStops.forEach((busStop) => {
    const busStopMarker = createMarkerFromData(busStop, 'busStop')
    if (busStopMarker) markers.push(busStopMarker)
  })
}

const processMapMarkers = (): void => {
  const markers: MapMarker[] = []

  regions.value.forEach((region) => {
    region.prefectures.forEach((prefecture) => {
      const prefectureMarker = createMarkerFromData(prefecture, 'prefecture')
      if (prefectureMarker) markers.push(prefectureMarker)

      prefecture.areas.forEach((area) => {
        const areaMarker = createMarkerFromData(area, 'area')
        if (areaMarker) markers.push(areaMarker)

        addBusStopMarkers(area.busStops, markers)
      })
    })
  })

  mapMarkers.value = markers
  updateMarkersOnMap()
}

const loadMapData = async (): Promise<void> => {
  try {
    regions.value = createTestData()
    processMapMarkers()
  } catch (error) {
    console.error('Failed to load map data:', error)
  }
}

/*
 * マーカーアイコン作成
 */
const createMarkerContent = (role?: MarkerRoleAlias) => {
  if (role === 'departure') {
    const svg = `
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <circle cx="16" cy="16" r="16" fill="#ffffff" stroke="#4caf50" stroke-width="2"/>
        <text x="16" y="21" font-size="16" font-weight="bold" text-anchor="middle" fill="#000000">発</text>
      </svg>
    `
    return {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svg),
      scaledSize: new window.google.maps.Size(32, 32)
    }
  } else if (role === 'destination') {
    const svg = `
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <circle cx="16" cy="16" r="16" fill="#ffffff" stroke="#f44336" stroke-width="2"/>
        <text x="16" y="21" font-size="16" font-weight="bold" text-anchor="middle" fill="#000000">着</text>
      </svg>
    `
    return {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svg),
      scaledSize: new window.google.maps.Size(32, 32)
    }
  } else if (role === 'waypoint') {
    const svg = `
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <circle cx="16" cy="16" r="16" fill="#ffffff" stroke="#ff9800" stroke-width="2"/>
        <text x="16" y="21" font-size="14" font-weight="bold" text-anchor="middle" fill="#000000">経</text>
      </svg>
    `
    return {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svg),
      scaledSize: new window.google.maps.Size(32, 32)
    }
  } else {
    return {
      path: window.google.maps.SymbolPath.CIRCLE,
      fillColor: '#26499d',
      fillOpacity: 1,
      strokeColor: '#fff',
      strokeWeight: 2,
      scale: 8
    }
  }
}

/*
 * Google Map初期化
 */
const initGoogleMap = async (): Promise<void> => {
  if (!mapContainer.value) return

  try {
    const map = new window.google.maps.Map(mapContainer.value, {
      zoom: MAP_CONFIG.DEFAULT_ZOOM,
      minZoom: 5,
      maxZoom: 18,
      center: MAP_CONFIG.DEFAULT_CENTER,
      mapTypeId: 'roadmap'
    })

    googleMap.value = map

    map.addListener('zoom_changed', () => {
      const newZoom = map.getZoom()
      if (newZoom !== currentZoom.value) {
        currentZoom.value = newZoom as number
        setTimeout(() => {
          updateMarkersOnMap()
        }, 200)
      }
    })

    await loadMapData()
    isMapLoading.value = false
  } catch (error) {
    console.error('Failed to initialize Google Map:', error)
    await loadMapData()
    isMapLoading.value = false
  }
}

/*
 * マーカー管理
 */
const clearAllMarkers = () => {
  mapMarkers.value.forEach((marker) => {
    if (marker.googleMarker) {
      try {
        marker.googleMarker.setMap(null)
        marker.googleMarker.setVisible(false)
        if (window.google?.maps?.event) {
          window.google.maps.event.clearInstanceListeners(marker.googleMarker)
        }
        marker.googleMarker = null
      } catch (error) {
        console.error(`Error clearing marker ${marker.name}:`, error)
      }
    }
  })
}

const updateMarkersOnMap = (): void => {
  if (isUpdatingMarkers.value) return

  if (googleMap.value && window.google && window.google.maps) {
    isUpdatingMarkers.value = true
    clearAllMarkers()

    setTimeout(() => {
      const markersToCreate = visibleMarkers.value

      markersToCreate.forEach((marker) => {
        const googleMarker = new window.google.maps.Marker({
          position: marker.coordinates,
          map: googleMap.value,
          title: marker.name,
          icon: createMarkerContent(marker.role),
          optimized: true,
          visible: true
        })

        marker.googleMarker = googleMarker

        googleMarker.addListener('click', () => {
          handleMarkerClick(marker)
        })
      })

      isUpdatingMarkers.value = false
    }, 100)
  }

  setTimeout(() => {
    drawRoutePolyline()
  }, 200)
}

/*
 * ルート描画
 */
const drawRoutePolyline = () => {
  if (!googleMap.value || !window.google || !window.google.maps) return

  if (routePolyline.value) {
    routePolyline.value.setMap(null)
    routePolyline.value = null
  }

  const routePoints = []

  if (selectedLocations.value.departure) {
    routePoints.push(selectedLocations.value.departure.coordinates)
  }

  if (
    selectedLocations.value.waypoints &&
    selectedLocations.value.waypoints.length > 0
  ) {
    selectedLocations.value.waypoints.forEach((waypoint) => {
      routePoints.push(waypoint.coordinates)
    })
  }

  if (selectedLocations.value.destination) {
    routePoints.push(selectedLocations.value.destination.coordinates)
  }

  if (routePoints.length < 2) return

  routePolyline.value = new window.google.maps.Polyline({
    path: routePoints,
    geodesic: true,
    strokeColor: '#1976d2',
    strokeOpacity: 0.8,
    strokeWeight: 4
  })

  routePolyline.value.setMap(googleMap.value)
}

/*
 * 位置データ設定とイベント処理
 */
const findMarkerByLocation = (
  location: SelectedLocation
): MapMarker | undefined => {
  return mapMarkers.value.find(
    (marker) =>
      marker.coordinates.lat === location.coordinates.lat &&
      marker.coordinates.lng === location.coordinates.lng
  )
}

const setLocationData = (
  marker: MapMarker,
  locationType: 'departure' | 'destination' | 'waypoint'
) => {
  const getLocationId = (marker: MapMarker): string => {
    if (marker.type === 'prefecture') {
      return (marker.data as Prefecture).prefectureId.toString()
    } else if (marker.type === 'area') {
      return (marker.data as Area).areaId.toString()
    } else {
      return (marker.data as BusStop).busStopId.toString()
    }
  }

  const locationData: SelectedLocation = {
    name: marker.name,
    id: getLocationId(marker),
    type: marker.type,
    coordinates: marker.coordinates,
    busStopInfo:
      marker.type === 'busStop' ? selectedLocationInfo.value : undefined
  }

  if (locationType === 'departure') {
    mapMarkers.value.forEach((m) => {
      if (m.role === 'departure') {
        m.role = undefined
      }
    })

    selectedLocations.value.departure = locationData
    formData.value.departure = marker.name
    formData.value.departureId = locationData.id
    formData.value.departureType = marker.type
  } else if (locationType === 'destination') {
    mapMarkers.value.forEach((m) => {
      if (m.role === 'destination') {
        m.role = undefined
      }
    })

    selectedLocations.value.destination = locationData
    formData.value.destination = marker.name
    formData.value.destinationId = locationData.id
    formData.value.destinationType = marker.type
  } else if (locationType === 'waypoint') {
    selectedLocations.value.waypoints.push(locationData)
    formData.value.waypoints = selectedLocations.value.waypoints.map(
      (waypoint, index) => ({
        id: `waypoint-${Date.now()}-${index}`,
        location: waypoint.name,
        locationId: waypoint.id,
        locationType: waypoint.type
      })
    )
  }

  marker.role = locationType

  setTimeout(() => {
    updateMarkersOnMap()
  }, 50)
}

const handleSetDeparture = (): void => {
  if (!selectedMarker.value) return
  setLocationData(selectedMarker.value, 'departure')
  showMarkerModal.value = false
}

const handleSetDestination = (): void => {
  if (!selectedMarker.value) return
  setLocationData(selectedMarker.value, 'destination')
  showMarkerModal.value = false
}

const handleSetWaypoint = (): void => {
  if (!selectedMarker.value) return
  setLocationData(selectedMarker.value, 'waypoint')

  nextTick(() => {
    handleFormChange({
      ...formData.value,
      waypoints: [...formData.value.waypoints]
    })
  })

  showMarkerModal.value = false
}

const handleMarkerClick = (marker: MapMarker): void => {
  selectedMarker.value = marker
  showMarkerModal.value = true
}

/*
 * Google Maps API読み込み
 */
const loadGoogleMapsAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (window.google && window.google.maps) {
      resolve()
      return
    }

    const apiKey = config.public.googleMapsApiKey
    if (!apiKey) {
      reject(new Error('Google Maps API key is not configured'))
      return
    }

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=marker,geometry&loading=async&callback=initMap`
    script.async = true
    script.defer = true

    window.initMap = () => {
      resolve()
    }

    script.onerror = () => {
      reject(new Error('Failed to load Google Maps API'))
    }

    document.head.appendChild(script)
  })
}

/*
 * フォーム処理とナビゲーション
 */
const handleFormChange = (newFormData: SearchFormData) => {
  if (newFormData.departure === '' && formData.value.departure !== '') {
    mapMarkers.value.forEach((m) => {
      if (m.role === 'departure') {
        m.role = undefined
      }
    })
    selectedLocations.value.departure = null
  }

  if (newFormData.destination === '' && formData.value.destination !== '') {
    mapMarkers.value.forEach((m) => {
      if (m.role === 'destination') {
        m.role = undefined
      }
    })
    selectedLocations.value.destination = null
  }

  if (newFormData.waypoints.length < selectedLocations.value.waypoints.length) {
    const removedWaypoints = selectedLocations.value.waypoints.slice(
      newFormData.waypoints.length
    )
    removedWaypoints.forEach((removedWaypoint) => {
      const waypointMarker = findMarkerByLocation(removedWaypoint)
      if (waypointMarker) {
        waypointMarker.role = undefined
      }
    })

    selectedLocations.value.waypoints = selectedLocations.value.waypoints.slice(
      0,
      newFormData.waypoints.length
    )
  }

  const updatedFormData = {
    ...formData.value,
    ...newFormData
  }

  formData.value = updatedFormData
  updateMarkersOnMap()
}

const handleSearch = async (searchData: SearchFormData): Promise<void> => {
  await navigateTo({
    path: '/bus-service-selection',
    query: {
      data: JSON.stringify(searchData)
    }
  })
}

const handleBack = (): void => {
  window.history.back()
}

const togglePanel = (): void => {
  isPanelExpanded.value = !isPanelExpanded.value
}

/*
 * ライフサイクル
 */
onMounted(async () => {
  isMapLoading.value = true
  try {
    await loadGoogleMapsAPI()
    await initGoogleMap()
  } catch (error) {
    console.error('Failed to initialize map:', error)
    await loadMapData()
  } finally {
    isMapLoading.value = false
  }
})
</script>
<template>
  <div class="map-search-container">
    <!-- ヘッダーナビゲーション -->
    <div class="header-section">
      <div class="header-content">
        <v-btn icon variant="text" size="small" @click="handleBack">
          <v-icon>mdi-arrow-left</v-icon>
        </v-btn>

        <div class="header-title">
          <h3>地図から検索</h3>
        </div>

        <div class="header-spacer"></div>
      </div>
    </div>

    <!-- マップエリア -->
    <div class="map-section">
      <div class="map-container" ref="mapContainer">
        <div class="map-instruction">
          地図を操作して、地点を選択してください。
        </div>
      </div>
    </div>

    <!-- ボトム詳細パネル -->
    <div class="detail-panel" :class="{ expanded: isPanelExpanded }">
      <div class="panel-header" @click="togglePanel">
        <span class="panel-title">選択中の内容を確認・検索</span>
        <v-btn icon variant="text" size="small" class="toggle-button">
          <v-icon>
            {{ isPanelExpanded ? 'mdi-chevron-down' : 'mdi-chevron-up' }}
          </v-icon>
        </v-btn>
      </div>

      <div v-if="isPanelExpanded" class="panel-content">
        <OneWayRoundTripRouteSelect
          :departure-config="{
            value: formData.departure,
            readonly: true,
            showMapButton: false
          }"
          :destination-config="{
            value: formData.destination,
            readonly: true,
            showMapButton: false
          }"
          :waypoint-config="{
            readonly: true,
            showMapButton: false
          }"
          :initial-form-data="formData"
          @search="handleSearch"
          @form-change="handleFormChange"
        />
      </div>
    </div>

    <!-- マーカー詳細モーダル -->
    <v-dialog v-model="showMarkerModal" max-width="400" class="marker-modal">
      <v-card v-if="selectedMarker" class="marker-card">
        <v-card-title class="modal-header">
          <v-chip rounded variant="outlined" size="small" class="type-tag">
            {{
              selectedMarker.type === 'busStop'
                ? '停留所'
                : selectedMarker.type === 'area'
                ? 'エリア'
                : '都道府県'
            }}
          </v-chip>

          <v-btn
            icon
            variant="text"
            size="small"
            @click="showMarkerModal = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="modal-content">
          <div class="location-info">
            <h4 class="location-name">{{ selectedMarker.name }}</h4>
            <p v-if="selectedLocationInfo" class="location-detail">
              {{ selectedLocationInfo }}
            </p>
          </div>

          <div class="action-buttons">
            <v-btn
              color="primary"
              rounded="false"
              class="action-button"
              @click="handleSetDeparture"
            >
              出発地に設定
            </v-btn>

            <v-btn
              color="primary"
              rounded="false"
              class="action-button"
              @click="handleSetDestination"
            >
              到着地に設定
            </v-btn>

            <v-btn
              color="primary"
              rounded="false"
              class="action-button"
              @click="handleSetWaypoint"
            >
              経由地に設定
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- マップローディング状態 -->
    <div v-if="isMapLoading" class="loading-overlay">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
        class="loading-spinner"
      ></v-progress-circular>
      <p class="loading-text">地図を読み込み中...</p>
    </div>
  </div>
</template>
<style scoped lang="scss">
.map-search-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #b2d1ff;
  position: relative;
  overflow: hidden;
}

.header-section {
  background: #ffffff;
  padding: 0 8px;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.15);
  z-index: 1000;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin: 0 auto;
}

.header-title {
  flex: 1;
  text-align: center;
}

.header-title h3 {
  color: #26499d;
  font-size: 20px;
  font-weight: 400;
  margin: 0;
}

.header-spacer {
  width: 40px;
}

.map-section {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 5px 5px 0 0;
  overflow: hidden;
}

.map-instruction {
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  color: #3b3b3b;
  font-size: 12px;
  line-height: 20px;
  font-weight: 400;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 12px;
  border-radius: 4px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-panel {
  background: #ffffff;
  border-radius: 10px 10px 0 0;
  box-shadow: 0 -4px 10px 0 rgba(33, 65, 88, 0.12);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(calc(100% - 50px));
  z-index: 1000;
}

.detail-panel.expanded {
  transform: translateY(0);
}

.panel-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 11px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  height: 50px;
}

.panel-title {
  flex: 1;
  text-align: center;
  color: #7d7d7d;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.toggle-button {
  position: absolute;
  right: 0;
}

.panel-content {
  padding: 16px;
  max-height: 350px;
  overflow-y: auto;
}

.marker-modal :deep(.v-dialog) {
  margin: 16px;
}

.marker-card {
  border-radius: 10px;
  border: 1px solid #9cbcd4;
  overflow: hidden;
  background: #ffffff;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 11px 14px;
  border-bottom: none;
}

.type-tag {
  background: #ffffff;
  border: 1px solid #7d7d7d;
  padding: 5px 10px;
  font-size: 12px;
}

.modal-content {
  padding: 0 14px 11px 14px;
}

.location-info {
  margin-bottom: 9px;
}

.location-name {
  color: #000000;
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.location-detail {
  color: #7d7d7d;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-button {
  background: #26499d !important;
  height: 49px;
  font-size: 16px;
  font-weight: 400;
  color: #ffffff !important;
}

.action-button:hover {
  background: #1e3d8a !important;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(178, 209, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-text {
  margin-top: 16px;
  color: #26499d;
  font-size: 16px;
  font-weight: 500;
}

.loading-spinner {
  width: 64px;
  height: 64px;
}

@media (min-width: 960px) {
  .map-search-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .detail-panel {
    max-width: 600px;
    left: 50%;
    transform: translateX(-50%) translateY(calc(100% - 50px));
  }

  .detail-panel.expanded {
    transform: translateX(-50%) translateY(0);
  }

  .panel-content {
    max-height: 500px;
  }
}

@media (max-width: 375px) {
  .header-section {
    padding: 8px 12px;
  }

  .panel-content {
    padding: 12px;
  }
}

.map-container:focus-within {
  outline: 2px solid #26499d;
  outline-offset: 2px;
}

@media (prefers-contrast) {
  .marker-card {
    border-width: 2px;
  }

  .action-button {
    border: 2px solid #ffffff;
  }
}

@media (prefers-reduced-motion: reduce) {
  .detail-panel {
    transition: none;
  }

  .loading-spinner {
    animation: none;
  }
}
</style>
