<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, onUnmounted, watch } from 'vue'
import SearchResult from '../../components/OriginDestHistoryInput.vue'
import { isLogin } from '~/utils/auth'
import { useSpotStore } from '~/stores/tourist-facilities'
import { useSpecialSpotStore } from '~/stores/special-events'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const showSearchResults = ref(true)
const spotStore = useSpotStore()
const specialStore = useSpecialSpotStore()

/* ログイン状態の判定（ログインしていない場合trueを返す） */
const isLoggedIn = computed(() => isLogin())

definePageMeta({
  hideFooter: true
})

/* バス停情報のインターフェース */
interface BusStop {
  bus_stop_id: number
  bus_stop_name: string
  bus_stop_short_name: string
  latitude: number
  longitude: number
  has_toilet: boolean
  has_wifi: boolean
  labels: string[]
}

/* エリア情報のインターフェース */
interface Area {
  area_id: number
  area_name: string
  display_order: number
  latitude: number
  longitude: number
  bus_stops?: BusStop[]
}

/* 都道府県情報のインターフェース */
interface Prefecture {
  prefecture_id: number
  prefecture_name: string
  display_order: number
  latitude: number
  longitude: number
  areas: Area[]
}

/* リージョン情報のインターフェース */
interface Region {
  region_id: number
  region_name: string
  display_order: number
  prefectures?: Prefecture[]
}

/* 検索データアイテムのインターフェース */
interface DataItem {
  id: number
  name: string
  type: string
}

/* 選択ノードのインターフェース */
interface SelectedNode {
  id: number
  name: string
  type: 'prefecture' | 'area' | 'bus_stop'
  level: 1 | 2 | 3
}

/* 観光地情報のインターフェース */
interface TouristSpot {
  id: number
  name: string
  [key: string]: any
}

/* リージョンデータ（メイン） */
const jsonData = {
  data: [
    {
      region_id: 1,
      region_name: '関東',
      display_order: 1,
      prefectures: [
        {
          prefecture_id: 13,
          prefecture_name: '東京都',
          display_order: 1,
          latitude: 35.6895,
          longitude: 139.6917,
          areas: [
            {
              area_id: 1,
              area_name: '新宿区',
              display_order: 1,
              latitude: 35.6938,
              longitude: 139.7034,
              bus_stops: [
                {
                  bus_stop_id: 1,
                  bus_stop_name: '新宿駅西口',
                  bus_stop_short_name: '新宿西口',
                  latitude: 35.6909,
                  longitude: 139.6995,
                  has_toilet: true,
                  has_wifi: false,
                  labels: ['地図・設備']
                },
                {
                  bus_stop_id: 2,
                  bus_stop_name: '新宿三丁目',
                  bus_stop_short_name: '新宿3丁目',
                  latitude: 35.6901,
                  longitude: 139.7052,
                  has_toilet: false,
                  has_wifi: true,
                  labels: ['地図']
                }
              ]
            },
            {
              area_id: 2,
              area_name: '渋谷区',
              display_order: 2,
              latitude: 35.6617,
              longitude: 139.704,
              bus_stops: [
                {
                  bus_stop_id: 3,
                  bus_stop_name: '渋谷駅東口',
                  bus_stop_short_name: '渋谷東口',
                  latitude: 35.658,
                  longitude: 139.7016,
                  has_toilet: false,
                  has_wifi: true,
                  labels: ['地図']
                }
              ]
            }
          ]
        },
        {
          prefecture_id: 14,
          prefecture_name: '神奈川県',
          display_order: 2,
          latitude: 35.4478,
          longitude: 139.6425,
          areas: [
            {
              area_id: 3,
              area_name: '横浜市',
              display_order: 1,
              latitude: 35.4437,
              longitude: 139.638,
              bus_stops: [
                {
                  bus_stop_id: 4,
                  bus_stop_name: '横浜駅西口',
                  bus_stop_short_name: '横浜西口',
                  latitude: 35.4658,
                  longitude: 139.6201,
                  has_toilet: true,
                  has_wifi: false,
                  labels: ['地図・設備']
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

/* ログイン状態用リージョンデータ */
const regionData: Region[] = [
  {
    region_id: 1,
    region_name: '予約履歴から',
    display_order: 1,
    prefectures: [
      {
        prefecture_id: 1,
        prefecture_name: '北海道',
        display_order: 1,
        latitude: 43.0642,
        longitude: 141.3469,
        areas: []
      }
    ]
  },
  {
    region_id: 2,
    region_name: 'お気に入り停留所',
    display_order: 2,
    prefectures: [
      {
        prefecture_id: 2,
        prefecture_name: '青森県',
        display_order: 1,
        latitude: 40.8244,
        longitude: 140.74,
        areas: [
          {
            area_id: 3,
            area_name: '青森市',
            display_order: 1,
            latitude: 40.8244,
            longitude: 140.74
          }
        ]
      }
    ]
  }
]

/* 検索用データリスト */
const dataList: DataItem[] = [
  { id: 1, name: '東京都', type: '东京都' },
  { id: 2, name: '大阪', type: '大阪府' }
]

const searchValue = ref('')
const searchResults = ref<DataItem[]>([])
const activeTag = ref<string>('')
const expandedRegions = ref<Set<string>>(new Set())
const selectedNode = ref<SelectedNode | null>(null)
const selectedItems = ref<Set<string>>(new Set())
const isProcessing = ref(false)
const isNavigating = ref(false)

/* リージョンタグリストを取得（jsonDataから抽出） */
const regionTags = computed(() => {
  return jsonData.data.map((region) => region.region_name)
})

/* アクティブなリージョンデータを取得 */
const currentRegionData = computed<Region | undefined>(() => {
  return jsonData.data.find((region) => region.region_name === activeTag.value)
})

/* モードを取得（ルートクエリから、初期値は'destination'） */
const mode = computed(() => route.query.mode || 'destination')

/* 確定ボタンのテキストを計算（選択ノードがあればノード名を表示） */
const confirmButtonText = computed(() => {
  if (!selectedNode.value) {
    return '決定'
  }
  return `決定（${selectedNode.value.name}）`
})

/* 検索処理：検索値に基づいてデータリストを絞り込み、結果を表示/非表示 */
const handleSearch = () => {
  if (searchValue.value.trim() === '') {
    searchResults.value = []
    showSearchResults.value = false
    return
  }
  searchResults.value = dataList.filter((item) =>
    item.name.includes(searchValue.value)
  )
  showSearchResults.value = true
}

/* アクティブなタグを設定：処理/ナビゲーション中は無効 */
function setActiveTag(tag: string): void {
  if (isProcessing.value || isNavigating.value) return
  activeTag.value = tag
  expandedRegions.value.clear()
  selectedNode.value = null
  selectedItems.value.clear()
}

/* リージョンの展開/折りたたみ切り替え */
function toggleRegion(regionId: string): void {
  if (isProcessing.value || isNavigating.value) return

  if (expandedRegions.value.has(regionId)) {
    expandedRegions.value.delete(regionId)
  } else {
    expandedRegions.value.add(regionId)
  }
}

/* 都道府県配下の全ての子要素（エリア・バス停）IDを取得 */
function getAllChildIdsForPrefecture(prefecture: Prefecture): string[] {
  const childIds: string[] = []

  prefecture.areas.forEach((area) => {
    childIds.push(`area_${area.area_id}`)
    area.bus_stops?.forEach((busStop) => {
      childIds.push(`bus_${busStop.bus_stop_id}`)
    })
  })

  return childIds
}

/* ノードが選択されているか判定（選択ノードのタイプ/ID一致、または選択アイテムに含まれる場合） */
function isNodeSelected(
  id: string,
  type: 'prefecture' | 'area' | 'bus_stop',
  nodeId: number
): boolean {
  return (
    (selectedNode.value?.type === type && selectedNode.value.id === nodeId) ||
    selectedItems.value.has(id)
  )
}

/* 都道府県が完全に選択されているか判定 */
function isPrefectureFullySelected(prefecture: Prefecture) {
  return selectedItems.value.has(`prefecture_${prefecture.prefecture_id}`)
}

/* エリアが完全に選択されているか判定 */
function isAreaFullySelected(area: Area) {
  return selectedItems.value.has(`area_${area.area_id}`)
}

/* 都道府県配下に部分選択があるか判定 */
function hasPartialSelection(prefecture: Prefecture): boolean {
  if (!selectedNode.value) return false

  if (selectedNode.value.type === 'area') {
    return prefecture.areas.some(
      (area) => area.area_id === selectedNode.value!.id
    )
  }

  if (selectedNode.value.type === 'bus_stop') {
    return prefecture.areas.some((area) =>
      area.bus_stops?.some((bus) => bus.bus_stop_id === selectedNode.value!.id)
    )
  }

  return false
}

/* エリア配下に部分選択（バス停）があるか判定 */
function hasPartialSelectionArea(area: Area): boolean {
  if (!selectedNode.value) return false

  if (selectedNode.value.type === 'bus_stop') {
    return (
      area.bus_stops?.some(
        (bus) => bus.bus_stop_id === selectedNode.value!.id
      ) || false
    )
  }

  return false
}

/* 都道府県選択処理：チェック時は配下全要素を選択、解除時は選択をリセット */
function handlePrefectureSelection(prefecture: Prefecture, event: Event): void {
  if (isProcessing.value || isNavigating.value) return

  const target = event.target as HTMLInputElement

  if (target.checked) {
    selectedNode.value = {
      id: prefecture.prefecture_id,
      name: prefecture.prefecture_name,
      type: 'prefecture',
      level: 1
    }

    selectedItems.value.clear()
    selectedItems.value.add(`prefecture_${prefecture.prefecture_id}`)
    const allChildIds = getAllChildIdsForPrefecture(prefecture)
    allChildIds.forEach((id) => selectedItems.value.add(id))
  } else {
    selectedNode.value = null
    selectedItems.value.clear()
  }
}

/* エリア選択処理：チェック時は配下バス停を選択、解除時は選択をリセット */
function handleAreaSelection(area: Area, event: Event): void {
  if (isProcessing.value || isNavigating.value) return

  const target = event.target as HTMLInputElement
  selectedNode.value = null
  selectedItems.value.clear()

  if (target.checked) {
    selectedNode.value = {
      id: area.area_id,
      name: area.area_name,
      type: 'area',
      level: 2
    }

    area.bus_stops?.forEach((busStop) => {
      selectedItems.value.add(`bus_${busStop.bus_stop_id}`)
    })
    selectedItems.value.add(`area_${area.area_id}`)
  }
}

/* バス停選択処理：チェック時は対象バス停を選択、解除時は選択をリセット */
function handleBusStopSelection(busStop: BusStop, event: Event): void {
  if (isProcessing.value || isNavigating.value) return

  const target = event.target as HTMLInputElement
  selectedNode.value = null
  selectedItems.value.clear()

  if (target.checked) {
    selectedNode.value = {
      id: busStop.bus_stop_id,
      name: busStop.bus_stop_name,
      type: 'bus_stop',
      level: 3
    }
    selectedItems.value.add(`bus_${busStop.bus_stop_id}`)
  }
}

/* ログイン状態：都道府県が選択されているか判定 */
function isLoggedInPrefectureSelected(prefecture: Prefecture): boolean {
  return (
    (selectedNode.value?.type === 'prefecture' &&
      selectedNode.value.id === prefecture.prefecture_id) ||
    selectedItems.value.has(`prefecture_${prefecture.prefecture_id}`)
  )
}

/* ログイン状態：エリアが選択されているか判定 */
function isLoggedInAreaSelected(area: Area): boolean {
  return (
    (selectedNode.value?.type === 'area' &&
      selectedNode.value.id === area.area_id) ||
    selectedItems.value.has(`area_${area.area_id}`)
  )
}

/* ログイン状態：都道府県配下に部分選択があるか判定 */
function hasLoggedInPartialSelection(prefecture: Prefecture): boolean {
  if (!selectedNode.value) return false

  if (selectedNode.value.type === 'area') {
    return prefecture.areas.some(
      (area) => area.area_id === selectedNode.value!.id
    )
  }

  return false
}

/* ログイン状態：都道府県選択処理 */
function handleLoggedInPrefectureSelection(
  prefecture: Prefecture,
  event: Event
): void {
  if (isProcessing.value || isNavigating.value) return

  const target = event.target as HTMLInputElement

  if (target.checked) {
    selectedNode.value = {
      id: prefecture.prefecture_id,
      name: prefecture.prefecture_name,
      type: 'prefecture',
      level: 1
    }
    selectedItems.value.clear()

    selectedItems.value.add(`prefecture_${prefecture.prefecture_id}`)
    prefecture.areas.forEach((area) => {
      selectedItems.value.add(`area_${area.area_id}`)
      area.bus_stops?.forEach((bus) =>
        selectedItems.value.add(`bus_${bus.bus_stop_id}`)
      )
    })
  } else {
    selectedNode.value = null
    selectedItems.value.clear()
  }
}

/* ログイン状態：エリア選択処理 */
function handleLoggedInAreaSelection(area: Area, event: Event): void {
  if (isProcessing.value || isNavigating.value) return

  const target = event.target as HTMLInputElement

  selectedNode.value = null
  selectedItems.value.clear()

  if (target.checked) {
    selectedNode.value = {
      id: area.area_id,
      name: area.area_name,
      type: 'area',
      level: 2
    }
    selectedItems.value.add(`area_${area.area_id}`)
  }
}



/* ラベルリストを文字列に変換（カンマ区切り） */
function getTagText(labels: string[]): string {
  return labels.join(', ')
}

/* 確定処理：選択ノードをストアに保存し、前のページに戻る */
const handleConfirm = async () => {
  if (isProcessing.value || isNavigating.value || !selectedNode.value) {
    return
  }

  isProcessing.value = true

  try {
    const selectedData: TouristSpot = {
      id: selectedNode.value.id,
      name: selectedNode.value.name
    }

    // 到着地データをストアに設定
    spotStore.setCurrentTouristData(selectedData)
    specialStore.setCurrentTouristData(selectedData)

    await nextTick()
    await new Promise((resolve) => setTimeout(resolve, 50))

    isNavigating.value = true

    selectedNode.value = null
    selectedItems.value.clear()

    setTimeout(() => {
      window.history.back()
    }, 100)
  } catch (error) {
    isProcessing.value = false
    isNavigating.value = false
    console.error(error)
  }
}

/* 検索結果選択処理：選択アイテムの名前を検索欄に設定、対応する都道府県を選択状態に */
function handleSearchResultSelect(item: DataItem) {
  searchValue.value = item.name
  showSearchResults.value = false

  selectedNode.value = null
  selectedItems.value.clear()
  expandedRegions.value.clear()

  for (const region of jsonData.data) {
    for (const prefecture of region.prefectures) {
      if (prefecture.prefecture_name === item.name) {
        selectedNode.value = {
          id: prefecture.prefecture_id,
          name: prefecture.prefecture_name,
          type: 'prefecture',
          level: 1
        }

        expandedRegions.value.add(`prefecture_${prefecture.prefecture_id}`)

        selectedItems.value.add(`prefecture_${prefecture.prefecture_id}`)
        prefecture.areas?.forEach((area) => {
          selectedItems.value.add(`area_${area.area_id}`)
          area.bus_stops?.forEach((bus) => {
            selectedItems.value.add(`bus_${bus.bus_stop_id}`)
          })
        })

        return
      }
    }
  }
}

/* 検索値監視：値が空の場合は状態リセット、一致するデータがあれば選択処理を実行 */
watch(searchValue, (newVal) => {
  if (!newVal) {
    selectedNode.value = null
    selectedItems.value.clear()
    expandedRegions.value.clear()
    return
  }

  const match = dataList.find((item) => item.name.includes(newVal))
  if (match) {
    handleSearchResultSelect(match)
  }
})

/* マウント時の初期化：アクティブタグ設定、状態リセット */
onMounted(() => {
  if (regionTags.value.length > 0) {
    activeTag.value = regionTags.value[0]
  }

  isProcessing.value = false
  isNavigating.value = false
})

/* アンマウント時の状態リセット */
onUnmounted(() => {
  isProcessing.value = false
  isNavigating.value = false
})

/* ルート変更時の状態リセット */
watch(
  () => route.fullPath,
  (newPath, oldPath) => {
    if (newPath !== oldPath) {
      isProcessing.value = false
      isNavigating.value = false
    }
  }
)
</script>
<template>
  <div class="regionSelector">
    <div class="regionSelectorHeader">
      <BaseHeader title="到着地" :showBack="true" :showRightIcon="false" />

      <div>
        <v-text-field
          prepend-inner-icon="mdi-magnify"
          color="grey"
          v-model="searchValue"
          placeholder="到着地を入力"
          @input="handleSearch"
        ></v-text-field>

        <SearchResult
          :searchResults="searchResults"
          @select="handleSearchResultSelect"
          v-if="showSearchResults"
        />
      </div>

      <div v-if="isLoggedIn" class="regionSelectorList loggedInRegionList">
        <div
          v-for="region in regionData"
          :key="region.region_id"
          class="regionSelectorItems"
        >
          <div
            class="regionSelectorItemHeader"
            @click="toggleRegion(`loggedIn_region_${region.region_id}`)"
          >
            <label
              class="regionSelectorCheckbox"
              :for="`region_${region.region_id}`"
            >
              <span class="regionSelectorLabels">{{ region.region_name }}</span>
            </label>
            <button
              :class="[
                'regionSelectorToggle',
                {
                  regionSelectorToggleExpanded: expandedRegions.has(
                    `loggedIn_region_${region.region_id}`
                  )
                }
              ]"
              @click.stop="toggleRegion(`loggedIn_region_${region.region_id}`)"
            >
              <svg width="12" height="12" viewBox="0 0 12 12">
                <path
                  d="M4 2l4 4-4 4"
                  stroke="currentColor"
                  stroke-width="1.5"
                  fill="none"
                />
              </svg>
            </button>
          </div>

          <div
            v-if="expandedRegions.has(`loggedIn_region_${region.region_id}`)"
            class="regionSelectorChildren"
          >
            <div
              v-for="prefecture in region.prefectures"
              :key="prefecture.prefecture_id"
              class="regionSelectorChildItem"
              :class="{
                regionSelectorChildItemSelected:
                  isLoggedInPrefectureSelected(prefecture)
              }"
            >
              <div
                class="regionSelectorChildHeader"
                @click="
                  toggleRegion(
                    `loggedIn_prefecture_${prefecture.prefecture_id}`
                  )
                "
              >
                <label class="regionSelectorCheckbox">
                  <input
                    type="checkbox"
                    :id="`loggedIn_prefecture_${prefecture.prefecture_id}`"
                    :checked="isLoggedInPrefectureSelected(prefecture)"
                    :indeterminate="hasLoggedInPartialSelection(prefecture)"
                    @click.stop
                    @change="
                      handleLoggedInPrefectureSelection(prefecture, $event)
                    "
                  />
                  <span class="regionSelectorLabels">
                    {{ prefecture.prefecture_name }}
                  </span>
                </label>
                <button
                  v-if="prefecture.areas && prefecture.areas.length > 0"
                  :class="[
                    'regionSelectorToggle',
                    {
                      regionSelectorToggleExpanded: expandedRegions.has(
                        `loggedIn_prefecture_${prefecture.prefecture_id}`
                      )
                    }
                  ]"
                  @click.stop="
                    toggleRegion(
                      `loggedIn_prefecture_${prefecture.prefecture_id}`
                    )
                  "
                >
                  <svg width="12" height="12" viewBox="0 0 12 12">
                    <path
                      d="M4 2l4 4-4 4"
                      stroke="currentColor"
                      stroke-width="1.5"
                      fill="none"
                    />
                  </svg>
                </button>
                <div v-else style="padding: 16px 16px 16px 32px"></div>
              </div>

              <div
                v-if="
                  expandedRegions.has(
                    `loggedIn_prefecture_${prefecture.prefecture_id}`
                  )
                "
                class="regionSelectorSubChildren"
              >
                <div
                  v-for="area in prefecture.areas"
                  :key="area.area_id"
                  class="regionSelectorSubChildItem"
                  :class="{
                    regionSelectorSubChildItemSelected:
                      isLoggedInAreaSelected(area)
                  }"
                >
                  <label class="regionSelectorCheckbox">
                    <input
                      type="checkbox"
                      :checked="isLoggedInAreaSelected(area)"
                      @change="handleLoggedInAreaSelection(area, $event)"
                    />
                    <span class="regionSelectorLabels">
                      {{ area.area_name }}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="regionSelectorTags">
        <button
          v-for="tag in regionTags"
          :key="tag"
          :class="[
            'regionSelectorTag',
            { regionSelectorTagActive: activeTag === tag }
          ]"
          @click="setActiveTag(tag)"
        >
          {{ tag }}
        </button>
      </div>
    </div>

    <div class="regionSelectorContent">
      <div class="regionSelectorList" v-if="currentRegionData">
        <div
          v-for="prefecture in currentRegionData.prefectures"
          :key="prefecture.prefecture_id"
          class="regionSelectorItem"
          :class="{
            regionSelectorItemSelected: isPrefectureFullySelected(prefecture)
          }"
        >
          <div
            class="regionSelectorItemHeader"
            @click="toggleRegion(`prefecture_${prefecture.prefecture_id}`)"
          >
            <label class="regionSelectorCheckbox">
              <input
                type="checkbox"
                :checked="isPrefectureFullySelected(prefecture)"
                :indeterminate="hasPartialSelection(prefecture)"
                @click.stop
                @change="handlePrefectureSelection(prefecture, $event)"
              />
              <span class="regionSelectorCheckmark"></span>
              <span class="regionSelectorLabel">
                {{ prefecture.prefecture_name }}
              </span>
            </label>
            <button
              :class="[
                'regionSelectorToggle',
                {
                  regionSelectorToggleExpanded: expandedRegions.has(
                    `prefecture_${prefecture.prefecture_id}`
                  )
                }
              ]"
              @click.stop="
                toggleRegion(`prefecture_${prefecture.prefecture_id}`)
              "
            >
              <svg width="12" height="12" viewBox="0 0 12 12">
                <path
                  d="M4 2l4 4-4 4"
                  stroke="currentColor"
                  stroke-width="1.5"
                  fill="none"
                />
              </svg>
            </button>
          </div>

          <div
            v-if="expandedRegions.has(`prefecture_${prefecture.prefecture_id}`)"
            class="regionSelectorChildren"
          >
            <div
              v-for="area in prefecture.areas"
              :key="area.area_id"
              class="regionSelectorChildItem"
              :class="{
                regionSelectorChildItemSelected:
                  isAreaFullySelected(area) ||
                  selectedItems.has(`area_${area.area_id}`)
              }"
            >
              <div
                class="regionSelectorChildHeader"
                @click="toggleRegion(`area_${area.area_id}`)"
              >
                <label class="regionSelectorCheckbox">
                  <input
                    type="checkbox"
                    :checked="
                      isAreaFullySelected(area) ||
                      selectedItems.has(`area_${area.area_id}`)
                    "
                    :indeterminate="hasPartialSelectionArea(area)"
                    @click.stop
                    @change="handleAreaSelection(area, $event)"
                  />
                  <span class="regionSelectorCheckmark"></span>
                  <span class="regionSelectorLabel">{{ area.area_name }}</span>
                </label>
                <button
                  v-if="area.bus_stops && area.bus_stops.length > 0"
                  :class="[
                    'regionSelectorToggle',
                    {
                      regionSelectorToggleExpanded: expandedRegions.has(
                        `area_${area.area_id}`
                      )
                    }
                  ]"
                  @click.stop="toggleRegion(`area_${area.area_id}`)"
                >
                  <svg width="12" height="12" viewBox="0 0 12 12">
                    <path
                      d="M4 2l4 4-4 4"
                      stroke="currentColor"
                      stroke-width="1.5"
                      fill="none"
                    />
                  </svg>
                </button>
              </div>

              <div
                v-if="
                  expandedRegions.has(`area_${area.area_id}`) && area.bus_stops
                "
                class="regionSelectorSubChildren"
              >
                <div
                  v-for="busStop in area.bus_stops"
                  :key="busStop.bus_stop_id"
                  class="regionSelectorSubChildItem"
                  :class="{
                    regionSelectorSubChildItemSelected:
                      isNodeSelected(
                        `bus_${busStop.bus_stop_id}`,
                        'bus_stop',
                        busStop.bus_stop_id
                      ) || selectedItems.has(`bus_${busStop.bus_stop_id}`)
                  }"
                >
                  <label class="regionSelectorCheckbox">
                    <input
                      type="checkbox"
                      :checked="
                        isNodeSelected(
                          `bus_${busStop.bus_stop_id}`,
                          'bus_stop',
                          busStop.bus_stop_id
                        ) || selectedItems.has(`bus_${busStop.bus_stop_id}`)
                      "
                      @change="handleBusStopSelection(busStop, $event)"
                    />
                    <span class="regionSelectorCheckmark"></span>
                    <span class="regionSelectorLabel">
                      {{ busStop.bus_stop_name }}
                    </span>
                  </label>
                  <span
                    v-if="busStop.labels && busStop.labels.length > 0"
                    class="regionSelectorTagLabel"
                  >
                    {{ getTagText(busStop.labels) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="regionSelectorFooter">
      <button
        class="regionSelectorConfirmBtn"
        :disabled="!selectedNode"
        @click="handleConfirm"
      >
        {{ confirmButtonText }}
      </button>
    </div>
  </div>
</template>
<style lang="scss">
.regionSelectorLabels {
  color: #26499d;
}

.regionSelectorItems {
  border-top: 1px solid #dfdfdf;
  transition: background-color 0.2s ease;
}

.regionSelectorItems:last-child {
  margin-bottom: 20px;
}

.regionSelectorItems:last-of-type {
  border-bottom: 1px solid #dfdfdf;
}

.regionSelectorItem {
  border-top: 1px solid #dfdfdf;
  margin: 0 20px;
  transition: background-color 0.2s ease;
}

.regionSelectorItem:last-child {
  margin-bottom: 60px;
}

.regionSelectorList > .regionSelectorItem:last-of-type {
  border-bottom: 1px solid #dfdfdf;
}

.regionSelector {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;

  &Header {
    padding: 12px 16px;
  }

  &HeaderTop {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  &BackBtn {
    padding: 4px;
    border: none;
    background: transparent;
    color: #666;
    margin-right: 8px;
  }

  &Title {
    font-size: 16px;
    font-weight: 500;
    color: #26499d;
    margin: 0;
    flex: 1;
    text-align: center;
  }

  &SearchBox {
    margin-bottom: 12px;
  }

  &SearchInput {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #4096ff;
    }

    &::placeholder {
      color: #999;
    }
  }

  &Tags {
    text-align: left;
    overflow: hidden;
  }

  &Tag {
    height: 32px;
    border-radius: 6px;
    font-size: 13px;
    color: #000;
    border: 1px solid #9cbcd4;
    width: calc(25% - 10px);
    min-width: 50px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin: 5px;

    &Active {
      background: #9cbcd4;
      color: #000;
    }
  }

  &Content {
    height: 100%;
    overflow-y: auto;
    box-sizing: border-box;
    margin-bottom: 80px;
  }

  &List {
    padding: 0;
  }

  &ItemHeader,
  &ChildHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    transition: background-color 0.2s ease;
  }

  &Checkbox {
    display: flex;
    align-items: center;
    flex: 1;

    input[type='checkbox'] {
      display: none;
    }
  }

  &Checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #26499d;
    border-radius: 3px;
    margin-right: 8px;
    position: relative;
    transition: all 0.2s ease;

    input:checked + & {
      background: #26499d;
      border-color: #26499d;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -60%) rotate(45deg);
        width: 4px;
        height: 8px;
        border: 2px solid #ffffff;
        border-top: none;
        border-left: none;
      }
    }

    input:indeterminate + & {
      background: #26499d;
      border-color: #26499d;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 2px;
        background: #ffffff;
        border: none;
      }
    }
  }

  &Label {
    font-size: 14px;
    color: #333;
    user-select: none;
  }

  &Toggle {
    padding: 4px;
    border: none;
    background: transparent;
    cursor: pointer;
    color: #26499d;
    transition: all 0.2s ease;

    &Expanded {
      transform: rotate(90deg);
    }
  }

  &Children {
    border-top: 1px solid #f0f0f0;
  }

  &ChildItem {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &Selected {
      background-color: #e7f2fa;
    }
  }

  &ChildHeader {
    padding: 8px 16px 8px 32px;
  }

  &SubChildren {
    border-top: 1px solid #e0e0e0;
  }

  &SubChildItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px 12px 48px;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &Selected {
      background-color: #e7f2fa;
    }
  }

  &TagLabel {
    font-size: 12px;
    color: #000;
    border: 1px solid #9cbcd4;
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
  }

  &Footer {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fff;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }

  &ConfirmBtn {
    width: 100%;
    padding: 12px 24px;
    background: #26499d;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:disabled {
      background: #dfdfdf;
      color: #7d7d7d;
      cursor: not-allowed;
    }
  }
}
</style>
