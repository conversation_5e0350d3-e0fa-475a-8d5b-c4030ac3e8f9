export interface UserFormData {
  lastName: string
  firstName: string
  lastNameKana: string
  firstNameKana: string

  birthYear: string
  birthMonth: string
  birthDay: string

  gender: 'male' | 'female' | ''

  phoneNumber: string
  emailAddress: string
  confirmEmailAddress: string
  residence: string | null
  cityAddressDetail: string
  occupation: string | null
  postalCode: string
  prefecture: string
  cityName: string
  detailAddress: string
  buildingName: string

  password: string
  confirmPassword: string

  emailSubscription: boolean

  agreeToTerms: boolean
}

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: string, formData?: UserFormData) => boolean
  message: string
}

export interface FieldRules {
  [key: string]: ValidationRule[]
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

export interface FormFieldConfig {
  key: keyof UserFormData
  label: string
  type: 'text' | 'email' | 'tel' | 'password' | 'select' | 'radio' | 'checkbox'
  placeholder?: string
  required?: boolean
  options?: { value: string | boolean; label: string }[]
  gridCols?: number
}

export const GENDER_OPTIONS = [
  { value: 'male', label: '男性' },
  { value: 'female', label: '女性' },
  { value: 'answer', label: '回答しない' }
] as const

export const OCCUPATION_OPTIONS = [
  { value: 'student', label: '学生' },
  { value: 'company_employee', label: '会社員' },
  { value: 'civil_servant', label: '公務員' },
  { value: 'self_employed', label: '自営業' },
  { value: 'freelancer', label: 'フリーランス' },
  { value: 'part_time', label: 'パート・アルバイト' },
  { value: 'housewife', label: '主婦・主夫' },
  { value: 'retired', label: '退職' },
  { value: 'other', label: 'その他' }
] as const

export const RESIDENCE_OPTIONS = [
  { value: 'hokkaido', label: '北海道' },
  { value: 'aomori', label: '青森県' },
  { value: 'iwate', label: '岩手県' },
  { value: 'miyagi', label: '宮城県' },
  { value: 'akita', label: '秋田県' },
  { value: 'yamagata', label: '山形県' },
  { value: 'fukushima', label: '福島県' },
  { value: 'ibaraki', label: '茨城県' },
  { value: 'tochigi', label: '栃木県' },
  { value: 'gunma', label: '群馬県' },
  { value: 'saitama', label: '埼玉県' },
  { value: 'chiba', label: '千葉県' },
  { value: 'tokyo', label: '東京都' },
  { value: 'kanagawa', label: '神奈川県' },
  { value: 'niigata', label: '新潟県' },
  { value: 'toyama', label: '富山県' },
  { value: 'ishikawa', label: '石川県' },
  { value: 'fukui', label: '福井県' },
  { value: 'yamanashi', label: '山梨県' },
  { value: 'nagano', label: '長野県' },
  { value: 'gifu', label: '岐阜県' },
  { value: 'shizuoka', label: '静岡県' },
  { value: 'aichi', label: '愛知県' },
  { value: 'mie', label: '三重県' },
  { value: 'shiga', label: '滋賀県' },
  { value: 'kyoto', label: '京都府' },
  { value: 'osaka', label: '大阪府' },
  { value: 'hyogo', label: '兵庫県' },
  { value: 'nara', label: '奈良県' },
  { value: 'wakayama', label: '和歌山県' },
  { value: 'tottori', label: '鳥取県' },
  { value: 'shimane', label: '島根県' },
  { value: 'okayama', label: '岡山県' },
  { value: 'hiroshima', label: '広島県' },
  { value: 'yamaguchi', label: '山口県' },
  { value: 'tokushima', label: '徳島県' },
  { value: 'kagawa', label: '香川県' },
  { value: 'ehime', label: '愛媛県' },
  { value: 'kochi', label: '高知県' },
  { value: 'fukuoka', label: '福岡県' },
  { value: 'saga', label: '佐賀県' },
  { value: 'nagasaki', label: '長崎県' },
  { value: 'kumamoto', label: '熊本県' },
  { value: 'oita', label: '大分県' },
  { value: 'miyazaki', label: '宮崎県' },
  { value: 'kagoshima', label: '鹿児島県' },
  { value: 'okinawa', label: '沖縄県' }
] as const

export const EMAIL_SUBSCRIPTION_OPTIONS = [
  { value: true, label: '希望する' },
  { value: false, label: '希望しない' }
] as const

export interface ConfirmFormData {
  lastName: string
  firstName: string
  lastNameKana: string
  firstNameKana: string
  birthYear: string
  birthMonth: string
  birthDay: string
  gender?: string
  residence: { label: string; value: string }
  cityAddressDetail: string
  buildingName?: string
  occupation: { label: string; value: string }
  phoneNumber: string
  emailAddress: string
  password: string
  emailSubscription?: boolean | null
}

export interface VerificationFormData {
  verificationCode: string
}

export interface ValidationCheckRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  message: string
}

export interface ValidationCheckResult {
  isValid: boolean
  message: string
}

export interface CountdownState {
  isActive: boolean
  seconds: number
  canResend: boolean
}
