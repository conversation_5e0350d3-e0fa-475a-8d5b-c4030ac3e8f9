<script lang="ts" setup>
import { ref } from 'vue'
import Introduction from '~/pages/top/components/Introduction.vue'
import TravelSearchForm from '~/pages/top/components/TravelSearchForm.vue'
import headerImageUrl from '~/assets/image/rightIcon.png'
import mapSearchImageUrl from '~/assets/image/mapSearch.png'
import Product from '~/pages/top/components/Product.vue'
import EventSpot from '~/pages/top/components/EventSpot.vue'
import NoticeList from '~/pages/top/components/NoticeList.vue'
import FooterLinks from '~/pages/top/components/FooterLinks.vue'
import RideReservationCard from '~/pages/top/components/RideReservationCard.vue'
import { isLogin } from '~/utils/auth'
import { useRouter } from 'vue-router'
import MapSearch from '~/pages/top/components/MapSearch.vue'

/* ルーターとログイン状態を取得 */
const router = useRouter()
const isLoggedIn = computed(() => isLogin())
const headerImage = headerImageUrl
const mapSearchImage = mapSearchImageUrl
const currentTab = ref('片道')

/* 臨時運休・遅延情報データ */
const delayInfo = ref([
  {
    id: 1,
    title: '台風の影響による運休情報',
    date: '2024.12.18',
    type: '遅延・運休',
    description: '台風の影響により、一部路線で運休が発生しています。'
  },
  {
    id: 2,
    title: '年末年始の一部列車運休情報',
    date: '2024.12.28',
    type: '遅延・運休',
    description: '年末年始期間中、一部列車の運休が予定されています。'
  },
  {
    id: 3,
    title: '工事による遅延情報',
    date: '2024.12.20',
    type: '遅延・運休',
    description: '線路工事のため、一部区間で遅延が発生する可能性があります。'
  },
  {
    id: 4,
    title: '信号機故障による遅延',
    date: '2024.12.22',
    type: '遅延・運休',
    description: '信号機の故障により、一部列車に遅延が発生しています。'
  },
  {
    id: 5,
    title: '雪の影響による運休',
    date: '2024.12.25',
    type: '遅延・運休',
    description: '大雪の影響により、山間部の一部路線で運休が発生しています。'
  },
  {
    id: 6,
    title: '設備点検による遅延',
    date: '2024.12.30',
    type: '遅延・運休',
    description: '定期設備点検のため、一部列車に遅延が発生する可能性があります。'
  }
])

/* 運行情報テキストを動的に生成 */
const fullStatusText = computed(() => {
  const count = delayInfo.value.length
  return `運行情報：運休・遅延情報あり（${count}件）`
})

/* タブクリックハンドラー */
const handleTabClick = (tabValue: string) => {
  currentTab.value = tabValue
}

/* ギャラリーデータ */
const galleryData = [
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop'
  },
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1481277542470-605612bd2d61?w=800&h=400&fit=crop'
  },
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1519985176271-adb1088fa94c?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?w=800&h=400&fit=crop'
  },
  {
    leftTopImage:
      'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?w=400&h=300&fit=crop',
    leftBottomImage:
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    rightImage:
      'https://images.unsplash.com/photo-1481277542470-605612bd2d61?w=800&h=400&fit=crop'
  }
]

/* チケットデータ */
const tickets = ref([
  {
    id: 'T20230501001',
    arr: [
      {
        id: 101,
        start: '京都',
        end: '奈良',
        next: '大阪',
        final: '神戸',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '1便目',
        startDate: '5/1 (月)',
        endDate: '5/1 (月)',
        startTime: '08:30',
        endTime: '12:45',
        startLocation: '京都駅中央口',
        endLocation: '神戸三宮駅',
        showReservationData: true
      },
      {
        id: 102,
        start: '京都',
        end: '大阪',
        next: '姫路',
        final: '岡山',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '2便目',
        startDate: '5/2 (火)',
        endDate: '5/2 (火)',
        startTime: '14:15',
        endTime: '18:30',
        startLocation: '京都駅新幹線口',
        endLocation: '岡山駅東口',
        showReservationData: true
      },
      {
        id: 103,
        start: '京都',
        end: '大阪',
        next: '姫路',
        final: '岡山',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '3便目',
        startDate: '5/2 (日)',
        endDate: '5/2 (日)',
        startTime: '14:15',
        endTime: '18:30',
        startLocation: '京都駅新幹線口',
        endLocation: '岡山駅東口',
        showReservationData: true
      }
    ]
  },
  {
    id: 'T20230503002',
    arr: [
      {
        id: 201,
        start: '札幌',
        end: '旭川',
        next: '富良野',
        final: '帯広',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '1便目',
        startDate: '5/3 (水)',
        endDate: '5/4 (木)',
        startTime: '09:10',
        endTime: '16:40',
        startLocation: '札幌駅北口',
        endLocation: '帯広駅',
        showReservationData: true
      },
      {
        id: 202,
        start: '函館',
        end: '小樽',
        next: '札幌',
        final: '岩見沢',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '4便目',
        startDate: '5/5 (金)',
        endDate: '5/5 (金)',
        startTime: '11:20',
        endTime: '19:50',
        startLocation: '函館駅中央口',
        endLocation: '岩見沢駅',
        showReservationData: true
      }
    ]
  },
  {
    id: 'T20230507003',
    arr: [
      {
        id: 301,
        start: '東京',
        end: '横浜',
        next: '静岡',
        final: '名古屋',
        isRouteExpanded: false,
        isContentVisible: true,
        tripHeader: '1便目',
        startDate: '5/7 (日)',
        endDate: '5/7 (日)',
        startTime: '07:00',
        endTime: '12:30',
        startLocation: '東京駅八重洲口',
        endLocation: '名古屋駅東口',
        showReservationData: true
      }
    ]
  }
])

/* 検索処理 */
const handleSearch = () => {
  router.push(`/bus-service-selection`)
}

/* 予約ページへ遷移 */
const reservation = () => {
  router.push(`/appointment-confirm-change`)
}

/* 運行情報バナーをクリックして通知ページへ遷移 */
const handleDelayInfoClick = () => {
  router.push('/notifications')
}

/* イベントデータ */
const eventsDataTourist = ref([
  {
    eventId: 101,
    name: '春の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-03-20',
    endDate: '2025-04-10',
    image: 'https://picsum.photos/400/300?random=1',
    description: '春の桜祭り春の桜祭り'
  },
  {
    eventId: 102,
    name: '秋の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-09-20',
    endDate: '2025-10-10',
    image: 'https://picsum.photos/400/300?random=2',
    description: '秋の桜祭り秋の桜祭り'
  },
  {
    eventId: 103,
    name: '夏の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-06-20',
    endDate: '2025-07-10',
    image: 'https://picsum.photos/400/300?random=3',
    description: '夏の桜祭り夏の桜祭り'
  },
  {
    eventId: 104,
    name: '冬の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-12-20',
    endDate: '2026-01-10',
    image: 'https://picsum.photos/400/300?random=4',
    description: '冬の桜祭り冬の桜祭り'
  },
  {
    eventId: 105,
    name: '夜桜ライトアップ',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-03-25',
    endDate: '2025-04-05',
    image: 'https://picsum.photos/400/300?random=5',
    description: '夜桜ライトアップ夜桜ライトアップ'
  },
  {
    eventId: 106,
    name: '桜と音楽の夕べ',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-04-01',
    endDate: '2025-04-01',
    image: 'https://picsum.photos/400/300?random=6',
    description: '桜と音楽の夕べ桜と音楽の夕べ'
  }
])
/* イベントデータ */
const eventsDataSet = ref([
  {
    eventId: 101,
    name: '春の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-03-20',
    endDate: '2025-04-10',
    image: 'https://picsum.photos/400/300?random=1',
    description: '春の桜祭り春の桜祭り'
  },
  {
    eventId: 102,
    name: '秋の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-09-20',
    endDate: '2025-10-10',
    image: 'https://picsum.photos/400/300?random=2',
    description: '秋の桜祭り秋の桜祭り'
  },
  {
    eventId: 103,
    name: '夏の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-06-20',
    endDate: '2025-07-10',
    image: 'https://picsum.photos/400/300?random=3',
    description: '夏の桜祭り夏の桜祭り'
  },
  {
    eventId: 104,
    name: '冬の桜祭り',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-12-20',
    endDate: '2026-01-10',
    image: 'https://picsum.photos/400/300?random=4',
    description: '冬の桜祭り冬の桜祭り'
  },
  {
    eventId: 105,
    name: '夜桜ライトアップ',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-03-25',
    endDate: '2025-04-05',
    image: 'https://picsum.photos/400/300?random=5',
    description: '夜桜ライトアップ夜桜ライトアップ'
  },
  {
    eventId: 106,
    name: '桜と音楽の夕べ',
    prefectureId: 13,
    address: '東京都千代田区皇居外苑',
    startDate: '2025-04-01',
    endDate: '2025-04-01',
    image: 'https://picsum.photos/400/300?random=6',
    description: '桜と音楽の夕べ桜と音楽の夕べ'
  }
])
</script>

<template>
  <div>
    <div class="top_Background">
      <div class="errorBanner flex" @click="handleDelayInfoClick">
        <div class="errorBannerIcon">
          <svg
            width="24"
            height="24"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M16 3L29.856 26H2.144L16 3Z" fill="#D00000" />
            <path
              d="M16 12V18"
              stroke="#FFFFFF"
              stroke-width="2"
              stroke-linecap="round"
            />
            <circle cx="16" cy="22" r="2" fill="#FFFFFF" />
          </svg>
        </div>
        <div class="errorBannerText">
          {{ fullStatusText }}
        </div>
      </div>
      <div class="introd">
        <Introduction :gallery="galleryData" />
      </div>
      <div>
        <TravelSearchForm
            :default-tab="'片道'"
            @search="handleSearch"
            @tab-click="handleTabClick"
        />
      </div>
      <div>
        <div class="center">
          <MapSearch />
        </div>
        <div class="center" v-if="!isLoggedIn">
          <div class="header-img flex">
            <div class="confirm flex" @click="reservation">
              <span>予約の確認・変更・キャンセル</span>
              <img
                :src="headerImage"
                alt="画像の読み込みに失敗しました"
                class="head-img"
              />
            </div>
          </div>
        </div>
        <div class="centerRide" v-else>
          <RideReservationCard :tickets="tickets" />
        </div>
      </div>
    </div>

    <div class="product">
      <Product />
    </div>
    <div class="spots">
      <EventSpot
        section-title="観光地・施設"
        :api-endpoint="'spots'"
        :max-items="6"
        category="tourist"
        :events-data="eventsDataTourist"
      />
    </div>
    <div class="event">
      <EventSpot
        section-title="期間限定・イベント"
        :api-endpoint="'event'"
        :max-items="6"
        category="event"
        :events-data="eventsDataSet"
      />
    </div>
    <div>
      <NoticeList />
      <FooterLinks />
    </div>
  </div>
</template>

<style scoped lang="scss">
.top_Background {
  background-color: #fff7ec;
  padding-top: 10px;

  .introd {
    padding: 8px 0 18px 0;
  }

  .map-img {
    max-width: 100%;
    margin: 20px 0;
  }

  .header-img {
    padding: 20px 17px 20px 17px;

    .confirm {
      background-color: #26499d;
      color: #fff;
      font-size: 14px;
      padding: 15px 0;
      border-radius: 40px;
      width: 341px;

      .head-img {
        width: 22px;
        height: 22px;
      }
    }
  }
}

.product {
  background-color: #e7f2fa;
}

.spots {
  background-color: #fffdea;
}

.event {
  background-color: #e4f5eb;
}

.round {
  padding-bottom: 20px;
}

.center {
  display: flex;
  justify-content: center;
}

.centerRide {
  padding: 20px 0;
}

.errorBanner {
  margin: 0 10px 0 10px;
  background-color: #ffeeee;
  border: 1px solid #d00000;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #ffe6e6;
  }

  .errorBannerIcon {
    display: flex;
    align-items: center;
  }

  .errorBannerText {
    color: #d00000;
    font-weight: bold;
    margin-left: 10px;
    display: flex;
    align-items: center;
  }
}
</style>
