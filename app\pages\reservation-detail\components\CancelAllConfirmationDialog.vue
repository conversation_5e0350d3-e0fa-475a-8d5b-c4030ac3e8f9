<script lang="ts" setup>
// プロパティの定義
interface Props {
  modelValue: boolean
}

// イベントの定義
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ダイアログの表示状態を管理
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// キャンセル完了ダイアログの表示状態
const cancelCompletionVisible = ref(false)

// 確認ボタンのクリック処理
const handleConfirm = () => {
  // 確認処理を実行してからキャンセル完了ダイアログを表示
  dialogVisible.value = false
  cancelCompletionVisible.value = true
}

// キャンセルボタンのクリック処理
const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// キャンセル完了ダイアログを閉じる処理
const handleCompletionClose = () => {
  cancelCompletionVisible.value = false
}

// 予約確認・変更・キャンセルへのナビゲーション
const handleNavigateToReservation = () => {
  cancelCompletionVisible.value = false
  // ここでナビゲーション処理を実装
  navigateTo('/appointment-confirm-change')
}
</script>

<template>
  <!-- キャンセル確認ダイアログ -->
  <v-dialog v-model="dialogVisible" max-width="343" persistent>
    <v-card class="cancel-all-dialog">
      <div class="dialog-content">
        <!-- タイトル -->
        <h3 class="dialog-title">予約全体をキャンセルしますか？</h3>

        <!-- 説明文 -->
        <div class="description-section">
          <p class="description-text">
            この操作を行うと、同じ予約番号に含まれるすべての便の予約がキャンセルされます。
            <br />
            一部だけをキャンセルすることはできません。よろしいですか？
          </p>
        </div>

        <!-- ボタンエリア -->
        <div class="button-section">
          <div class="buttons">
            <!-- 確認ボタン -->
            <v-btn class="confirm-btn" @click="handleConfirm" block>はい</v-btn>

            <!-- キャンセルボタン -->
            <v-btn class="cancel-btn" @click="handleCancel" block>いいえ</v-btn>
          </div>
        </div>
      </div>
    </v-card>
  </v-dialog>

  <!-- キャンセル完了ダイアログ -->
  <v-dialog v-model="cancelCompletionVisible" max-width="343" persistent>
    <v-card class="cancel-completion-dialog">
      <div class="completion-dialog-content">
        <!-- タイトル -->
        <h3 class="completion-dialog-title">予約をキャンセル完了</h3>

        <!-- 説明文 -->
        <div class="completion-description-section">
          <p class="completion-description-text">
            予約をキャンセル完了しました。
            <br />
            またのご利用をお待ちしております。
          </p>
        </div>

        <!-- ボタンエリア -->
        <div class="completion-button-section">
          <div class="completion-buttons">
            <v-btn
              class="navigate-btn"
              @click="handleNavigateToReservation"
              block
            >
              予約確認・変更・キャンセルへ
            </v-btn>
          </div>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.cancel-all-dialog {
  border-radius: 10px !important;
  background: #ffffff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 320px;
}

.dialog-title {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.2;
  color: #000000;
  text-align: left;
  width: 311px;
  margin: 0;
}

.description-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 311px;
  height: 88px;
}

.description-text {
  line-height: 1.5714285714285714;
  color: #000000;
  text-align: left;
  margin: 0;
}

.button-section {
  position: absolute;
  left: 16px;
  top: 160px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 311px;
}

.buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px 0 0;
  width: 311px;
}

.confirm-btn {
  width: 311px !important;
  height: 48px !important;
  background: #ed785f !important;
  color: #ffffff !important;
  border-radius: 4px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;

  padding: 14px 13.38px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 10px !important;
}

.confirm-btn:hover {
  background: #d66b52 !important;
}

.cancel-btn {
  width: 311px !important;
  height: 48px !important;
  background: #e7f2fa !important;
  color: #26499d !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;

  line-height: 1.2 !important;

  padding: 14px 13.38px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 10px !important;
}

.cancel-btn:hover {
  background: #d6e9f5 !important;
}

.cancel-completion-dialog {
  border-radius: 10px !important;
  background: #ffffff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.completion-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 320px;
}

.completion-dialog-title {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.2;
  color: #000000;
  text-align: left;
  width: 311px;
  margin: 0;
}

.completion-description-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 311px;
  height: auto;
}

.completion-description-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5714285714285714;
  color: #000000;
  text-align: left;
  margin: 0;
}

.completion-button-section {
  position: absolute;
  left: 16px;
  top: 224px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 311px;
}

.completion-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px 0 0;
  width: 311px;
}

.navigate-btn {
  width: 311px !important;
  height: 48px !important;
  background: #26499d !important;
  color: #ffffff !important;
  border-radius: 4px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 10px !important;
}

.navigate-btn:hover {
  background: #1e3a7a !important;
}
</style>
