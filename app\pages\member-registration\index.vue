<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import type {
  UserFormData,
  ValidationResult
} from '~/types/member-Registration'
import {
  GENDER_OPTIONS,
  OCCUPATION_OPTIONS,
  EMAIL_SUBSCRIPTION_OPTIONS,
  RESIDENCE_OPTIONS
} from '~/types/member-Registration'
import {
  VALIDATION_RULES,
  validateField,
  validateForm,
  isFormValid
} from '~/pages/member-registration/member-registration'
import { useRouter } from 'vue-router'
import { useRegistrationFormStore } from '@/stores/member-registration'

const router = useRouter()

/* フォームデータの初期化 */
const formData = reactive<UserFormData>({
  lastName: '',
  firstName: '',
  lastNameKana: '',
  firstNameKana: '',
  birthYear: '',
  birthMonth: '',
  birthDay: '',
  gender: '',
  phoneNumber: '',
  emailAddress: '',
  confirmEmailAddress: '',
  residence: null,
  cityAddressDetail: '',
  occupation: null,
  password: '',
  confirmPassword: '',
  emailSubscription: true,
  agreeToTerms: false,
  postalCode: '',
  prefecture: '',
  cityName: '',
  detailAddress: '',
  buildingName: ''
})

/* UI状態の管理 */
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isSubmitting = ref(false)
const validationResults = reactive<{ [key: string]: ValidationResult }>({})

/* 誕生日のエラーメッセージを統合 */
const birthErrorMessage = computed(() => {
  const yearErrors = getFieldErrors('birthYear')
  const monthErrors = getFieldErrors('birthMonth')
  const dayErrors = getFieldErrors('birthDay')

  if (yearErrors.length > 0) return yearErrors[0]
  if (monthErrors.length > 0) return monthErrors[0]
  if (dayErrors.length > 0) return dayErrors[0]

  return ''
})

/* エラーバナー表示の判定 */
const showErrorBanner = computed(() => {
  return (
    Object.keys(validationResults).length > 0 && !isFormValid(validationResults)
  )
})

/* フォーム送信可能状態の判定 */
const canSubmit = computed(() => {
  const hasValidated = Object.keys(validationResults).length > 0
  return hasValidated && isFormValid(validationResults)
})

/* フィールドのエラーメッセージを取得 */
const getFieldErrors = (fieldName: string): string[] => {
  return validationResults[fieldName]?.errors || []
}

/* 単一フィールドのバリデーション実行 */
const validateSingleField = (fieldName: keyof UserFormData) => {
  const value = formData[fieldName]
  const rules = VALIDATION_RULES[fieldName]

  if (rules) {
    validationResults[fieldName] = validateField(value, rules, formData)
  }
}

/* フォーム送信の処理 */
const handleSubmit = async () => {
  const results = validateForm(formData)
  Object.assign(validationResults, results)

  if (!isFormValid(validationResults)) {
    return
  }

  isSubmitting.value = true

  try {
    const formStore = useRegistrationFormStore()
    formStore.saveSubmittedData(formData)

    await new Promise((resolve) => setTimeout(resolve, 2000))
    router.push('/member-registration/registration-confirmation')
  } catch (error) {
    console.error('Submission failed:', error)
  } finally {
    isSubmitting.value = false
  }
}


/* フォームデータ変更時の自動バリデーション */
watch(
  formData,
  () => {
    Object.keys(validationResults).forEach((fieldName) => {
      validateSingleField(fieldName as keyof UserFormData)
    })
  },
  { deep: true }
)
</script>

<template>
  <div class="registrationContainer">
    <div class="regionpadding">
      <BaseHeader
        title="新規会員登録"
        :showBack="true"
        :showRightIcon="false"
      />
    </div>


    <div v-if="showErrorBanner" class="errorBanner">
      <svg
        width="32"
        height="32"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M16 3L29.856 26H2.144L16 3Z" fill="#D00000" />
        <path
          d="M16 12V18"
          stroke="#FFFFFF"
          stroke-width="2"
          stroke-linecap="round"
        />
        <circle cx="16" cy="22" r="2" fill="#FFFFFF" />
      </svg>
      <span class="text">
        入力内容に誤りがあります。
        <br />
        再確認してください。
      </span>
    </div>

    <form @submit.prevent="handleSubmit" class="registrationForm">
      <section class="formSection">
        <div class="formGroup">
          <span class="fieldLabel required">お名前</span>
          <div class="nameRow">
            <v-text-field
              v-model="formData.lastName"
              placeholder="姓"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('lastName')"
              @blur="validateSingleField('lastName')"
            />
            <v-text-field
              v-model="formData.firstName"
              placeholder="名"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('firstName')"
              @blur="validateSingleField('firstName')"
            />
          </div>
        </div>

        <div class="formGroup">
          <span class="fieldLabel required">フリガナ</span>
          <div class="nameRow">
            <v-text-field
              v-model="formData.lastNameKana"
              placeholder="セイ"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('lastNameKana')"
              @blur="validateSingleField('lastNameKana')"
            />
            <v-text-field
              v-model="formData.firstNameKana"
              placeholder="メイ"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('firstNameKana')"
              @blur="validateSingleField('firstNameKana')"
            />
          </div>
        </div>
      </section>

      <section class="formSection">
        <span class="fieldLabel required">生年月日</span>
        <div class="birthContainer">
          <div class="birthRow">
            <v-text-field
              v-model="formData.birthYear"
              placeholder="YYYY"
              variant="outlined"
              :error="getFieldErrors('birthYear').length > 0"
              density="comfortable"
              class="birthField"
              @blur="validateSingleField('birthYear')"
            />
            <span class="birthUnit">年</span>
            <v-text-field
              v-model="formData.birthMonth"
              placeholder="MM"
              variant="outlined"
              :error="getFieldErrors('birthMonth').length > 0"
              density="comfortable"
              class="birthField"
              @blur="validateSingleField('birthMonth')"
            />
            <span class="birthUnit">月</span>
            <v-text-field
              v-model="formData.birthDay"
              placeholder="DD"
              variant="outlined"
              :error="getFieldErrors('birthDay').length > 0"
              density="comfortable"
              class="birthField"
              @blur="validateSingleField('birthDay')"
            />
            <span class="birthUnit">日</span>
          </div>
          <p v-if="birthErrorMessage" class="errorMessage">
            {{ birthErrorMessage }}
          </p>
        </div>
      </section>

      <section class="formSection">
        <span class="fieldLabel required">性別</span>
        <v-radio-group
          v-model="formData.gender"
          inline
          class="genderGroup"
          :error-messages="getFieldErrors('gender')"
          @update:model-value="validateSingleField('gender')"
        >
          <v-radio
            v-for="option in GENDER_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            style="color: #26499d"
          />
        </v-radio-group>
      </section>

      <section class="formSection">
        <div class="formGroup">
          <span class="fieldLabel required">居住地</span>
          <v-select
            v-model="formData.residence"
            :items="RESIDENCE_OPTIONS"
            item-title="label"
            item-value="value"
            placeholder="居住地を選択してください"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('residence')"
            return-object
            @update:model-value="validateSingleField('residence')"
          />
        </div>

        <div class="formGroup">
          <span class="fieldLabel">市区町村・番地以降</span>
          <v-text-field
            v-model="formData.cityAddressDetail"
            placeholder="市区町村・番地以降を入力"
            variant="outlined"
            density="comfortable"
            @blur="validateSingleField('cityAddressDetail')"
          />

          <div
            v-if="getFieldErrors('cityAddressDetail').length"
            class="errorMessage"
          >
            {{ getFieldErrors('cityAddressDetail')[0] }}
          </div>
        </div>
      </section>

      <section class="formSection">
        <div class="formGroup">
          <span class="fieldLabel required">職業</span>
          <v-select
            v-model="formData.occupation"
            :items="OCCUPATION_OPTIONS"
            item-title="label"
            return-object
            item-value="value"
            placeholder="職業を選択してください"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('occupation')"
            @update:model-value="validateSingleField('occupation')"
          />
        </div>
      </section>

      <section class="formSection">
        <div class="formGroup">
          <span class="fieldLabel required">電話番号</span>
          <v-text-field
            v-model="formData.phoneNumber"
            placeholder="電話番号を入力"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('phoneNumber')"
            @blur="validateSingleField('phoneNumber')"
          />
        </div>

        <div class="formGroup">
          <span class="fieldLabel required">メールアドレス</span>
          <v-text-field
            v-model="formData.emailAddress"
            placeholder="メールアドレスを入力"
            variant="outlined"
            density="comfortable"
            type="email"
            :error-messages="getFieldErrors('emailAddress')"
            @blur="validateSingleField('emailAddress')"
          />
        </div>

        <div class="formGroup">
          <span class="fieldLabel required">メールアドレス（確認用）</span>
          <v-text-field
            v-model="formData.confirmEmailAddress"
            placeholder="メールアドレスを再入力"
            variant="outlined"
            density="comfortable"
            type="email"
            :error-messages="getFieldErrors('confirmEmailAddress')"
            @blur="validateSingleField('confirmEmailAddress')"
          />
        </div>
      </section>

      <section class="formSection">
        <div class="formGroup">
          <span class="fieldLabel required">パスワード</span>
          <v-text-field
            v-model="formData.password"
            :type="showPassword ? 'text' : 'password'"
            placeholder="パスワードを入力"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('password')"
            @blur="validateSingleField('password')"
          >
            <template #append-inner>
              <v-icon
                @click="showPassword = !showPassword"
                :icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                class="passwordToggle"
              />
            </template>
          </v-text-field>
        </div>

        <div class="formGroup">
          <span class="fieldLabel required">パスワード（確認用）</span>
          <v-text-field
            v-model="formData.confirmPassword"
            :type="showConfirmPassword ? 'text' : 'password'"
            placeholder="パスワードを再入力"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('confirmPassword')"
            @blur="validateSingleField('confirmPassword')"
          >
            <template #append-inner>
              <v-icon
                @click="showConfirmPassword = !showConfirmPassword"
                :icon="showConfirmPassword ? 'mdi-eye-off' : 'mdi-eye'"
                class="passwordToggle"
              />
            </template>
          </v-text-field>
        </div>
      </section>

      <section class="formSection">
        <span class="fieldLabel">メールマガジン配信</span>
        <v-radio-group
          v-model="formData.emailSubscription"
          class="subscriptionGroup"
        >
          <v-radio
            v-for="option in EMAIL_SUBSCRIPTION_OPTIONS"
            :key="String(option.value)"
            :label="option.label"
            :value="option.value"
            style="color: #26499d"
          />
        </v-radio-group>
        <p class="fieldHint">
          利用規約をお読みいただき、「利用規約に同意する」にチェックをしてください
        </p>
      </section>

      <section class="formSection">
        <p class="termsHint">利用規約</p>
        <div class="termsContainer">
          <v-checkbox
            v-model="formData.agreeToTerms"
            class="termsCheckbox"
            :error-messages="getFieldErrors('agreeToTerms')"
            @update:model-value="validateSingleField('agreeToTerms')"
          >
            <template #label>
              <span class="termsLabel">利用規約等に同意する</span>
            </template>
          </v-checkbox>
        </div>
      </section>

      <div class="submitContainer">
        <v-btn
          type="submit"
          class="submitBtn"
          color="primary"
          size="large"
          block
          :loading="isSubmitting"
          :disabled="!canSubmit"
        >
          登録内容を確認する
        </v-btn>
      </div>
    </form>
  </div>
</template>

<style lang="scss" scoped>
.submitContainer :deep(.v-btn.submitBtn) {
  background-color: #26499d !important;
  color: #ffffff !important;
}

.submitContainer :deep(.v-btn.submitBtn:disabled) {
  background-color: #dfdfdf !important;
  color: #7d7d7d !important;
}

$primaryColor: #1976d2;
$errorColor: #e74c3c;
$warningColor: #f39c12;
$textColor: #26499d;
$borderColor: #e0e0e0;
$backgroundColor: #f5f5f5;
$white: #fff;

@mixin flexCenter {
  display: flex;
  align-items: center;
}

@mixin flexBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin buttonReset {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

@mixin requiredAsterisk {
  &::after {
    content: ' *';
    color: $errorColor;
  }
}

.registrationContainer {
  margin: 0 auto;
  min-height: 100vh;
  background-color: #fff;
}

.errorBanner {
  display: flex;
  align-items: center;
  padding: 0 20px;

  .text {
    margin-left: 10px;
    color: #d00000;
  }
}

.registrationForm {
  background-color: $white;
  border-radius: 8px;
  padding: 20px;
}

.formSection {
  margin-bottom: 24px;

  &:last-of-type {
    margin-bottom: 16px;
  }
}

.formGroup {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.fieldLabel {
  display: block;
  font-size: 14px;
  color: $textColor;
  margin-bottom: 8px;

  &.required {
    @include requiredAsterisk;
  }
}

.fieldHint {
  font-size: 12px;
  color: #000;
  margin-top: 4px;
  margin-bottom: 0;
  line-height: 1.4;
}

.nameRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .nameField {
    min-width: 0;
  }
}

.birthContainer {
  .birthRow {
    @include flexCenter;
    gap: 8px;
    flex-wrap: wrap;

    .birthField {
      flex: 1;
      max-width: 80px;
    }

    .birthUnit {
      font-size: 14px;
      white-space: nowrap;
    }
  }
}

.errorMessage {
  line-height: 12px;
  word-break: break-word;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  transition-duration: 150ms;
  color: #b00020;
  font-size: 12px;
  padding-left: 16px;
}

.genderGroup {
  :deep(.v-selection-control-group) {
    flex-direction: row;
    gap: 24px;
  }
}

.subscriptionGroup {
  :deep(.v-selection-control-group) {
    flex-direction: row;
    gap: 24px;
  }
}

.passwordToggle {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  color: #26499d;
}

.termsContainer {
  .termsCheckbox {
    :deep(.v-label) {
      font-size: 14px;
      color: #000;
    }
  }
}

.termsHint {
  color: #26499d;
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

.submitContainer {
  margin-top: 32px;

  .submitBtn {
    height: 48px;
  }
}

:deep(.v-text-field) {
  .v-field {
    border-radius: 6px;
  }

  .v-field__input {
    font-size: 14px;
    padding: 12px 16px;
  }
}

:deep(.v-select) {
  .v-field {
    border-radius: 6px;
  }
}

:deep(.v-radio-group) {
  .v-selection-control {
    min-height: auto;
  }

  .v-label {
    font-size: 14px;
    color: $textColor;
  }
}

:deep(.v-checkbox) {
  .v-selection-control {
    min-height: auto;
  }
}

:deep(.v-radio-group .v-label) {
  font-size: 14px;
  color: #000;
}
</style>
