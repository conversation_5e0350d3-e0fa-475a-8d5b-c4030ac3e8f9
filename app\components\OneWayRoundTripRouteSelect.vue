<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import DatePicker from './DatePicker.vue'
import TimePicker from './TimePicker.vue'
import { useWaypoints } from '~/composables/business/useWaypoints'
import { useRouteStore } from '~/stores/bus-service-selection'

/* 型定義 */
interface TripInfo {
  date: string
  time: string
  direction: 'departure' | 'arrival'
}

type LocationType = '' | 'prefecture' | 'area' | 'busStop'

interface SearchFormData {
  tripType: 'oneway' | 'roundtrip'
  departure: string
  departureId: string
  departureType: LocationType
  destination: string
  destinationId: string
  destinationType: LocationType
  waypoints: Array<{
    id: string
    location: string
    locationId: string
    locationType: LocationType
  }>
  date: string
  time: string
  direction: 'departure' | 'arrival'
  outbound: TripInfo
  return: TripInfo
}

/* Props定義 */
interface Props {
  enableTripType?: boolean
  enableWaypoints?: boolean
  enableDateTime?: boolean
  departureConfig?: {
    value?: string
    placeholder?: string
    readonly?: boolean
    showMapButton?: boolean
  }
  destinationConfig?: {
    value?: string
    placeholder?: string
    readonly?: boolean
    showMapButton?: boolean
  }
  waypointConfig?: {
    readonly?: boolean
    showMapButton?: boolean
  }
  searchButtonText?: string
  searchButtonColor?: string
  initialFormData?: Partial<SearchFormData>
  initialTripType?: 'oneway' | 'roundtrip'
}

const props = withDefaults(defineProps<Props>(), {
  enableTripType: true,
  enableWaypoints: true,
  enableDateTime: true,
  departureConfig: () => ({ readonly: true }),
  destinationConfig: () => ({ readonly: true }),
  waypointConfig: () => ({ readonly: true }),
  searchButtonText: '検索',
  searchButtonColor: '#ed785f'
})

/* イベント定義 */
const emit = defineEmits<{
  'departure-input': [value: string]
  'destination-input': [value: string]
  'waypoint-input': [index: number, value: string]
  'departure-map-click': []
  'destination-map-click': []
  'waypoint-map-click': [index: number]
  'search': [formData: SearchFormData]
  'form-change': [formData: SearchFormData]
  'departure-click': []
  'waypoint-click': [index: number]
  'destination-click': []
  'trip-type-change': [tripType: 'oneway' | 'roundtrip']
}>()

/* Composables */
const {
  waypoints,
  showWaypoints,
  addWaypoint,
  removeWaypoint,
  toggleWaypoint,
  canAddMoreWaypoints,
  waypointButtonText
} = useWaypoints()

const routeStore = useRouteStore()

/* リアクティブデータ */
const formRef = ref()

const createEmptyFormData = (): SearchFormData => ({
  tripType: 'oneway',
  departure: '',
  departureId: '',
  departureType: '',
  destination: '',
  destinationId: '',
  destinationType: '',
  waypoints: [],
  date: '',
  time: '',
  direction: 'departure',
  outbound: {
    date: '',
    time: '',
    direction: 'departure'
  },
  return: {
    date: '',
    time: '',
    direction: 'departure'
  }
})
const localFormData = ref<SearchFormData>(createEmptyFormData())

/* 日付時間選択器の状態 */
const datePickerOpen = ref(false)
const timePickerOpen = ref(false)
const currentDateType = ref('')
const currentTimeType = ref('')

/* 再帰更新防止フラグ */
const isInternalUpdate = ref(false)

/* バリデーションルール */
const validationRules = {
  departure: [(v: string) => !!v || '出発地を選択してください'],
  destination: [(v: string) => !!v || '到着地を選択してください'],
  date: [(v: string) => !!v || '日付を選択してください'],
  time: [(v: string) => !!v || '時間を選択してください'],
  outboundDate: [(v: string) => !!v || '行きの日付を選択してください'],
  outboundTime: [(v: string) => !!v || '行きの時間を選択してください'],
  returnDate: [(v: string) => !!v || '帰りの日付を選択してください'],
  returnTime: [(v: string) => !!v || '帰りの時間を選択してください']
}

/* 算出プロパティ */
const isSearchEnabled = computed(() => {
  if (!props.enableDateTime) {
    return !!(localFormData.value.departure && localFormData.value.destination)
  }

  if (localFormData.value.tripType === 'oneway') {
    return !!(
      localFormData.value.departure &&
      localFormData.value.destination &&
      localFormData.value.date &&
      localFormData.value.time
    )
  } else {
    return !!(
      localFormData.value.departure &&
      localFormData.value.destination &&
      localFormData.value.outbound?.date &&
      localFormData.value.outbound?.time &&
      localFormData.value.return?.date &&
      localFormData.value.return?.time
    )
  }
})

const currentSelectedDate = computed(() => {
  if (currentDateType.value === 'oneway') {
    return localFormData.value.date
  } else if (currentDateType.value === 'outbound') {
    return localFormData.value.outbound?.date || ''
  } else if (currentDateType.value === 'return') {
    return localFormData.value.return?.date || ''
  }
  return ''
})

const currentSelectedTime = computed(() => {
  if (currentTimeType.value === 'oneway') {
    return localFormData.value.time
  } else if (currentTimeType.value === 'outbound') {
    return localFormData.value.outbound?.time || ''
  } else if (currentTimeType.value === 'return') {
    return localFormData.value.return?.time || ''
  }
  return ''
})

/* ユーティリティ関数 */
const formatDate = (date: string | undefined): string => {
  if (!date) return ''
  try {
    const d = new Date(date)
    return `${d.getFullYear()}/${String(d.getMonth() + 1).padStart(
      2,
      '0'
    )}/${String(d.getDate()).padStart(2, '0')} (${
      ['日', '月', '火', '水', '木', '金', '土'][d.getDay()]
    })`
  } catch {
    return date
  }
}

const formatTime = (time: string | undefined): string => {
  if (!time) return ''
  return time
}

/* イベントハンドラー */
const handleTripTypeChange = (newType: 'oneway' | 'roundtrip') => {
  isInternalUpdate.value = true

  const emptyFormData = createEmptyFormData()
  emptyFormData.tripType = newType
  localFormData.value = emptyFormData

  const currentWaypoints = [...waypoints.value]
  currentWaypoints.forEach((w) => removeWaypoint(Number(w.id)))
  if (showWaypoints.value) toggleWaypoint()

  emit('trip-type-change', newType)

  nextTick(() => {
    isInternalUpdate.value = false
    emitFormChange()
    if (formRef.value) {
      formRef.value.resetValidation()
    }
  })
}

const openDatePicker = (type: string) => {
  currentDateType.value = type
  datePickerOpen.value = true
}

const openTimePicker = (type: string) => {
  currentTimeType.value = type
  timePickerOpen.value = true
}

const handleDateSelected = (date: string) => {
  if (currentDateType.value === 'oneway') {
    localFormData.value.date = date
  } else if (currentDateType.value === 'outbound') {
    if (!localFormData.value.outbound) {
      localFormData.value.outbound = {
        date: '',
        time: '',
        direction: 'departure'
      }
    }
    localFormData.value.outbound.date = date
  } else if (currentDateType.value === 'return') {
    if (!localFormData.value.return) {
      localFormData.value.return = {
        date: '',
        time: '',
        direction: 'departure'
      }
    }
    localFormData.value.return.date = date
  }
  closeDatePicker()
}

const handleTimeSelected = (time: string) => {
  if (currentTimeType.value === 'oneway') {
    localFormData.value.time = time
  } else if (currentTimeType.value === 'outbound') {
    if (!localFormData.value.outbound) {
      localFormData.value.outbound = {
        date: '',
        time: '',
        direction: 'departure'
      }
    }
    localFormData.value.outbound.time = time
  } else if (currentTimeType.value === 'return') {
    if (!localFormData.value.return) {
      localFormData.value.return = {
        date: '',
        time: '',
        direction: 'departure'
      }
    }
    localFormData.value.return.time = time
  }
  closeTimePicker()
}

const closeDatePicker = () => {
  datePickerOpen.value = false
  currentDateType.value = ''
}

const closeTimePicker = () => {
  timePickerOpen.value = false
  currentTimeType.value = ''
}

const handleWaypointChange = (index: number, value: string) => {
  emit('waypoint-input', index, value)
  emitFormChange()
}

const handleRemoveWaypoint = (waypointId: string) => {
  const index = waypoints.value.findIndex(
    (w) => String(w.id) === String(waypointId)
  )

  removeWaypoint(Number(waypointId))

  if (index !== -1 && localFormData.value.waypoints[index]) {
    localFormData.value.waypoints.splice(index, 1)
    emitFormChange()
  }
}

const handleSearch = async () => {
  let valid = true
  if (formRef.value && typeof formRef.value.validate === 'function') {
    const result = await formRef.value.validate()
    valid = result.valid
  }
  if (valid && isSearchEnabled.value) {
    const waypointsData = waypoints.value.map((waypoint, index) => ({
      id: String(waypoint.id),
      location: waypoint.location,
      locationId: String(waypoint.id),
      locationType:
        localFormData.value.waypoints[index]?.locationType ||
        ('' as '' | 'prefecture' | 'area' | 'busStop')
    }))

    const searchData: SearchFormData = {
      ...localFormData.value,
      waypoints: waypointsData
    }

    // 将数据同步到 route store
    routeStore.setFromSearchForm(searchData)

    emit('search', searchData)
  }
}

const emitFormChange = () => {
  if (!isInternalUpdate.value) {
    emit('form-change', localFormData.value)
  }
}

/* ウォッチャー */
watch(
  () => props.initialFormData,
  (newData) => {
    if (newData && !isInternalUpdate.value) {
      isInternalUpdate.value = true

      localFormData.value = { ...localFormData.value, ...newData }

      if (newData.waypoints && newData.waypoints.length > 0) {
        const wasShowingWaypoints = showWaypoints.value
        const newCount = newData.waypoints.length

        if (!wasShowingWaypoints) {
          toggleWaypoint()
        }

        nextTick(() => {
          const updatedCurrentCount = waypoints.value.length

          if (newCount > updatedCurrentCount) {
            for (let i = updatedCurrentCount; i < newCount; i++) {
              addWaypoint()
            }
          }

          nextTick(() => {
            newData.waypoints!.forEach((waypointData, index) => {
              if (waypoints.value[index]) {
                waypoints.value[index].location = waypointData.location
              }
            })
          })
        })
      }

      nextTick(() => {
        isInternalUpdate.value = false
      })
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => props.initialTripType,
  (val) => {
    if (!val) return
    if (localFormData.value.tripType === val) return

    if (props.initialFormData) {
      isInternalUpdate.value = true
      localFormData.value.tripType = val
      nextTick(() => {
        isInternalUpdate.value = false
        emitFormChange()
      })
      return
    }

    localFormData.value.tripType = val
    handleTripTypeChange(val)
  },
  { immediate: true }
)

watch(
  () => props.departureConfig?.value,
  (newValue) => {
    if (newValue !== undefined && !isInternalUpdate.value) {
      localFormData.value.departure = newValue
    }
  }
)

watch(
  () => props.destinationConfig?.value,
  (newValue) => {
    if (newValue !== undefined && !isInternalUpdate.value) {
      localFormData.value.destination = newValue
    }
  }
)

watch(
  localFormData,
  () => {
    if (!isInternalUpdate.value) {
      emitFormChange()
    }
  },
  { deep: true }
)

watch(
  waypoints,
  (newWaypoints) => {
    if (!isInternalUpdate.value) {
      localFormData.value.waypoints = newWaypoints.map((waypoint, index) => ({
        id: String(waypoint.id),
        location: waypoint.location,
        locationId: String(waypoint.id),
        locationType:
          localFormData.value.waypoints[index]?.locationType ||
          ('' as '' | 'prefecture' | 'area' | 'busStop')
      }))

      emitFormChange()
    }
  },
  { deep: true }
)

/* 親コンポーネントに公開するメソッド */
defineExpose({
  validateForm: () => formRef.value?.validate(),
  resetForm: () => formRef.value?.reset()
})
</script>
<template>
  <div class="one-way-round-trip-route">
    <v-form ref="formRef">
      <div v-if="enableTripType" class="trip-type-section">
        <v-btn-toggle
          v-model="localFormData.tripType"
          variant="outlined"
          color="primary"
          class="trip-type-toggle"
          mandatory
          @update:model-value="handleTripTypeChange"
        >
          <v-btn value="oneway" class="trip-type-button mr-1">片道</v-btn>
          <v-btn value="roundtrip" class="trip-type-button ml-1">往復</v-btn>
        </v-btn-toggle>
      </div>

      <div class="location-section">
        <div class="location-inputs">
          <v-text-field
            v-model="localFormData.departure"
            :rules="validationRules.departure"
            variant="outlined"
            density="comfortable"
            :placeholder="departureConfig?.placeholder || '出発地を選択'"
            :readonly="departureConfig?.readonly ?? true"
            hide-details="auto"
            class="location-input"
            @update:model-value="emit('departure-input', $event)"
            @click="emit('departure-click')"
          >
            <template v-if="departureConfig?.showMapButton" #append>
              <v-btn
                variant="text"
                size="small"
                color="primary"
                @click="emit('departure-map-click')"
              >
                地図で表示
              </v-btn>
            </template>
          </v-text-field>

          <div v-if="enableWaypoints" class="controls-section">
            <v-btn
              variant="outlined"
              color="primary"
              class="waypoint-button"
              prepend-icon="mdi-plus"
              :disabled="showWaypoints && !canAddMoreWaypoints"
              @click="showWaypoints ? addWaypoint() : toggleWaypoint()"
            >
              {{ waypointButtonText }}
            </v-btn>
          </div>

          <div
            v-if="enableWaypoints && showWaypoints && waypoints.length > 0"
            class="waypoints-container"
          >
            <v-fade-transition group>
              <div
                v-for="(waypoint, index) in waypoints"
                :key="waypoint.id"
                class="waypoint-item ml-4"
              >
                <v-text-field
                  v-model="waypoint.location"
                  variant="outlined"
                  density="comfortable"
                  :placeholder="`経由地${index + 1}を選択`"
                  :readonly="waypointConfig?.readonly ?? true"
                  hide-details
                  class="waypoint-input"
                  @update:model-value="handleWaypointChange(index, $event)"
                  @click:control="emit('waypoint-click', index)"
                >
                  <template #append>
                    <v-btn
                      icon
                      variant="text"
                      size="small"
                      @click="handleRemoveWaypoint(String(waypoint.id))"
                    >
                      <v-icon color="grey">mdi-close-circle</v-icon>
                    </v-btn>
                  </template>
                  <template v-if="waypointConfig?.showMapButton" #prepend>
                    <v-btn
                      variant="text"
                      size="small"
                      color="primary"
                      @click="emit('waypoint-map-click', index)"
                    >
                      地図
                    </v-btn>
                  </template>
                </v-text-field>
              </div>
            </v-fade-transition>
          </div>

          <v-text-field
            v-model="localFormData.destination"
            :rules="validationRules.destination"
            variant="outlined"
            density="comfortable"
            :placeholder="destinationConfig?.placeholder || '到着地を選択'"
            :readonly="destinationConfig?.readonly ?? true"
            hide-details="auto"
            class="location-input"
            @update:model-value="emit('destination-input', $event)"
            @click="emit('destination-click')"
          >
            <template v-if="destinationConfig?.showMapButton" #append>
              <v-btn
                variant="text"
                size="small"
                color="primary"
                @click="emit('destination-map-click')"
              >
                地図で表示
              </v-btn>
            </template>
          </v-text-field>
        </div>

        <div
          v-if="enableDateTime && localFormData.tripType === 'oneway'"
          class="datetime-section"
        >
          <v-text-field
            v-model="localFormData.date"
            :value="formatDate(localFormData.date)"
            :rules="validationRules.date"
            variant="outlined"
            density="comfortable"
            readonly
            placeholder="日付を選択"
            hide-details="auto"
            class="date-input"
            @click="openDatePicker('oneway')"
          >
            <template #append-inner>
              <v-icon color="primary">mdi-calendar</v-icon>
            </template>
          </v-text-field>

          <v-text-field
            v-model="localFormData.time"
            :value="formatTime(localFormData.time)"
            :rules="validationRules.time"
            variant="outlined"
            density="comfortable"
            readonly
            placeholder="時間を選択"
            hide-details="auto"
            class="time-input"
            @click="openTimePicker('oneway')"
          >
            <template #append-inner>
              <v-icon color="primary">mdi-clock-outline</v-icon>
            </template>
          </v-text-field>
        </div>

        <div
          v-if="enableDateTime && localFormData.tripType === 'roundtrip'"
          class="roundtrip-datetime"
        >
          <div class="trip-section">
            <p class="trip-label">行き</p>
            <div class="datetime-section">
              <v-text-field
                v-model="localFormData.outbound.date"
                :value="formatDate(localFormData.outbound?.date)"
                :rules="validationRules.outboundDate"
                variant="outlined"
                density="comfortable"
                readonly
                placeholder="日付を選択"
                hide-details="auto"
                class="date-input"
                @click="openDatePicker('outbound')"
              >
                <template #append-inner>
                  <v-icon color="primary">mdi-calendar</v-icon>
                </template>
              </v-text-field>

              <v-text-field
                v-model="localFormData.outbound.time"
                :value="formatTime(localFormData.outbound?.time)"
                :rules="validationRules.outboundTime"
                variant="outlined"
                density="comfortable"
                readonly
                placeholder="時間を選択"
                hide-details="auto"
                class="time-input"
                @click="openTimePicker('outbound')"
              >
                <template #append-inner>
                  <v-icon color="primary">mdi-clock-outline</v-icon>
                </template>
              </v-text-field>
            </div>

            <v-radio-group
              v-model="localFormData.outbound.direction"
              inline
              class="direction-radio"
              hide-details
            >
              <v-radio
                label="出発"
                value="departure"
                color="primary"
                class="radio-item"
              />
              <v-radio
                label="到着"
                value="arrival"
                color="primary"
                class="radio-item"
              />
            </v-radio-group>
          </div>

          <div class="trip-section">
            <p class="trip-label">帰り</p>
            <div class="datetime-section">
              <v-text-field
                v-model="localFormData.return.date"
                :value="formatDate(localFormData.return?.date)"
                :rules="validationRules.returnDate"
                variant="outlined"
                density="comfortable"
                readonly
                placeholder="日付を選択"
                hide-details="auto"
                class="date-input"
                @click="openDatePicker('return')"
              >
                <template #append-inner>
                  <v-icon color="primary">mdi-calendar</v-icon>
                </template>
              </v-text-field>

              <v-text-field
                v-model="localFormData.return.time"
                :rules="validationRules.returnTime"
                variant="outlined"
                density="comfortable"
                readonly
                placeholder="時間を選択"
                hide-details="auto"
                class="time-input"
                @click="openTimePicker('return')"
              >
                <template #append-inner>
                  <v-icon color="primary">mdi-clock-outline</v-icon>
                </template>
              </v-text-field>
            </div>

            <v-radio-group
              v-model="localFormData.return.direction"
              inline
              class="direction-radio"
              hide-details
            >
              <v-radio
                label="出発"
                value="departure"
                color="primary"
                class="radio-item"
              />
              <v-radio
                label="到着"
                value="arrival"
                color="primary"
                class="radio-item"
              />
            </v-radio-group>
          </div>
        </div>

        <v-radio-group
          v-if="enableDateTime && localFormData.tripType === 'oneway'"
          v-model="localFormData.direction"
          inline
          class="direction-radio"
          hide-details
        >
          <v-radio
            label="出発"
            value="departure"
            color="primary"
            class="radio-item"
          />
          <v-radio
            label="到着"
            value="arrival"
            color="primary"
            class="radio-item"
          />
        </v-radio-group>
      </div>

      <div class="search-section">
        <v-btn
          size="large"
          :color="searchButtonColor"
          variant="flat"
          class="search-button"
          @click="handleSearch"
        >
          {{ searchButtonText }}
          <v-icon class="ml-2">mdi-magnify</v-icon>
        </v-btn>
      </div>
    </v-form>
  </div>

  <v-overlay v-model="datePickerOpen" class="align-center justify-center">
    <DatePicker
      :selectedDateValue="currentSelectedDate"
      @date-selected="handleDateSelected"
      @close="closeDatePicker"
    />
  </v-overlay>

  <v-overlay v-model="timePickerOpen" class="align-center justify-center">
    <TimePicker
      :selectedTimeValue="currentSelectedTime"
      @time-selected="handleTimeSelected"
      @close="closeTimePicker"
    />
  </v-overlay>
</template>
<style scoped lang="scss">
.one-way-round-trip-route {
  border-radius: 10px;
  .trip-type-section {
    margin-bottom: 24px;
    padding-top: 10px;
  }

  .trip-type-toggle {
    background: #d9d9d9;
    border-radius: 33.6px;
    padding: 6px;
    width: 100%;
  }

  .trip-type-toggle :deep(.v-btn-group) {
    width: 100%;
    border-radius: 33.6px;
  }

  .trip-type-button {
    flex: 1;
    height: 35px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 400;
    background-color: #ffffff;
  }

  .trip-type-toggle :deep(.v-btn--active) {
    background: #26499d !important;
    color: #ffffff !important;
  }

  .trip-type-toggle :deep(.v-btn:not(.v-btn--active)) {
    color: #26499d !important;
    border: 1.5px solid #26499d;
  }

  .location-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .location-inputs {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .location-input :deep(.v-field) {
    border-radius: 8px;
    background: #f3f3f3;
    border: 2px solid transparent;
  }

  .location-input :deep(.v-field__input) {
    padding: 13px 18px;
    min-height: 50px;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
  }

  .location-input :deep(.v-field__input input::placeholder) {
    color: #7d7d7d;
  }

  .controls-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
  }

  .waypoint-button {
    border: 1px dashed #c1c7cd !important;
    color: #666 !important;
    font-size: 13px;
    background: transparent !important;
  }

  .waypoint-button :deep(.v-btn__prepend .v-icon) {
    width: 20px;
    height: 20px;
    background: #26499d;
    color: white;
    border-radius: 50%;
    font-size: 14px;
  }

  .waypoints-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;

    :deep(.v-input__append) {
      margin-inline-start: 0;
    }
  }

  .waypoint-input :deep(.v-field) {
    border-radius: 8px;
    background: #ffffff;
    border: 2px solid #dfdfdf;
  }

  .waypoint-input :deep(.v-field__input) {
    padding: 13px 16px;
    min-height: 50px;
  }

  .datetime-section {
    display: flex;
    gap: 8px;
  }

  .date-input {
    flex: 1;
  }

  .time-input {
    flex: 1;
  }

  .datetime-section :deep(.v-field) {
    border-radius: 2px;
    background: #ffffff;
    border: 2px solid #dfdfdf;
  }

  .datetime-section :deep(.v-field__input) {
    padding: 13px 16px;
    cursor: pointer;
    min-height: 50px;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
  }

  .roundtrip-datetime {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .trip-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .trip-label {
    color: #26499d;
    font-size: 16px;
    font-weight: 500;
    margin: 0;
  }

  .direction-radio {
    padding: 16px 0;
  }

  .direction-radio :deep(.v-selection-control-group) {
    gap: 48px;
    flex-direction: row;
  }

  .radio-item :deep(.v-label) {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    margin-left: 10px;
  }

  .radio-item :deep(.v-selection-control__wrapper) {
    width: 20px;
    height: 20px;
  }

  .radio-item :deep(.v-radio .v-selection-control__input) {
    width: 20px;
    height: 20px;
  }

  .search-section {
    text-align: center;
    padding: 8px 0;
  }

  .search-button {
    width: 230px !important;
    border-radius: 35px !important;
    height: 50px !important;
    font-size: 16px;
    color: #ffffff !important;
  }

  .search-button:disabled {
    background: #fbe4df !important;
    color: #ffffff !important;
    opacity: 0.6;
  }

  .search-button :deep(.v-btn__content) {
    gap: 10px;
  }

  .search-button:focus {
    box-shadow: 0 0 0 3px rgba(237, 120, 95, 0.3);
  }

  :deep(.v-field--variant-outlined .v-field__outline) {
    color: #e0e4e7;
  }

  :deep(.v-field__input) {
    padding: 16px;
    min-height: 56px;
  }

  :deep(.v-label) {
    color: #9aa0a6;
    font-size: 16px;
  }

  .v-fade-transition-enter-active,
  .v-fade-transition-leave-active {
    transition: opacity 0.3s ease;
  }

  .v-fade-transition-enter-from,
  .v-fade-transition-leave-to {
    opacity: 0;
  }

  .v-text-field--error :deep(.v-field__outline) {
    color: #f44336 !important;
  }

  .v-text-field--error :deep(.v-field__input) {
    color: #f44336;
  }

  @media (min-width: 960px) {
    .location-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      align-items: start;
    }

    .controls-section {
      grid-column: 1 / -1;
    }

    .waypoints-container {
      grid-column: 1 / -1;
    }

    .datetime-section {
      grid-column: 1 / -1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }

    .direction-radio {
      grid-column: 1 / -1;
    }

    .roundtrip-datetime {
      display: flex;
      flex-direction: row;
      gap: 32px;
      align-items: flex-start;
      position: relative;

      .trip-section {
        flex: 1;
        margin-bottom: 0;

        .datetime-section {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        }
      }

      &::before {
        content: '';
        position: absolute;
        left: calc(50% - 16px);
        top: 0;
        width: 1px;
        height: 200px;
        background-color: #e0e4e7;
      }
    }
  }

  @media (max-width: 375px) {
    .search-button {
      width: 100% !important;
      max-width: 300px;
    }

    .datetime-section {
      flex-direction: column;
      gap: 12px;
    }

    .roundtrip-datetime .datetime-section {
      flex-direction: column;
      gap: 8px;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .v-fade-transition-enter-active,
    .v-fade-transition-leave-active {
      transition: none;
    }
  }
}
</style>
