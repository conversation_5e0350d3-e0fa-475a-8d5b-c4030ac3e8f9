<script lang="ts" setup>
import StationDetailDialog from '~/components/StationDetailDialog.vue'
import { useRouteStore } from '~/stores/bus-service-selection'

definePageMeta({
  title: '予約確認',
  footerPlaceholderHeight: 100
})

const routeStore = useRouteStore()

const mapPreviewSrc = ref(
  'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d52522.52878166499!2d135.342561006546!3d34.6380806932309!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6000e9f83737a5e5%3A0x9e031ed1e92eefdf!2zMjAyNeW5tOaXpeacrOWbvemam-WNmuimp-S8muWNlOS8mg!5e0!3m2!1sja!2sjp!4v1744012296326!5m2!1sja!2sjp'
)

/* 設備情報の展開状態 */
const expandedAmenities = ref<{ [key: string | number]: boolean }>({})

/* ダイアログの表示状態 */
const showInfoDialog = ref(false)
const showReservationCompleteDialog = ref(false)
const showStationDetailDialog = ref(false)
const showFromToStationDialog = ref(false)
const showStationInfoDialog = ref(false)
const selectedStationName = ref('')
const showBackToTop = ref(false)

/* 設備情報の展開切り替え */
const toggleAmenities = (busIndex: number | string) => {
  expandedAmenities.value[busIndex] = !expandedAmenities.value[busIndex]
}

/* 表示する設備情報を取得 */
const getDisplayedAmenities = (amenities: any[], busIndex: number | string) => {
  const isExpanded = expandedAmenities.value[busIndex] || false
  if (isExpanded || amenities.length <= 6) {
    return amenities
  }
  return amenities.slice(0, 6)
}

/* スクロール監視 */
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

/* トップへ戻る */
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

/* 予約確認処理 */
const handleConfirmReservation = () => {
  showReservationCompleteDialog.value = true
}

/* 決済処理 */
const handlePayment = () => {
  showReservationCompleteDialog.value = false
}

/* TOP画面への遷移 */
const handleGoToTop = () => {
  showReservationCompleteDialog.value = false
}

/* 停留所詳細ダイアログ表示 */
const showStationDialog = (stationName: string) => {
  selectedStationName.value = stationName
  showStationDetailDialog.value = true
}

/* 停留所詳細ダイアログを閉じる */
const closeStationDialog = () => {
  showStationDetailDialog.value = false
  selectedStationName.value = ''
}

const showMapLink = () => {
  showFromToStationDialog.value = true
}

const closeMapLink = () => {
  showFromToStationDialog.value = false
}

/* 停留所詳細情報表示 */
const handleStationDetail = () => {
  showStationInfoDialog.value = true
}

/* 全ダイアログを閉じる */
const handleCloseAllDialogs = () => {
  showStationDetailDialog.value = false
  showStationInfoDialog.value = false
  selectedStationName.value = ''
}
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

/* Piniaストアからデータを取得 */
const inboundBusServices = computed(() => {
  // 往路のバスサービスを直接取得
  return (
    routeStore.routeSelected.routeInbound.routes?.flatMap(
      (route) => route.busService
    ) || []
  )
})

const outboundBusServices = computed(() => {
  // 復路のバスサービスを直接取得
  return (
    routeStore.routeSelected.routeOutbound.routes?.flatMap(
      (route) => route.busService
    ) || []
  )
})

/* 選択中ルートのデータ */
const selectedRoute = computed(() => {
  const inboundServices = inboundBusServices.value
  if (inboundServices.length === 0)
    return { cities: [], transferCount: 0, discountText: '' }

  // ルート情報から都市名を抽出
  const cities = inboundServices
    .map((service) => {
      const routeParts = service.route.split('→')
      return routeParts.map((part) => part.trim())
    })
    .flat()

  // 重複を除去して順序を保持
  const uniqueCities = [...new Set(cities)]

  return {
    cities: uniqueCities,
    transferCount: Math.max(0, inboundServices.length - 1),
    discountText: inboundServices.length > 1 ? '5%乗継割引適用中' : ''
  }
})

/* バス詳細データをPiniaストアから生成 */
const busDetails = computed(() => {
  return inboundBusServices.value.map((service, index) => {
    const routeParts = service.route.split('→')
    const departure = routeParts[0]?.trim() || ''
    const arrival = routeParts[1]?.trim() || ''

    // 乗客情報を解析
    const adultCount = service.adultCount || 0
    const passengerDetails = service.passengerDetails || ''

    // 座席番号を取得
    const seatNumbers =
      service.selectedSeats.length > 0
        ? service.selectedSeats.join('、')
        : 'A6、A7、B6、B7' // デフォルト値

    return {
      busNumber: service.tripNumber,
      date: service.datePicker,
      departure,
      arrival,
      departureTime: service.departureTime,
      departureStation: service.selectedStop || `${departure}駅`,
      arrivalTime: calculateArrivalTime(service.departureTime, '000時間00分'),
      arrivalStation: service.selectedDropOff || `${arrival}駅`,
      duration: '000時間00分',
      seatNumbers,
      adultPassengers: formatPassengerInfo(adultCount, 'adult'),
      childPassengers: formatPassengerInfo(0, 'child'), // 子供情報は別途実装が必要
      company: service.operator,
      busName: service.serviceName,
      vehicleNumber: '1号車',
      amenities: service.amenities,
      totalAmount: formatPrice(service.price)
    }
  })
})

/* 注文項目をPiniaストアから生成 */
const orderItems = computed(() => {
  return inboundBusServices.value.map((service, index) => {
    const adultCount = service.adultCount || 0
    const price = parseInt(service.price.replace(/,/g, '')) || 0

    return {
      busNumber: service.tripNumber,
      adultPrice: `${formatPrice(
        price.toString()
      )} 円 x ${adultCount}人 = ${formatPrice(
        (price * adultCount).toString()
      )} 円`,
      childPrice: '0 円 x 0人 = 0 円' // 子供料金は別途実装が必要
    }
  })
})

/* 支払い情報 */
const paymentInfo = computed(() => {
  const totalAmount = inboundBusServices.value.reduce((sum, service) => {
    const price = parseInt(service.price.replace(/,/g, '')) || 0
    const adultCount = service.adultCount || 0
    return sum + price * adultCount
  }, 0)

  return {
    totalAmount: formatPrice(totalAmount.toString())
  }
})

/* 予約者情報 */
const bookerInfo = ref({
  name: 'リョウビ　タロウ',
  phone: 'XXXX-XXXX-XXXX',
  email: 'XXXXXXX @example .com'
})

/* 復路の有無を判定 */
const hasReturnTrip = computed(() => outboundBusServices.value.length > 0)

/* 復路のルート情報 */
const returnRoute = computed(() => {
  const outboundServices = outboundBusServices.value
  if (outboundServices.length === 0)
    return { cities: [], transferCount: 0, discountText: '' }

  // ルート情報から都市名を抽出
  const cities = outboundServices
    .map((service) => {
      const routeParts = service.route.split('→')
      return routeParts.map((part) => part.trim())
    })
    .flat()

  // 重複を除去して順序を保持
  const uniqueCities = [...new Set(cities)]

  return {
    cities: uniqueCities,
    transferCount: Math.max(0, outboundServices.length - 1),
    discountText: outboundServices.length > 1 ? '5%乗継割引適用中' : ''
  }
})

/* 復路のバス詳細データ */
const returnBusDetails = computed(() => {
  return outboundBusServices.value.map((service, index) => {
    const routeParts = service.route.split('→')
    const departure = routeParts[0]?.trim() || ''
    const arrival = routeParts[1]?.trim() || ''

    // 乗客情報を解析
    const adultCount = service.adultCount || 0

    // 座席番号を取得
    const seatNumbers =
      service.selectedSeats.length > 0
        ? service.selectedSeats.join('、')
        : 'A6、A7、B6、B7' // デフォルト値

    return {
      busNumber: service.tripNumber,
      date: service.datePicker,
      departure,
      arrival,
      departureTime: service.departureTime,
      departureStation: service.selectedStop || `${departure}駅`,
      arrivalTime: calculateArrivalTime(service.departureTime, '000時間00分'),
      arrivalStation: service.selectedDropOff || `${arrival}駅`,
      duration: '000時間00分',
      seatNumbers,
      adultPassengers: formatPassengerInfo(adultCount, 'adult'),
      childPassengers: formatPassengerInfo(0, 'child'),
      company: service.operator,
      busName: service.serviceName,
      vehicleNumber: '1号車',
      amenities: service.amenities,
      totalAmount: formatPrice(service.price)
    }
  })
})

/* 復路の注文項目 */
const returnOrderItems = computed(() => {
  return outboundBusServices.value.map((service, index) => {
    const adultCount = service.adultCount || 0
    const price = parseInt(service.price.replace(/,/g, '')) || 0

    return {
      busNumber: service.tripNumber,
      adultPrice: `${formatPrice(
        price.toString()
      )} 円 x ${adultCount}人 = ${formatPrice(
        (price * adultCount).toString()
      )} 円`,
      childPrice: '0 円 x 0人 = 0 円'
    }
  })
})

/* ヘルパー関数 */
const formatPrice = (price: string): string => {
  const num = parseInt(price.replace(/,/g, '')) || 0
  return num.toLocaleString()
}

const formatPassengerInfo = (
  count: number,
  type: 'adult' | 'child'
): string => {
  if (count === 0) return '0名'

  // 簡単な分割（実際のプロジェクトでは詳細な乗客情報が必要）
  const maleCount = Math.floor(count / 2)
  const femaleCount = count - maleCount

  const parts = []
  if (maleCount > 0) parts.push(`男性 ${maleCount} 名`)
  if (femaleCount > 0) parts.push(`女性 ${femaleCount} 名`)

  return parts.join('、') || '0名'
}

const calculateArrivalTime = (
  departureTime: string,
  duration: string
): string => {
  // 簡単な到着時間計算（実際のプロジェクトではより詳細な計算が必要）
  const [hours, minutes] = departureTime.split(':').map(Number)
  const arrivalHours = (hours + 8) % 24 // 8時間後と仮定
  return `${arrivalHours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}`
}
</script>
<template>
  <div class="reservation-page">
    <div class="page-title-header">
      <h1 class="page-title">予約確認</h1>
    </div>
    <!-- 来路 -->
    <div class="from-section">
      <div class="selected-route">
        <div class="route-info-section">
          <div class="route-title">往路ルート</div>
          <div class="route-path">
            <template
              v-for="(city, index) in selectedRoute.cities"
              :key="index"
            >
              <span class="city">{{ city }}</span>
              <v-icon
                v-if="index < selectedRoute.cities.length - 1"
                color="#9CBCD4"
                size="20"
              >
                mdi-arrow-right
              </v-icon>
            </template>
          </div>
        </div>
        <div class="route-divider"></div>
        <div class="route-details">
          <div class="transfer-info">
            <v-icon color="#9CBCD4" size="20">mdi-airplane-landing</v-icon>
            <span class="transfer-text">
              乗継：{{ selectedRoute.transferCount }}回
            </span>
          </div>
          <div v-if="selectedRoute.discountText" class="discount-badge">
            <span class="discount-text">{{ selectedRoute.discountText }}</span>
          </div>
        </div>
      </div>
      <div class="bus-details-list">
        <div
          v-for="(bus, index) in busDetails"
          :key="index"
          :id="`bus-detail-${index}`"
          class="bus-detail-section"
        >
          <div class="bus-detail-card">
            <div class="bus-header">
              <div class="bus-icon">
                <v-icon color="#26499D">mdi-bus</v-icon>
              </div>
              <div class="bus-number">{{ bus.busNumber }}</div>
            </div>

            <div class="bus-info">
              <div class="route-info">
                <div class="date-section">
                  <div class="date">{{ bus.date }}</div>
                  <div class="boarding-label">乗車</div>
                </div>
                <div class="route-section">
                  <div class="departure">{{ bus.departure }}</div>
                  <v-icon color="#9CBCD4" size="20">mdi-arrow-right</v-icon>
                  <div class="arrival">{{ bus.arrival }}</div>
                </div>
              </div>

              <div class="divider"></div>

              <div class="time-info">
                <div class="time-line">
                  <div class="time-visual">
                    <div class="line"></div>
                    <div class="circle start"></div>
                    <div class="circle end"></div>
                  </div>
                  <div class="time-details">
                    <div class="departure-time">
                      <div class="time">{{ bus.departureTime }}</div>
                      <div
                        class="station"
                        @click="showStationDialog(bus.departureStation)"
                      >
                        {{ bus.departureStation }}
                      </div>
                    </div>
                    <div class="arrival-time">
                      <div class="time">{{ bus.arrivalTime }}</div>
                      <div
                        class="station"
                        @click="showStationDialog(bus.arrivalStation)"
                      >
                        {{ bus.arrivalStation }}
                      </div>
                    </div>
                  </div>
                </div>
                <span class="duration-badge">{{ bus.duration }}</span>
              </div>
            </div>

            <div class="section-header">座席番号</div>
            <div class="section-content">
              <div class="seat-numbers">{{ bus.seatNumbers }}</div>
            </div>

            <div class="section-header">乗車人数</div>
            <div class="section-content">
              <div class="passenger-info">
                <div class="passenger-row">
                  <div class="passenger-type">大人</div>
                  <div class="passenger-detail">{{ bus.adultPassengers }}</div>
                </div>
                <div class="passenger-row">
                  <div class="passenger-type">子供</div>
                  <div class="passenger-detail">{{ bus.childPassengers }}</div>
                </div>
              </div>
            </div>

            <div class="section-header">便情報詳細</div>
            <div class="section-content">
              <div class="bus-details-info">
                <div class="detail-row">
                  <div class="detail-label">運行会社</div>
                  <div class="detail-value">{{ bus.company }}</div>
                </div>
                <div class="detail-row">
                  <div class="detail-label">乗車便名</div>
                  <div class="detail-value">{{ bus.busName }}</div>
                </div>
                <div class="detail-row">
                  <div class="detail-label">車輌番号</div>
                  <div class="detail-value">{{ bus.vehicleNumber }}</div>
                </div>
              </div>
            </div>

            <div class="section-header">便情報詳細</div>
            <div class="section-content">
              <div class="amenities">
                <div class="amenities-tags">
                  <v-chip
                    v-for="amenity in getDisplayedAmenities(
                      bus.amenities,
                      index
                    )"
                    :key="amenity.name"
                    color="#ffffff"
                    :text-color="'#000000'"
                    size="small"
                    class="amenity-chip"
                  >
                    <v-icon
                      v-if="amenity.icon"
                      :color="amenity.iconColor || '#F2B774'"
                      size="16"
                      start
                    >
                      {{ amenity.icon }}
                    </v-icon>
                    {{ amenity.name }}
                  </v-chip>
                </div>
                <div
                  v-if="bus.amenities.length > 5"
                  class="toggle-button"
                  @click="toggleAmenities(index)"
                >
                  {{ expandedAmenities[index] ? '閉じる' : '…すべて見る' }}
                </div>
              </div>
            </div>

            <div class="total-section">
              <v-btn class="change-btn">予約変更</v-btn>
              <div class="total-info">
                <div class="total-label">総計</div>
                <div class="total-amount">{{ bus.totalAmount }}</div>
                <div class="currency">円</div>
              </div>
            </div>
          </div>

          <div v-if="index < busDetails.length - 1" class="bus-stop-guide">
            <v-icon color="#7D7D7D" size="24">mdi-play</v-icon>
            <div class="guide-text">次のバス停へはこちら</div>
            <a href="#" class="map-link" @click="showMapLink()">地図で表示</a>
          </div>
        </div>
      </div>
    </div>
    <!-- 復路 -->
    <div v-if="hasReturnTrip" class="to-section">
      <div class="selected-route">
        <div class="route-info-section">
          <div class="route-title">復路ルート</div>
          <div class="route-path">
            <template v-for="(city, index) in returnRoute.cities" :key="index">
              <span class="city">{{ city }}</span>
              <v-icon
                v-if="index < returnRoute.cities.length - 1"
                color="#9CBCD4"
                size="20"
              >
                mdi-arrow-right
              </v-icon>
            </template>
          </div>
        </div>
        <div class="route-divider"></div>
        <div class="route-details">
          <div class="transfer-info">
            <v-icon color="#9CBCD4" size="20">mdi-airplane-landing</v-icon>
            <span class="transfer-text">
              乗継：{{ returnRoute.transferCount }}回
            </span>
          </div>
          <div v-if="returnRoute.discountText" class="discount-badge">
            <span class="discount-text">{{ returnRoute.discountText }}</span>
          </div>
        </div>
      </div>
      <div class="bus-details-list">
        <div
          v-for="(bus, index) in returnBusDetails"
          :key="index"
          :id="`return-bus-detail-${index}`"
          class="bus-detail-section"
        >
          <div class="bus-detail-card">
            <div class="bus-header">
              <div class="bus-icon">
                <v-icon color="#26499D">mdi-bus</v-icon>
              </div>
              <div class="bus-number">{{ bus.busNumber }}</div>
            </div>

            <div class="bus-info">
              <div class="route-info">
                <div class="date-section">
                  <div class="date">{{ bus.date }}</div>
                  <div class="boarding-label">乗車</div>
                </div>
                <div class="route-section">
                  <div class="departure">{{ bus.departure }}</div>
                  <v-icon color="#9CBCD4" size="20">mdi-arrow-right</v-icon>
                  <div class="arrival">{{ bus.arrival }}</div>
                </div>
              </div>

              <div class="divider"></div>

              <div class="time-info">
                <div class="time-line">
                  <div class="time-visual">
                    <div class="line"></div>
                    <div class="circle start"></div>
                    <div class="circle end"></div>
                  </div>
                  <div class="time-details">
                    <div class="departure-time">
                      <div class="time">{{ bus.departureTime }}</div>
                      <div
                        class="station"
                        @click="showStationDialog(bus.departureStation)"
                      >
                        {{ bus.departureStation }}
                      </div>
                    </div>
                    <div class="arrival-time">
                      <div class="time">{{ bus.arrivalTime }}</div>
                      <div
                        class="station"
                        @click="showStationDialog(bus.arrivalStation)"
                      >
                        {{ bus.arrivalStation }}
                      </div>
                    </div>
                  </div>
                </div>
                <span class="duration-badge">{{ bus.duration }}</span>
              </div>
            </div>

            <div class="section-header">座席番号</div>
            <div class="section-content">
              <div class="seat-numbers">{{ bus.seatNumbers }}</div>
            </div>

            <div class="section-header">乗車人数</div>
            <div class="section-content">
              <div class="passenger-info">
                <div class="passenger-row">
                  <div class="passenger-type">大人</div>
                  <div class="passenger-detail">{{ bus.adultPassengers }}</div>
                </div>
                <div class="passenger-row">
                  <div class="passenger-type">子供</div>
                  <div class="passenger-detail">{{ bus.childPassengers }}</div>
                </div>
              </div>
            </div>

            <div class="section-header">便情報詳細</div>
            <div class="section-content">
              <div class="bus-details-info">
                <div class="detail-row">
                  <div class="detail-label">運行会社</div>
                  <div class="detail-value">{{ bus.company }}</div>
                </div>
                <div class="detail-row">
                  <div class="detail-label">乗車便名</div>
                  <div class="detail-value">{{ bus.busName }}</div>
                </div>
                <div class="detail-row">
                  <div class="detail-label">車輌番号</div>
                  <div class="detail-value">{{ bus.vehicleNumber }}</div>
                </div>
              </div>
            </div>

            <div class="section-header">便情報詳細</div>
            <div class="section-content">
              <div class="amenities">
                <div class="amenities-tags">
                  <v-chip
                    v-for="amenity in getDisplayedAmenities(
                      bus.amenities,
                      `return-${index}`
                    )"
                    :key="amenity.name"
                    color="#ffffff"
                    :text-color="'#000000'"
                    size="small"
                    class="amenity-chip"
                  >
                    <v-icon
                      v-if="amenity.icon"
                      :color="amenity.iconColor || '#F2B774'"
                      size="16"
                      start
                    >
                      {{ amenity.icon }}
                    </v-icon>
                    {{ amenity.name }}
                  </v-chip>
                </div>
                <div
                  v-if="bus.amenities.length > 5"
                  class="toggle-button"
                  @click="toggleAmenities(`return-${index}`)"
                >
                  {{
                    expandedAmenities[`return-${index}`]
                      ? '閉じる'
                      : '…すべて見る'
                  }}
                </div>
              </div>
            </div>

            <div class="total-section">
              <v-btn class="change-btn">予約変更</v-btn>
              <div class="total-info">
                <div class="total-label">総計</div>
                <div class="total-amount">{{ bus.totalAmount }}</div>
                <div class="currency">円</div>
              </div>
            </div>
          </div>

          <div
            v-if="index < returnBusDetails.length - 1"
            class="bus-stop-guide"
          >
            <v-icon color="#7D7D7D" size="24">mdi-play</v-icon>
            <div class="guide-text">次のバス停へはこちら</div>
            <a href="#" class="map-link" @click="showMapLink()">地図で表示</a>
          </div>
        </div>
      </div>
    </div>
    <!-- お支払い情報 -->
    <div class="payment-section">
      <h2 class="section-title">お支払い情報</h2>
      <div class="payment-card">
        <div class="order-content">
          <h3 class="order-title">注文内容</h3>
          <div class="order-items">
            <!-- 往路の注文項目 -->
            <div class="trip-section">
              <div class="trip-title">往路</div>
              <div
                v-for="(item, index) in orderItems"
                :key="`outbound-${index}`"
                class="order-item"
              >
                <a :href="`#bus-detail-${index}`" class="bus-link">
                  {{ item.busNumber }}
                </a>
                <div class="price-details">
                  <div class="price-row">
                    <div class="passenger-type">大人</div>
                    <div class="price-calculation">{{ item.adultPrice }}</div>
                  </div>
                  <div class="price-row">
                    <div class="passenger-type">子供</div>
                    <div class="price-calculation">{{ item.childPrice }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 復路の注文項目 -->
            <div v-if="hasReturnTrip" class="trip-section">
              <div class="trip-title">復路</div>
              <div
                v-for="(item, index) in returnOrderItems"
                :key="`return-${index}`"
                class="order-item"
              >
                <a :href="`#return-bus-detail-${index}`" class="bus-link">
                  {{ item.busNumber }}
                </a>
                <div class="price-details">
                  <div class="price-row">
                    <div class="passenger-type">大人</div>
                    <div class="price-calculation">{{ item.adultPrice }}</div>
                  </div>
                  <div class="price-row">
                    <div class="passenger-type">子供</div>
                    <div class="price-calculation">{{ item.childPrice }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="divider"></div>

          <div class="discount-section">
            <div class="discount-badge">5%乗継割引適用中</div>
            <div class="discount-amount">5,700円～</div>
            <div class="final-total">
              <div class="total-row">
                <div class="total-label">総計</div>
                <div class="total-amount">{{ paymentInfo.totalAmount }}</div>
                <div class="currency">円</div>
                <div class="tax-note">（税込）</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 复路 -->

    <!-- 予約者情報 -->
    <div class="booker-section">
      <h2 class="section-title">予約者情報</h2>
      <div class="booker-card">
        <div class="booker-row">
          <div class="booker-label">予約者</div>
          <div class="booker-value">{{ bookerInfo.name }}</div>
        </div>
        <div class="booker-row">
          <div class="booker-label">電話番号</div>
          <div class="booker-value">{{ bookerInfo.phone }}</div>
        </div>
        <div class="booker-row">
          <div class="booker-label">メールアドレス</div>
          <div class="booker-value">{{ bookerInfo.email }}</div>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <div v-show="showBackToTop" class="back-to-top-btn" @click="scrollToTop">
      <div class="back-to-top-icon">
        <div class="icon-line"></div>
        <div class="icon-arrow">
          <div class="arrow-stem"></div>
        </div>
      </div>
    </div>

    <!-- 底部固定容器 -->
    <div class="bottom-fixed-container">
      <div class="bottom-content">
        <div class="total-summary">
          <div class="total-amount-section">
            <div class="total-fixed">
              <div class="total-title">合計金額</div>
              <div class="total-amount-display">0,000,000</div>
              <div class="total-title">円</div>
            </div>
            <div class="total-note">※人数を選択すると表示されます</div>
            <div class="total-time">合計時間: 11時間35分</div>
          </div>
        </div>
        <div class="confirm-button-section">
          <v-btn class="confirm-btn disabled" @click="handleConfirmReservation">
            予約確認
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 予約ステップ情報ダイアログ -->
    <v-dialog
      v-model="showInfoDialog"
      max-width="500"
      max-height="80vh"
      persistent
    >
      <v-card class="info-dialog">
        <v-card-title class="info-dialog-title">予約ステップ</v-card-title>
        <v-card-text class="info-dialog-content scrollable-content">
          <div class="step-info-item">
            <div class="step-number">1.</div>
            <div class="step-info">
              <div class="step-title">ルート選択</div>
              <div class="step-description">
                目的地まで経由地の違いでルートを提案します。料金や所要時間などを考慮して選択ください。
              </div>
            </div>
          </div>

          <div class="step-info-item">
            <div class="step-number">2.</div>
            <div class="step-info">
              <div class="step-title">便選択</div>
              <div class="step-description">
                選択したルートで、車両装備や、出発時間/到着時間の違いで便がいくつか表示されるので希望の便を選択してください。
              </div>
            </div>
          </div>

          <div class="step-info-item">
            <div class="step-number">3.</div>
            <div class="step-info">
              <div class="step-title">予約確認</div>
              <div class="step-description">
                目的地まで経由地の違いでルートを提案します。料金や所要時間などを考慮して選択ください。
              </div>
            </div>
          </div>

          <div class="step-info-item">
            <div class="step-number">4.</div>
            <div class="step-info">
              <div class="step-title">予約確定</div>
              <div class="step-description">
                支払い方法を選択して、仮予約が完了します。入金が確認できましたら乗車券が発行されます。
              </div>
            </div>
          </div>
        </v-card-text>
        <v-card-actions class="info-dialog-actions">
          <v-btn class="close-dialog-btn" @click="showInfoDialog = false" block>
            閉じる
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 予約完了ダイアログ -->
    <v-dialog
      v-model="showReservationCompleteDialog"
      max-width="375"
      persistent
    >
      <v-card class="reservation-complete-dialog">
        <div class="dialog-content">
          <!-- タイトル -->
          <div class="dialog-title">予約完了</div>

          <!-- コンテンツ -->
          <div class="dialog-body">
            <div class="reservation-number">予約番号：　XXXXXXXXX</div>
            <div class="payment-deadline">
              決済期限：　2024年8月5日（月）まで
            </div>
            <div class="completion-message">
              予約が完了しました。予約完了メールを送信しましたので、必ず内容をご確認ください。
              <br />
              予約番号は予約の確認やお問い合わせに必要な番号です。大切に保管するようにお願いいたします。
            </div>
            <div class="payment-notice">
              お支払期限までにお支払をしてください。
            </div>
            <div class="cancellation-notice">
              お支払期限を過ぎた予約は自動でキャンセルされます。
            </div>
          </div>

          <!-- ボタン -->
          <div class="dialog-buttons">
            <v-btn class="payment-btn" @click="handlePayment" block>
              決済を行う
            </v-btn>
            <v-btn class="top-btn" @click="handleGoToTop" block>TOPへ</v-btn>
          </div>
        </div>
      </v-card>
    </v-dialog>

    <!-- 停留所詳細ダイアログ -->
    <v-dialog v-model="showStationDetailDialog" max-width="343" persistent>
      <v-card class="station-detail-dialog">
        <div class="station-dialog-content">
          <!-- ヘッダー -->
          <div class="station-header">
            <div class="station-title">停留所の場所</div>
            <v-btn
              class="station-detail-btn"
              @click="handleStationDetail"
              size="small"
            >
              停留所の詳細
            </v-btn>
          </div>

          <!-- 地図エリア -->
          <div class="station-map-area">
            <iframe
              v-if="mapPreviewSrc"
              :src="mapPreviewSrc"
              height="440"
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
              style="border: 0"
              title="地図プレビュー"
              width="100%"
            />
          </div>

          <!-- 閉じるボタン -->
          <div class="station-dialog-buttons">
            <v-btn class="close-station-btn" @click="closeStationDialog" block>
              閉じる
            </v-btn>
          </div>
        </div>
      </v-card>
    </v-dialog>

    <v-dialog v-model="showFromToStationDialog" max-width="343" persistent>
      <v-card class="station-detail-dialog">
        <div class="station-dialog-content">
          <!-- ヘッダー -->
          <div class="station-header">
            <div class="station-title">次のバス停までの経路</div>
            <div class="station-text station-text-top">
              <span class="station-name">XXXXXXXXXXXX</span>
              <v-icon color="#7D7D7D" size="20">mdi-arrow-right</v-icon>
              <span class="station-name">XXXXXXXXXXXX</span>
            </div>
          </div>

          <!-- 地図エリア -->
          <div class="station-map-area-ft">
            <iframe
              v-if="mapPreviewSrc"
              :src="mapPreviewSrc"
              height="440"
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
              style="border: 0"
              title="地図プレビュー"
              width="100%"
            />
          </div>

          <!-- 閉じるボタン -->
          <div class="station-dialog-buttons">
            <v-btn class="close-station-btn" @click="closeMapLink" block>
              閉じる
            </v-btn>
          </div>
        </div>
      </v-card>
    </v-dialog>

    <!-- 停留所詳細情報ダイアログ -->
    <StationDetailDialog
      v-model="showStationInfoDialog"
      :station-name="selectedStationName"
      @close-all="handleCloseAllDialogs"
    />
  </div>
</template>
<style scoped>
.reservation-page {
  padding: 0 0 60px;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.selected-route {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #dfdfdf;
}

.route-info-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 200px;
}

.route-title {
  font: 500 14px/1.2 sans-serif;
  color: #7d7d7d;
}

.route-path {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 2px;
}

.city {
  font: 500 16px/1.375 sans-serif;
  color: #000;
}

.route-divider {
  width: 1px;
  height: 36px;
  background: #9cbcd4;
}

.route-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.transfer-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.transfer-text {
  font: 400 14px/1.571 sans-serif;
  color: #232323;
  text-align: right;
}

.discount-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2px 8px;
  background-color: #ffe1e1;
  border-radius: 19px;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.2;
  color: #d00000;
}

.discount-text {
  font: 500 12px/1.2 sans-serif;
  color: #d00000;
  text-align: right;
}

.bus-details-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 343px;
  padding: 16px 0;
  margin: 0 auto;
}

.bus-detail-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  scroll-margin-top: 80px;
}

.bus-detail-card {
  display: flex;
  flex-direction: column;
  width: 343px;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #9cbcd4;
}

.bus-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 13px 16px;
  background: #e7f2fa;
  border-bottom: 1px solid #9cbcd4;
  height: 50px;
  position: relative;
}

.bus-icon {
  position: absolute;
  left: 16px;
}

.bus-number {
  font: 400 20px/1.2 sans-serif;
  color: #000;
  text-align: center;
}

.bus-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: #fff;
}

.route-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-section {
  display: flex;
  gap: 4px;
  align-items: center;
}

.date,
.boarding-label {
  font: 500 16px/1.375 sans-serif;
  color: #000;
}

.route-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.departure,
.arrival {
  font: 500 18px/1.333 sans-serif;
  color: #000;
}

.divider {
  width: 311px;
  height: 1px;
  background: #d9d9d9;
  margin: 12px 0;
}

.time-info {
  align-items: center;
  gap: 8px;
}

.time-line {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-visual {
  position: relative;
  width: 12px;
  height: 51px;
}

.line {
  position: absolute;
  left: 5px;
  top: 11px;
  width: 2px;
  height: 34px;
  background: #9cbcd4;
}

.circle {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #9cbcd4;
  border-radius: 50%;
  background: #fff;
}

.circle.start {
  top: 0;
}
.circle.end {
  bottom: 0;
}

.time-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 180px;
}

.departure-time,
.arrival-time {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time {
  font: 700 22px/1.364 sans-serif;
  color: #000;
}

.station {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.2;
  color: #26499d;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease;
}

.duration-badge {
  padding: 2px 8px;
  background: #e7f2fa;
  border-radius: 19px;
  font: 500 12px/1.2 sans-serif;
  color: #26499d;
  white-space: nowrap;
  flex-shrink: 0;
  margin-top: 5px;
}

.section-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 10px;
  background: #e7f2fa;
  border-top: 1px solid #9cbcd4;
  font: 400 14px/1.571 sans-serif;
  color: #26499d;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #9cbcd4;
}

.seat-numbers,
.passenger-type,
.detail-label {
  font: 500 16px/1.375 sans-serif;
  color: #000;
}

.passenger-detail,
.detail-value {
  font: 400 16px/1.563 sans-serif;
  color: #000;
}

.passenger-info,
.bus-details-info,
.passenger-row,
.detail-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.amenities {
  position: relative;
  width: 311px;
}

.amenities-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.amenity-chip {
  border: 1px solid #9cbcd4 !important;
  border-radius: 26px !important;
  font: 400 14px/1.2 sans-serif !important;
}

.toggle-button {
  position: absolute;
  right: 0;
  top: 40px;
  font: 500 14px/1.2 sans-serif;
  color: #26499d;
  text-align: right;
  cursor: pointer;
  user-select: none;
}

.total-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: #fff;
  border-radius: 0 0 10px 10px;
  height: 48px;
}

.total-info {
  display: flex;
  align-items: flex-end;
  gap: 2px;
}

.total-label,
.currency {
  font: 400 14px/1.571 sans-serif;
  color: #232323;
}

.total-amount {
  font: 700 24px/1.2 sans-serif;
  color: #232323;
  text-align: right;
}

.change-btn {
  width: 100px;
  height: 36px;
  background: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 7px !important;
  font: 500 16px/1.2 sans-serif !important;
  color: #26499d !important;
  text-transform: none !important;
  box-shadow: none !important;
}

.bus-stop-guide {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 0 24px;
  width: 343px;
}

.guide-text {
  font: 500 16px/1.375 sans-serif;
  color: #000;
}

.map-link {
  font: 500 14px/1.2 sans-serif;
  color: #26499d;
  text-decoration: none;
}

.payment-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  max-width: 343px;
}

.section-title {
  font: 500 18px/1.333 sans-serif;
  color: #000;
  margin: 5px 0;
}

.payment-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
  padding: 20px;
  width: 341px;
  background: #fff;
  border: 1px solid #9cbcd4;
  border-radius: 10px;
}

.order-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.order-title {
  font: 500 16px/1.375 sans-serif;
  color: #000;
  margin: 0;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.trip-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trip-title {
  font: 500 16px/1.375 sans-serif;
  color: #26499d;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.order-item {
  display: flex;
  align-items: center;
  gap: 32px;
  width: 302px;
}

.bus-link {
  font: 500 14px/1.2 sans-serif;
  color: #26499d;
  text-decoration: none;
}

.price-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.price-row {
  display: flex;
  align-items: center;
  width: 229px;
  height: 22px;
}

.price-row .passenger-type,
.price-calculation {
  font: 400 14px/1.571 sans-serif;
  color: #000;
}

.price-row .passenger-type {
  width: 28px;
  height: 22px;
}

.price-calculation {
  width: 169px;
  height: 22px;
  text-align: right;
}

.discount-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  width: 302px;
}

.discount-amount {
  width: 104px;
  height: 22px;
  font: 400 14px/1.571 sans-serif;
  color: #7d7d7d;
  text-decoration: line-through;
}

.final-total {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  gap: 2px;
  width: 100%;
}

.total-row {
  display: flex;
  align-items: flex-end;
  gap: 2px;
}

.total-row .total-label,
.total-row .currency {
  font: 400 14px/1.571 sans-serif;
  color: #232323;
}

.total-row .total-label {
  width: 28px;
  height: 22px;
  margin-top: 8px;
}

.total-row .total-amount {
  font: 700 24px/1.2 sans-serif;
  color: #232323;
}

.tax-note {
  font: 400 14px/1.571 sans-serif;
  color: #000;
}

.booker-section {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 343px;
}

.booker-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  width: 341px;
  background: #fff;
  border: 1px solid #9cbcd4;
  border-radius: 10px;
}

.booker-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.booker-label {
  font: 500 16px/1.375 sans-serif;
  color: #000;
}

.booker-value {
  font: 400 14px/1.571 sans-serif;
  color: #000;
}

.page-title-header {
  background: #fff;
  padding: 12px 16px;
  text-align: center;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font: 400 20px/1.2 sans-serif;
  color: #26499d;
  margin: 0;
}

:deep(.v-chip__content) {
  color: #000;
}

.back-to-top-btn {
  position: fixed;
  bottom: 120px;
  right: 0;
  width: 45px;
  height: 45px;
  background: #26499d;
  border: 1px solid #9cbcd4;
  border-right: none;
  border-radius: 5px 0 0 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.back-to-top-btn:hover {
  background: #1e3a7a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.back-to-top-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}

.back-to-top-icon {
  position: relative;
  width: 23.33px;
  height: 26.25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon-line {
  width: 20px;
  height: 3px;
  background: #fff;
  border-radius: 1.5px;
  margin-bottom: 4px;
}

.icon-arrow {
  position: relative;
  width: 16px;
  height: 16px;
}

.icon-arrow::before,
.icon-arrow::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 10px;
  background: #fff;
  border-radius: 1.5px;
}

.icon-arrow::before {
  transform: rotate(45deg);
  left: 3px;
  top: 2px;
}

.icon-arrow::after {
  transform: rotate(-45deg);
  right: 3px;
  top: 2px;
}

.icon-arrow .arrow-stem {
  position: absolute;
  width: 3px;
  height: 12px;
  background: #fff;
  border-radius: 1.5px;
  left: 50%;
  top: 6px;
  transform: translateX(-50%);
}

.bottom-fixed-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #dfdfdf;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.bottom-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  max-width: 100%;
  margin: 0 auto;
}

.total-summary {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.total-amount-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.total-title {
  font: 500 18px/1.333 sans-serif;
  color: #000;
}

.total-amount-display {
  font: 700 24px/1.2 sans-serif;
  margin: 4px 0 4px 7px;
}

.total-note {
  font: 400 12px/1.2 sans-serif;
  color: #7d7d7d;
}

.total-time {
  font: 500 16px/1.375 sans-serif;
  color: #000;
}

.confirm-button-section {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.confirm-btn {
  width: 120px;
  height: 48px;
  background: #26499d !important;
  border-radius: 8px !important;
  font: 500 16px/1.2 sans-serif !important;
  color: #fff !important;
  text-transform: none !important;
  box-shadow: 0 2px 4px rgba(38, 73, 157, 0.3) !important;
}

.confirm-btn:hover {
  background: #1e3a7a !important;
  box-shadow: 0 4px 8px rgba(38, 73, 157, 0.4) !important;
}

.confirm-btn.disabled,
.confirm-btn:disabled {
  background: #c4c4c4 !important;
  color: #7d7d7d !important;
  box-shadow: none !important;
  cursor: not-allowed !important;
  opacity: 1 !important;
}

.confirm-btn.disabled:hover,
.confirm-btn:disabled:hover {
  background: #c4c4c4 !important;
  color: #7d7d7d !important;
  box-shadow: none !important;
  transform: none !important;
}

.info-dialog {
  border-radius: 16px !important;
  background: #fff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.info-dialog-title {
  font: 700 20px/1.2 sans-serif !important;
  color: #000 !important;
  text-align: center !important;
  padding: 24px 24px 16px !important;
}

.info-dialog-content {
  padding: 0 24px 16px !important;
}

.scrollable-content {
  max-height: 60vh !important;
  overflow-y: auto !important;
  scrollbar-width: thin !important;
  scrollbar-color: #9cbcd4 #f0f0f0 !important;
}

.scrollable-content::-webkit-scrollbar {
  width: 6px;
}
.scrollable-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}
.scrollable-content::-webkit-scrollbar-thumb {
  background: #9cbcd4;
  border-radius: 3px;
}
.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #7ba3c4;
}

.step-info-item {
  display: flex;
  margin-bottom: 24px;
}

.step-info-item:last-child {
  margin-bottom: 0;
}

.step-number {
  font: 700 16px/1.375 sans-serif;
  color: #000;
  min-width: 20px;
}

.step-info {
  flex: 1;
}

.step-title {
  font: 700 16px/1.375 sans-serif;
  color: #000;
  margin-bottom: 8px;
}

.step-description {
  font: 400 14px/1.571 sans-serif;
  color: #000;
}

.info-dialog-actions {
  padding: 16px 24px 24px !important;
}

.close-dialog-btn {
  width: 100% !important;
  height: 48px !important;
  background: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 8px !important;
  font: 500 16px/1.2 sans-serif !important;
  color: #26499d !important;
  text-transform: none !important;
  box-shadow: none !important;
}

.close-dialog-btn:hover {
  background: #d6e9f5 !important;
}

.reservation-complete-dialog {
  border-radius: 10px !important;
  background: #fff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 580px;
}

.dialog-title {
  font: 400 20px/1.2 sans-serif;
  color: #000;
  text-align: left;
  width: 311px;
}

.dialog-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 311px;
}

.reservation-number,
.payment-deadline {
  font: 400 16px/1.5625 sans-serif;
  color: #000;
  text-align: left;
}

.completion-message,
.payment-notice {
  font: 400 14px/1.571 sans-serif;
  color: #000;
  text-align: left;
}

.cancellation-notice {
  font: 400 14px/1.571 sans-serif;
  color: #d00000;
  text-align: left;
  width: 311px;
  height: 44px;
}

.dialog-buttons {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  padding: 24px 0 0;
  width: 311px;
  position: absolute;
  bottom: 24px;
  left: 16px;
}

.payment-btn {
  width: 311px !important;
  height: 48px !important;
  background: #ed785f !important;
  border-radius: 4px !important;
  font: 400 16px/1.2 sans-serif !important;
  color: #fff !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.payment-btn:hover {
  background: #e66b50 !important;
}

.top-btn {
  width: 311px !important;
  height: 48px !important;
  background: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font: 400 16px/1.2 sans-serif !important;
  color: #26499d !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.top-btn:hover {
  background: #d6e9f5 !important;
}

.station-detail-dialog {
  border-radius: 10px !important;
  background: #fff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.station-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 580px;
}

.station-header {
  width: 100%;
  position: relative;
}

.station-title {
  font: 400 20px/1.2 sans-serif;
  color: #000;
}

.station-detail-btn {
  position: absolute;
  right: 0;
  top: -4px;
  width: auto !important;
  height: 32px !important;
  background: #fff !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font: 400 14px/1.2 sans-serif !important;
  color: #000 !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 6px 16px !important;
  min-width: auto !important;
}

.station-detail-btn:hover {
  background: #f5f5f5 !important;
}

.station-map-area {
  width: 311px;
  height: 412px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.station-map-area-ft {
  width: 311px;
  height: 375px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.map-text {
  font: 500 18px/1.333 sans-serif;
  color: #26499d;
  text-align: center;
}

.map-subtext {
  font: 400 14px/1.571 sans-serif;
  color: #7d7d7d;
  text-align: center;
}

.station-dialog-buttons {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  width: 311px;
  position: absolute;
  bottom: 24px;
  left: 16px;
}

.close-station-btn {
  width: 311px !important;
  height: 48px !important;
  background: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font: 400 16px/1.2 sans-serif !important;
  color: #26499d !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.close-station-btn:hover {
  background: #d6e9f5 !important;
}

/* 駅名表示コンテナのスタイル */
.station-text {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 311px;
  height: 24px;
}

.station-text .station-name {
  font: 400 20px/1.2 sans-serif;
  color: #000;
  width: 137px;
  height: 24px;
  display: flex;
  align-items: center;
}

.station-text-top {
  margin-top: 15px;
}

.total-fixed {
  display: flex;
  align-items: center;
}
</style>
