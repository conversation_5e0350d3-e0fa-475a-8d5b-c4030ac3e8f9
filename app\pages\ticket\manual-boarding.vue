<script lang="ts" setup>
// ページメタデータ設定
definePageMeta({
  footerPlaceholderHeight: 163
})

const router = useRouter()

// 戻るボタンの処理
const handleBack = (): void => {
  router.back()
}

// 乗車ボタンの処理
const handleBoarding = (): void => {
  router.push('/ticket/manual')
}
</script>
<template>
  <div class="manual-boarding-page">
    <div class="page-title-header">
      <button class="back-button" @click="handleBack">
        <img src="~/assets/image/Icon.png" alt="戻る" class="back-icon" />
      </button>
      <h1 class="page-title">乗車券</h1>
    </div>

    <v-main class="main-content">
      <v-container class="pa-4">
        <v-card class="boarding-info-card" elevation="0" rounded="lg">
          <div class="card-content">
            <div class="date-route-section">
              <div class="date-info">
                <span class="date-text">
                  <span class="date-text-md">3/21</span>
                  （金）
                </span>
                <v-chip color="#D9D9D9" class="route-chip" variant="flat">
                  岡山 ～ 大阪線
                </v-chip>
              </div>
            </div>

            <div class="time-route-section">
              <div class="vertical-line"></div>
              <div class="time-details">
                <div class="time-info-text">
                  <div class="time-info-text-font">
                    23:20 発　岡山駅 西口
                    <br />
                    08:30 着 2/8(土)　大阪駅JR高速BT
                    <br />
                  </div>
                  <div class="time-text">所要時間：9時間10分</div>
                </div>
              </div>
            </div>

            <div class="bus-info-section">
              <span class="bus-info-text">
                中国ハイウェイバス
                <br />
                ○○○号　1号車
              </span>
            </div>

            <div class="seat-section">
              <div class="seat-title">座席番号</div>
              <div class="seat-numbers">
                <div
                  v-for="seat in ['B1', 'B2', 'B3', 'C1', 'C2', 'C3', 'C4']"
                  :key="seat"
                  class="seat-chip"
                >
                  {{ seat }}
                </div>
              </div>
            </div>
          </div>
        </v-card>

        <v-card class="reservation-info-card mt-4" elevation="0" rounded="lg">
          <div class="reservation-content">
            <div class="reservation-labels">
              <div class="label-text">予約番号</div>
              <div class="label-text">予約者</div>
              <div class="label-text">乗車人数</div>
            </div>
            <div class="reservation-values">
              <div class="value-text">99912045678</div>
              <div class="value-text">リョウビ　タロウ　様</div>
              <div class="value-text">大人4名　子供3名</div>
            </div>
          </div>
        </v-card>
      </v-container>
    </v-main>

    <div class="boarding-footer">
      <div class="instruction-text">
        乗務員が確認の上、
        <br />
        乗車ボタンを押してください。
      </div>

      <div class="warning-section">
        <v-icon size="24" color="#D00000" class="warning-icon">
          mdi-alert
        </v-icon>
        <span class="warning-text">乗車券は再利用できません。</span>
      </div>

      <v-btn
        color="#ED785F"
        variant="flat"
        block
        size="large"
        class="boarding-btn"
        @click="handleBoarding"
      >
        乗車する
      </v-btn>
    </div>
  </div>
</template>

<style scoped>
.manual-boarding-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.page-title-header {
  background-color: #ffffff;
  padding: 12px 16px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 16px;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: rgba(38, 73, 157, 0.1);
}

.back-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  transform: rotate(-90deg);
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}

.main-content {
  flex: 1;
  background-color: #f5f5f5;
  padding-bottom: 200px; /* フッターの高さ分の余白 */
  padding-top: 0;
}

.boarding-info-card {
  background: white;
  border-radius: 5px;
  overflow: hidden;
}

.card-content {
  padding: 10px;
}

.date-route-section {
  margin-bottom: 10px;
}

.date-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 36px;
}

.date-text {
  font-weight: 500;
  font-size: 16.71px;
  line-height: 1.2;
  color: #000000;
}
.date-text-md {
  font-weight: bold;
  font-size: 26px;
}

.route-chip {
  background-color: #d9d9d9 !important;
  color: #000000 !important;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.2;
  height: auto !important;
  min-height: auto !important;
  border-radius: 23px !important;
}

/* Vuetifyチップの内部スタイルを調整 */
:deep(.route-chip .v-chip__content) {
  padding: 6px 15px;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.2;
  color: #000000;
}

.time-route-section {
  display: flex;
  align-items: flex-start;
  padding: 0 12px;
  margin-bottom: 10px;
}

.vertical-line {
  width: 1px;
  height: 31px;
  background-color: #3b3b3b;
  margin-right: 12px;
  margin-top: 12px;
  position: relative;
}

/* 垂直線の上端に実心円を追加 */
.vertical-line::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  width: 7px;
  height: 7px;
  background-color: #3b3b3b;
  border-radius: 50%;
}

/* 垂直線の下端に実心円を追加 */
.vertical-line::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: -3px;
  width: 7px;
  height: 7px;
  background-color: #3b3b3b;
  border-radius: 50%;
}

.time-details {
  flex: 1;
}

.time-info-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.93;
  color: #000000;
}
.time-info-text-font {
  font-weight: bold;
}
.bus-info-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 10px;
}

.bus-info-text {
  font-weight: 400;
  font-size: 16px;
  line-height: 1.56;
  color: #000000;
}

.seat-section {
  margin-top: 10px;
}

.seat-title {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
  color: #5e5e5e;
  margin-bottom: 5px;
}

.seat-numbers {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.seat-chip {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #e7f2fa;
  color: #000000;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.375;
  padding: 0 12px;
  height: 33px;
  border-radius: 4px;
  min-width: fit-content;
}

.reservation-info-card {
  background: white;
  border-radius: 5px;
}

.reservation-content {
  display: flex;
  align-items: center;
  gap: 33px;
  padding: 8px 13px;
}

.reservation-labels {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label-text {
  font-weight: 400;
  font-size: 16px;
  line-height: 1.56;
  color: #000000;
}

.reservation-values {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.value-text {
  font-weight: 500;
  font-size: 18px;
  line-height: 1.33;
  color: #000000;
}

.boarding-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #cccbca;
  box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.15);
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.instruction-text {
  font-size: 16px;
  line-height: 1.56;
  text-align: center;
  color: #000000;
  width: 291px;
  font-weight: bold;
}

.warning-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.warning-icon {
  flex-shrink: 0;
}

.warning-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.57;
  color: #d00000;
}

.boarding-btn {
  width: 343px;
  height: 50px;
  background-color: #ed785f !important;
  color: white !important;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.2;
  border-radius: 35px !important;
  text-transform: none;
  letter-spacing: normal;
}

.boarding-btn:hover {
  background-color: #e66b4f !important;
}

/* Vuetifyのデフォルトスタイルをオーバーライド */
:deep(.v-chip) {
  border: none !important;
}

:deep(.v-chip--variant-tonal .v-chip__underlay) {
  opacity: 1 !important;
}

/* 路線チップの追加スタイル調整 */
:deep(.route-chip) {
  box-shadow: none !important;
  border: none !important;
}

:deep(.route-chip .v-chip__underlay) {
  background-color: #d9d9d9 !important;
  opacity: 1 !important;
}
.time-text {
  color: #7d7d7d;
}
:deep(.justify-space-between) {
  align-items: baseline !important;
}
</style>
