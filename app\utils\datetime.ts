export function getFormattedCurrentYearMonth() {
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const currentMonth = currentDate.getMonth() + 1
  return `${currentYear}年${String(currentMonth).padStart(2, '0')}月分`
}

export function generateRecentDates(days: number): string[] {
  const result: string[] = []
  const now = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(now.getDate() - i)
    const month = date.getMonth() + 1
    const day = date.getDate()
    result.push(`${month}/${day}`)
  }
  return result
}

export function dateToWeekdayFormat(dateInput: string | Date) {
  let date: Date

  if (typeof dateInput === 'string') {
    date = new Date(dateInput.replace(/\//g, '-'))
  } else {
    date = dateInput
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const weekDays = ['日', '月', '火', '水', '木', '金', '土']
  const weekDay = weekDays[date.getDay()]
  return `${year}/${month}/${day}（${weekDay}）`
}

export function formatDateToString(date: string | null) {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}/${month}/${day}`
}

export function formatDateTimeToString(date: string | null) {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  return `${year}/${month}/${day} ${hours}:${minutes}`
}
