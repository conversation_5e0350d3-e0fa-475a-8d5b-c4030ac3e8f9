<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

/* Props */
const props = defineProps({
  selectedTimeValue: {
    type: String,
    default: '指定なし'
  }
})

const emit = defineEmits(['timeSelected', 'close'])

const currentTab = ref('timeSpecified') 
const visibleItemsCount = 5
const middlePosition = Math.floor(visibleItemsCount / 2)

const hourList = ref(Array.from({ length: 25 }, (_, i) => `${i}`.padStart(2, '0')))
const minuteList = ref(Array.from({ length: 6 }, (_, i) => `${i * 10}`.padStart(2, '0')))

const hourActiveIndex = ref(12) 
const minuteActiveIndex = ref(3) 

const hourTranslate = ref(0)
const minuteTranslate = ref(0)

const rowHeight = 34

const clamp = (v: number, a: number, b: number): number => Math.max(a, Math.min(b, v))

function syncScrollPosition(column: 'hour' | 'minute'): void {
  if (column === 'hour') hourTranslate.value = (middlePosition - hourActiveIndex.value) * rowHeight
  else minuteTranslate.value = (middlePosition - minuteActiveIndex.value) * rowHeight
}

function snapToNearestIndex(column: 'hour' | 'minute'): void {
  if (column === 'hour') hourActiveIndex.value = Math.round(hourActiveIndex.value)
  else minuteActiveIndex.value = Math.round(minuteActiveIndex.value)
  syncScrollPosition(column)
}

const parseTimeValue = (timeString: string | undefined): { tab: string; hour: number; minute: number } | null => {
  if (!timeString || typeof timeString !== 'string' || timeString === '指定なし') {
    return null
  }
  if (
    !timeString ||
    typeof timeString !== 'string' ||
    timeString === '指定なし'
  ) {
    return {
      tab: 'none',
      hour: 12,
      minute: 6
    }
  }

  const timeParts = timeString.split(':')
  if (timeParts.length === 2) {
    const hour = parseInt(timeParts[0]!)
    const minute = parseInt(timeParts[1]!)
    if (!isNaN(hour) && !isNaN(minute) && hour >= 0 && hour <= 24 && minute >= 0 && minute <= 59) {
      const minuteIndex = Math.round(minute / 10)
      return {
        tab: 'timeSpecified',
        hour,
        minute: Math.min(minuteIndex, minuteList.value.length - 1)
      }
    }
  }
  return null
}

const initializeTimeValue = () => {
  const parsed = parseTimeValue(props.selectedTimeValue)
  if (parsed) {
    currentTab.value = parsed.tab
    hourActiveIndex.value = parsed.hour
    minuteActiveIndex.value = parsed.minute
  } else {
    const now = new Date()
    let hour = now.getHours()
    let minute = now.getMinutes()

    const roundedMinute = Math.ceil(minute / 10) * 10
    if (roundedMinute === 60) {
      hour += 1
      minute = 0
    } else {
      minute = roundedMinute
    }

    hourActiveIndex.value = Math.min(hour, hourList.value.length - 1)
    minuteActiveIndex.value = Math.min(minute / 10, minuteList.value.length - 1)
    currentTab.value = 'timeSpecified'
  }
  syncScrollPosition('hour')
  syncScrollPosition('minute')
}

const touchState = ref({
  isDragging: false,
  startY: 0,
  startIndex: 0,
  column: '',
  lastY: 0,
  lastTime: 0,
  velocity: 0
})

const mouseMoveListener = (e: MouseEvent) => handleTouchMove(e, touchState.value.column as 'hour' | 'minute')
const mouseUpListener = (e: MouseEvent) => handleTouchEnd(touchState.value.column as 'hour' | 'minute')

const handleWheel = (e: WheelEvent, column: 'hour' | 'minute'): void => {
  e.stopPropagation()
  const delta = e.deltaY > 0 ? 1 : -1
  if (column === 'hour') hourActiveIndex.value = clamp(hourActiveIndex.value + delta, 0, hourList.value.length - 1)
  else minuteActiveIndex.value = clamp(minuteActiveIndex.value + delta, 0, minuteList.value.length - 1)
  syncScrollPosition(column)
}

function handleTouchStart(e: TouchEvent | MouseEvent, column: 'hour' | 'minute'): void {
  const y = 'touches' in e ? e.touches[0]?.clientY || 0 : e.clientY
  touchState.value = { ...touchState.value, isDragging: true, startY: y, lastY: y, lastTime: Date.now(), velocity: 0, column, startIndex: column === 'hour' ? hourActiveIndex.value : minuteActiveIndex.value }
  if (!('touches' in e)) {
    document.addEventListener('mousemove', mouseMoveListener)
    document.addEventListener('mouseup', mouseUpListener)
  }
}

function handleTouchMove(e: TouchEvent | MouseEvent, column: 'hour' | 'minute'): void {
  if (!touchState.value.isDragging || touchState.value.column !== column) return
  const y = 'touches' in e ? e.touches[0]?.clientY || 0 : e.clientY
  const now = Date.now()
  const dt = now - touchState.value.lastTime
  if (dt > 0) touchState.value.velocity = (y - touchState.value.lastY) / dt
  touchState.value.lastY = y
  touchState.value.lastTime = now
  const totalDiffY = y - touchState.value.startY
  const diffIndex = Math.round(totalDiffY / rowHeight)
  const newIndex = touchState.value.startIndex - diffIndex
  if (column === 'hour') hourActiveIndex.value = clamp(newIndex, 0, hourList.value.length - 1)
  else minuteActiveIndex.value = clamp(newIndex, 0, minuteList.value.length - 1)
  syncScrollPosition(column)
}

function handleTouchEnd(column: 'hour' | 'minute'): void {
  if (touchState.value.column !== column) return
  let extra = clamp(Math.round((touchState.value.velocity || 0) * 20), -3, 3)
  if (column === 'hour') hourActiveIndex.value = clamp(hourActiveIndex.value - extra, 0, hourList.value.length - 1)
  else minuteActiveIndex.value = clamp(minuteActiveIndex.value - extra, 0, minuteList.value.length - 1)
  snapToNearestIndex(column)
  touchState.value.isDragging = false
  touchState.value.column = ''
  document.removeEventListener('mousemove', mouseMoveListener)
  document.removeEventListener('mouseup', mouseUpListener)
}

const handleTabChange = (tab: string): void => {
  currentTab.value = tab
}

const cancel = () => emit('close')
const confirm = () => {
  let timeValue = ''
  if (currentTab.value === 'timeSpecified') {
    timeValue = `${hourList.value[hourActiveIndex.value]}:${minuteList.value[minuteActiveIndex.value]}`
  } else if (currentTab.value === 'first') {
    timeValue = '始発'
  } else if (currentTab.value === 'last') {
    timeValue = '最終発'
  }
  emit('timeSelected', timeValue)
  emit('close')
}

const preventBackgroundScroll = (e: TouchEvent): void => e.preventDefault()

watch(() => props.selectedTimeValue, initializeTimeValue, { immediate: true })

onMounted(() => document.addEventListener('touchmove', preventBackgroundScroll, { passive: false }))
onBeforeUnmount(() => {
  document.removeEventListener('touchmove', preventBackgroundScroll)
  document.removeEventListener('mousemove', mouseMoveListener)
  document.removeEventListener('mouseup', mouseUpListener)
})
</script>

<template>
  <div class="popup-overlay">
    <div class="popup-content">
      <div class="popup-header">
        <span class="title">時間指定</span>
        <button class="close-btn" @click="cancel">×</button>
      </div>

      <!-- 「始発」「最終発」は第1フェーズでは不要となりました。 -->
      <!-- <div class="tabs">
        <button class="tab" :class="{ active: currentTab === 'timeSpecified' }"
          @click="handleTabChange('timeSpecified')">時間指定</button>
        <button class="tab" :class="{ active: currentTab === 'first' }" @click="handleTabChange('first')">始発</button>
        <button class="tab" :class="{ active: currentTab === 'last' }" @click="handleTabChange('last')">最終発</button>
      </div> -->

      <!-- 時間指定 -->
      <div v-if="currentTab === 'timeSpecified'" class="time-picker-container">
        <div class="selected-time-mask"></div>
        <div class="time-picker">
          <div class="picker-column" @wheel.prevent="handleWheel($event, 'hour')">
            <div class="picker-items" :style="{ transform: `translateY(${hourTranslate}px)` }">
              <div v-for="(hour, index) in hourList" :key="`h-${index}`" :class="{ active: index === hourActiveIndex }"
                @touchstart="handleTouchStart($event, 'hour')" @touchmove.prevent="handleTouchMove($event, 'hour')"
                @touchend="handleTouchEnd('hour')" @mousedown.prevent="handleTouchStart($event, 'hour')">
                {{ hour }}
              </div>
            </div>
          </div>
          <div class="separator">:</div>
          <div class="picker-column" @wheel.prevent="handleWheel($event, 'minute')">
            <div class="picker-items" :style="{ transform: `translateY(${minuteTranslate}px)` }">
              <div v-for="(minute, index) in minuteList" :key="`m-${index}`"
                :class="{ active: index === minuteActiveIndex }" @touchstart="handleTouchStart($event, 'minute')"
                @touchmove.prevent="handleTouchMove($event, 'minute')" @touchend="handleTouchEnd('minute')"
                @mousedown.prevent="handleTouchStart($event, 'minute')">
                {{ minute }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 始発 -->
      <div v-else-if="currentTab === 'first'" class="time-picker-container">
        <div class="centerText">始発</div>
      </div>

      <!-- 最終発 -->
      <div v-else-if="currentTab === 'last'" class="time-picker-container">
        <div class="centerText">最終発</div>
      </div>

      <div class="buttons">
        <button class="cancel-btn" @click="cancel">キャンセル</button>
        <button class="confirm-btn" @click="confirm">決定</button>
      </div>
    </div>
  </div>
</template>



<style scoped>
.centerText {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 258px;
  height: 34px;
  background: rgba(204, 204, 204, 0.5);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  pointer-events: none;
  border-radius: 6px;
}

.popup-overlay {
  background: #fff;
  border-radius: 8px;
  width: 300px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.popup-content {
  background: #fff;
  border-radius: 8px;
  width: 100%;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #26499d;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 auto;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.tabs {
  display: flex;
  padding: 10px;
}

.tab {
  flex: 1;
  padding: 6px 0;
  text-align: center;
  border: 1px solid #ccc;
  cursor: pointer;
  background: #fff;
}

.tab.active {
  background: #acd1ed;
  color: #000;
  font-weight: bold;
}

.time-picker-container {
  position: relative;
  padding: 15px 0;
  height: 205px;
}

.selected-time-mask {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 258px;
  height: 34px;
  background: rgba(204, 204, 204, 0.5);
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  pointer-events: none;
  border-radius: 6px;
}

.time-picker {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  z-index: 1;
}

.picker-column {
  width: 80px;
  height: 100%;
  overflow: hidden;
  border-radius: 4px;
  position: relative;
}

.picker-items {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.15s cubic-bezier(0.2, 0.9, 0.3, 1);
}

.picker-items>div {
  box-sizing: border-box;
  width: 100%;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.picker-items>div.active {
  font-weight: 700;
  color: #000;
  font-size: 16px;
  transform: scale(1.05);
}

.picker-items>div:not(.active) {
  opacity: 0.8;
  transform: scale(0.95);
}

.separator {
  width: 20px;
  text-align: center;
  font-size: 20px;
  color: #666;
  font-weight: bold;
}

.buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
  border-top: 1px solid #eee;
}

.cancel-btn,
.confirm-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background: #e7f2fa;
  border: 1px solid #9cbcd4;
  margin-right: 10px;
  color: #26499d;
  width: 131px;
}

.confirm-btn {
  width: 205px;
  background: #26499d;
  color: #fff;
  border: 1px solid #007bff;
}
</style>