// pages/top/composables/useWaypoints.ts
import { ref, computed, type Ref, type ComputedRef } from 'vue'

export interface Waypoint {
  id: number
  location: string
}

export interface WaypointsManager {
  waypoints: Ref<Waypoint[]>
  showWaypoints: Ref<boolean>
  addWaypoint: () => void
  removeWaypoint: (id: number) => void
  toggleWaypoint: () => void
  canAddMoreWaypoints: ComputedRef<boolean>
  waypointButtonText: ComputedRef<string>
  clearWaypoints: () => void
  getValidWaypoints: () => string[]
}

export function useWaypoints(maxWaypoints: number = 3): WaypointsManager {
  const waypoints = ref<Waypoint[]>([])
  const showWaypoints = ref<boolean>(false)

  /* 経由地を追加する（最大数制限あり） */
  const addWaypoint = (): void => {
    if (waypoints.value.length < maxWaypoints) {
      waypoints.value.push({
        id: Date.now() + Math.random(),
        location: ''
      })
    }
  }

  /* 経由地を削除する（削除後空になった場合は表示を非表示に） */
  const removeWaypoint = (id: number): void => {
    const index = waypoints.value.findIndex(w => w.id === id)
    if (index > -1) {
      waypoints.value.splice(index, 1)
    }

    if (waypoints.value.length === 0) {
      showWaypoints.value = false
    }
  }

  /* 経由地表示領域の表示/非表示を切り替え（非表示→表示時は初期経由地を追加） */
  const toggleWaypoint = (): void => {
    if (!showWaypoints.value) {
      showWaypoints.value = true
      if (waypoints.value.length === 0) {
        addWaypoint()
      }
    }
  }

  /* 更に経由地を追加可能か判定する計算プロパティ */
  const canAddMoreWaypoints = computed((): boolean => {
    return waypoints.value.length < maxWaypoints
  })

  /* 経由地ボタンの表示テキストを計算するプロパティ */
  const waypointButtonText = computed((): string => {
    if (showWaypoints.value && waypoints.value.length > 0) {
      return `経由地を追加 (${waypoints.value.length}/${maxWaypoints})`
    }
    return '経由地を選択（任意）'
  })

  /* 経由地データを全てクリアし、表示も非表示にする */
  const clearWaypoints = (): void => {
    waypoints.value = []
    showWaypoints.value = false
  }

  /* 有効な経由地リストを取得（空値・既定テキストを除外） */
  const getValidWaypoints = (): string[] => {
    return waypoints.value
      .map(w => w.location)
      .filter(loc => loc && loc !== '経由地を選択' && loc.trim() !== '')
  }

  return {
    waypoints,
    showWaypoints,
    addWaypoint,
    removeWaypoint,
    toggleWaypoint,
    canAddMoreWaypoints,
    waypointButtonText,
    clearWaypoints,
    getValidWaypoints
  }
}