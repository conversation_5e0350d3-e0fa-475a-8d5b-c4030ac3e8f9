// pages/top/composables/useLocationSwap.ts

export interface Waypoint {
  id: number
  location: string
}

export interface SwapResult {
  departure: string
  destination: string
  waypoints?: Waypoint[]
}

export interface LocationSwap {
  swapLocations: (departure: string, destination: string, waypoints?: Waypoint[]) => SwapResult
  swapSimpleLocations: (departure: string, destination: string) => SwapResult
  swapLocationsWithWaypoints: (
    departure: string,
    destination: string,
    waypoints: Waypoint[]
  ) => SwapResult
}

export function useLocationSwap(): LocationSwap {
  /* 経由地なしの簡単な位置交換 */
  const swapSimpleLocations = (departure: string, destination: string): SwapResult => {
    return {
      departure: destination,
      destination: departure
    }
  }

  /* 経由地付きの位置交換 */
  const swapLocationsWithWaypoints = (
    departure: string,
    destination: string,
    waypoints: Waypoint[]
  ): SwapResult => {
    const allLocations = [departure, ...waypoints.map((w) => w.location), destination]
    allLocations.reverse()

    const newDeparture = allLocations[0]
    const newDestination = allLocations[allLocations.length - 1]
    const newWaypoints = waypoints.map((waypoint, index) => ({
      ...waypoint,
      location: allLocations[index + 1]
    }))

    return {
      departure: newDeparture,
      destination: newDestination,
      waypoints: newWaypoints
    }
  }

  /* 汎用的な位置交換関数 */
  const swapLocations = (
    departure: string,
    destination: string,
    waypoints: Waypoint[] = []
  ): SwapResult => {
    if (waypoints.length > 0) {
      return swapLocationsWithWaypoints(departure, destination, waypoints)
    } else {
      return swapSimpleLocations(departure, destination)
    }
  }

  return {
    swapLocations,
    swapSimpleLocations,
    swapLocationsWithWaypoints
  }
}
