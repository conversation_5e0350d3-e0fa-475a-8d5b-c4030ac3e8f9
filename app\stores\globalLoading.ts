import { defineStore } from 'pinia'

export const useGLStore = defineStore('gl', {
  state: () => ({
    activeRequests: 0,
    overlayDisabled: false,
  }),
  getters: {
    isGlobalLoading: (s) => s.activeRequests > 0,
    isOverlayDisabled: (s) => s.overlayDisabled,
  },
  actions: {
    startLoading() {
      if (!this.overlayDisabled) this.activeRequests++
    },
    endLoading() {
      if (this.activeRequests > 0) this.activeRequests--
    },
    resetLoading() {
      this.activeRequests = 0
    },
    setOverlayDisabled(disabled: boolean) {
      this.overlayDisabled = disabled
    },
  },
})
