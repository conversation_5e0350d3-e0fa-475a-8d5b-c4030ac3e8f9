<script lang="ts" setup>
definePageMeta({
  layout: 'default',
  ssr: false
})

const goToTop = () => {
  navigateTo('/')
}
</script>
<template>
  <v-container class="success-page" fluid>
    <v-row no-gutters class="page-header">
      <v-col cols="12" class="d-flex justify-center align-center">
        <h1 class="page-title">メールアドレス変更完了</h1>
      </v-col>
    </v-row>

    <v-row no-gutters class="main-content">
      <v-col cols="12" class="content-wrapper">
        <div class="success-container">
          <div class="success-icon mb-6">
            <v-icon size="80" color="#4CAF50">mdi-check-circle</v-icon>
          </div>

          <p class="success-message mb-12">
            メールアドレスの変更が正常に完了しました。
            <br />
            新しいメールアドレスでログインしてください。
          </p>

          <v-btn
            class="top-btn"
            color="#26499D"
            variant="flat"
            size="large"
            width="343"
            height="48"
            @click="goToTop"
          >
            TOPへ
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>
<style scoped>
.success-page {
  width: 375px;
  height: 672px;
  background-color: #ffffff;
  margin: 0 auto;
  overflow: hidden;
}

.page-header {
  background-color: #ffffff;
  padding: 12px 16px;
  height: 50px;
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
  text-align: center;
}

.main-content {
  height: calc(672px - 50px);
}

.content-wrapper {
  padding: 16px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

.success-title {
  font-size: 24px;
  font-weight: 500;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
}

.success-message {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #000000;
  margin: 0;
  max-width: 343px;
}

.top-btn {
  border-radius: 4px !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
}

@media (max-width: 375px) {
  .success-page {
    width: 100%;
    max-width: 375px;
  }

  .top-btn {
    width: 100% !important;
    max-width: 343px !important;
  }
}
</style>
