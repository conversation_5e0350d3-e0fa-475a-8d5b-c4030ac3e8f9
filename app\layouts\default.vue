<script lang="ts" setup>
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'
import { computed } from 'vue'
import { useRoute } from '#app'

/* *
 * ルーターの初期化
 * */
const route = useRoute()

/* *
 * フッター表示制御の計算プロパティ
 * */
const hideFooter = computed(() => route.meta.hideFooter)

/* *
 * フッターのプレースホルダー高さの計算プロパティ
 * */
const footerPlaceholderHeight = computed(
  () => route.meta.footerPlaceholderHeight
)

/* *
 * ドロワーメニューのトグル処理
 * */
const toggleDrawer = (): void => {
  console.log('ドロワーメニューをトグルしました')
}

/* *
 * ログインボタンクリック処理
 * */
const handleLogin = (): void => {
  console.log('ログインボタンがクリックされました')
}
</script>

<template>
  <!-- ヘッダーコンポーネント -->
  <Header @toggle-drawer="toggleDrawer" @login="handleLogin" />

  <!-- メインコンテンツエリア -->
  <VMain>
    <slot />
  </VMain>

  <!-- フッター表示制御 -->
  <div v-show="!hideFooter">
    <Footer />
  </div>

  <!-- フッターのプレースホルダー -->
  <div
    v-if="!!footerPlaceholderHeight"
    :style="`height: ${footerPlaceholderHeight}px`"
  ></div>
</template>
