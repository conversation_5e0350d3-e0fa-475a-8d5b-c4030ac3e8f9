<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import {
  onMounted,
  ref,
  computed,
  watchEffect,
  nextTick,
  onUnmounted
} from 'vue'
import {
  useSpecialSpotStore as useSpotStore,
  useSpecialOriginStore as useOriginStore,
  useSpecialWaypointStore as useWaypointStore,
  useSpecialAppStore as useAppStore,
  useTimeStore
} from '~/stores/special-events'
import { useRouteStore } from '~/stores/bus-service-selection'
import OneWayRoundTripRouteSelect from '~/components/OneWayRoundTripRouteSelect.vue'
const routeStore = useRouteStore()
const spotStore = useSpotStore()
const originStore = useOriginStore()
const waypointStore = useWaypointStore()
const appStore = useAppStore()
const timeStore = useTimeStore()

const router = useRouter()
const route = useRoute()
const touristData = ref<any>(null)

/* ** 型別名の定義 ** */
type TripType = 'oneway' | 'roundtrip'
type LocationType = '' | 'prefecture' | 'area' | 'busStop'
type Direction = 'departure' | 'arrival'

/* ** 旅行情報の型定義 ** */
interface TripInfo {
  date: string
  time: string
  direction: Direction
}

/* ** 検索フォームデータの型定義 ** */
interface SearchFormData {
  arrival: any
  tripType: TripType
  departure: string
  departureId: string
  departureType: LocationType
  destination: string
  destinationId: string
  destinationType: LocationType
  waypoints: Array<{
    id: string
    location: string
    locationId: string
    locationType: LocationType
  }>
  date?: string
  time?: string
  direction?: Direction
  outbound?: TripInfo
  return?: TripInfo
}

/* ** 位置データの型定義 ** */
interface LocationData {
  name: string
  id: string | number
  type?: Exclude<LocationType, ''>
  coordinates?: { lat: number; lng: number }
  busStopInfo?: string
  parentId?: string | number
  level?: number
  children?: LocationData[]
  hasChildren?: boolean
}

interface SelectedLocation extends LocationData {
  type: Exclude<LocationType, ''>
}

/* ** 位置タイプ検出クラス ** */
class LocationTypeDetector {
  static detectLocationType(location: LocationData | null): LocationType {
    if (!location) return ''
    if (location.type) return location.type

    if (this.isBusStop(location)) return 'busStop'
    if (this.isArea(location)) return 'area'
    return 'prefecture'
  }

  private static isBusStop(location: LocationData): boolean {
    return !!(
      location.busStopInfo ||
      this.hasStopKeywords(location.name) ||
      (!location.hasChildren && location.level && location.level >= 3)
    )
  }

  private static isArea(location: LocationData): boolean {
    return !!(
      (location.hasChildren && location.parentId) ||
      location.level === 2 ||
      this.hasAreaKeywords(location.name)
    )
  }

  private static hasStopKeywords(name: string): boolean {
    return /駅|站|バス停|停留所|乗り場|前|口/.test(name)
  }

  private static hasAreaKeywords(name: string): boolean {
    return /区|區|町|市|村|郡|地区|地區/.test(name)
  }
}

/* ** フォームデータの初期化 ** */
const formData = ref<SearchFormData>({
  tripType: appStore.savedTripType || 'oneway',
  departure: '',
  departureId: '',
  departureType: '',
  destination: '',
  destinationId: '',
  destinationType: '',
  waypoints: [],
  date: '',
  time: '',
  direction: 'departure',
  arrival: '',
})

/* 旅行タイプ変更処理 */
const tripChange = (newTripType: TripType) => {
  isUpdatingForm = true

  const clearedData: SearchFormData = {
    tripType: newTripType,
    departure: '',
    departureId: '',
    departureType: '',
    destination: '',
    destinationId: '',
    destinationType: '',
    waypoints: [],
    date: '',
    time: '',
    direction: 'departure',
    outbound: { date: '', time: '', direction: 'departure' },
    return: { date: '', time: '', direction: 'departure' },
    arrival: '',
  }

  formData.value = clearedData
  selectedLocations.value = {
    departure: null,
    destination: null,
    waypoints: []
  }

  waypointStore.clearAllWaypoints()
  spotStore.clearCurrentTouristData()
  originStore.clearCurrentOriginData()

  appStore.setSavedTripType(newTripType)

  nextTick(() => {
    isUpdatingForm = false
  })
}

/* 選択位置の状態管理 */
const selectedLocations = ref({
  departure: null as SelectedLocation | null,
  destination: null as SelectedLocation | null,
  waypoints: [] as SelectedLocation[]
})

let isUpdatingForm = false

/* 現在の経由地情報を取得 */
const currentWaypoints = computed(() => waypointStore.getAllWaypoints)

/* 経由地をフォームに同期 */
const syncWaypointsToForm = () => {
  if (isUpdatingForm) return

  const waypoints = currentWaypoints.value
  if (!waypoints || waypoints.length === 0) {
    if (formData.value.waypoints.length === 0) return
    formData.value.waypoints = []
    selectedLocations.value.waypoints = []
    return
  }

  const currentFormWaypoints = formData.value.waypoints
  if (currentFormWaypoints.length === waypoints.length) {
    const needsUpdate = waypoints.some((waypoint, index) => {
      const formWaypoint = currentFormWaypoints[index]
      return (
        !formWaypoint ||
        formWaypoint.locationId !== waypoint.id?.toString() ||
        formWaypoint.location !== waypoint.name
      )
    })
    if (!needsUpdate) return
  }

  const formattedWaypoints = waypoints.map((waypoint) => {
    const detectedType = LocationTypeDetector.detectLocationType(waypoint)
    return {
      id: waypoint.id?.toString() || '',
      location: waypoint.name || '',
      locationId: waypoint.id?.toString() || '',
      locationType: detectedType
    }
  })

  isUpdatingForm = true
  formData.value = { ...formData.value, waypoints: formattedWaypoints }
  updateSelectedWaypoints(waypoints)
  nextTick(() => {
    isUpdatingForm = false
  })
}

/* 選択された経由地の更新 */
const updateSelectedWaypoints = (waypoints: LocationData[]) => {
  selectedLocations.value.waypoints = waypoints.map((waypoint) => {
    const detectedType = LocationTypeDetector.detectLocationType(waypoint)
    return {
      name: waypoint.name || '',
      id: waypoint.id?.toString() || '',
      type: detectedType as Exclude<LocationType, ''>,
      coordinates: waypoint.coordinates || { lat: 0, lng: 0 },
      parentId: waypoint.parentId,
      level: waypoint.level,
      hasChildren: waypoint.hasChildren,
      children: waypoint.children,
      busStopInfo: waypoint.busStopInfo
    }
  })
}

/* 選択されたスポット情報を取得 */
const selectedSpot = computed(() => spotStore.getSelectedSpot)

/* 経由地変更の監視 */
watchEffect(() => {
  if (isUpdatingForm) return

  nextTick(() => {
    syncWaypointsToForm()
  })
})

/* 戻るボタンの処理 */
function handleBack() {
  isUpdatingForm = true

  router.go(-1)

  setTimeout(() => {
    originStore.clearCurrentOriginData()
    spotStore.clearCurrentTouristData()
    waypointStore.clearAllWaypoints()

    Object.assign(formData.value, {
      departure: '',
      departureId: '',
      departureType: '',
      destination: '',
      destinationId: '',
      destinationType: '',
      waypoints: []
    })

    selectedLocations.value = {
      departure: null,
      destination: null,
      waypoints: []
    }
  }, 100)
}

// /* 検索実行処理 */
// const handleSearch = async (searchData: SearchFormData): Promise<void> => {
//   // 打印原始搜索数据
//   console.log('原始搜索数据:', searchData)

//   // 根据旅行类型处理数据存储逻辑
//   const isOneWay = searchData.tripType === 'oneway';

//   // 基础数据（出发地和目的地）
//   const baseData: Partial<RouteList> = {
//     origin: searchData.departure,
//     destination: searchData.destination || '',
//     transferCount: routeStore.updateRouteField.transferCount,
//     transferCondition: routeStore.updateRouteField.transferCondition
//   };

//   // 单程逻辑：存储到date和time，清空返程数据
//   if (isOneWay) {
//     Object.assign(baseData, {
//       date: searchData.date || searchData.outbound?.date || '',
//       time: searchData.time || searchData.outbound?.time || '',
//       recipDate: '',
//       recipTime: ''
//     });
//   }
//   // 往返逻辑：只存储往路数据，清空返程相关字段
//   else {
//     Object.assign(baseData, {
//       // 清空返程相关字段
//       date: '',
//       time: '',
//       // 只存储往路数据
//       recipDate: searchData.outbound?.date || '',
//       recipTime: searchData.outbound?.time || ''
//     });
//   }

//   // 处理方向数据
//   if (searchData.arrival) {
//     baseData.arrival = searchData.arrival;
//   }

//   // 一次性更新所有路由数据
//   routeStore.setRouteData(baseData);

//   // 更新时间存储
//   if (isOneWay) {
//     timeStore.setTimeData(
//       searchData.date || searchData.outbound?.date || '',
//       searchData.time || searchData.outbound?.time || ''
//     );
//   } else {
//     // 往返模式下只保存往路时间
//     timeStore.setTimeData(
//       searchData.outbound?.date || '',
//       searchData.outbound?.time || ''
//     );
//   }

//   if (searchData.direction) {
//     timeStore.setTimeDirection(searchData.direction);
//   }

//   // 存储后打印更新的路由状态
//   console.log('存储后的路由状态:', {
//     origin: routeStore.updateRouteField.origin,
//     destination: routeStore.updateRouteField.destination,
//     date: routeStore.updateRouteField.date,
//     time: routeStore.updateRouteField.time,
//     recipDate: routeStore.updateRouteField.recipDate,
//     recipTime: routeStore.updateRouteField.recipTime,
//     direction: routeStore.updateRouteField.arrival
//   });

//   router.push({
//     path: '/bus-service-selection'
//   });
// }
const handleSearch = async (searchData: SearchFormData): Promise<void> => {
  console.log('原始搜索数据:', searchData);

  // 使用 setFromSearchForm 方法更新所有字段
  const searchFormData = {
    ...searchData,
    date: searchData.date || '',
    time: searchData.time || '',
    direction: searchData.direction || 'departure' as const,
    departureType: searchData.departureType as 'prefecture' | 'area' | 'busStop',
    destinationType: searchData.destinationType as 'prefecture' | 'area' | 'busStop',
    waypoints: searchData.waypoints.map(wp => ({
      ...wp,
      locationType: wp.locationType as 'prefecture' | 'area' | 'busStop'
    })),
    outbound: searchData.outbound || { date: '', time: '', direction: 'departure' as const },
    return: searchData.return || { date: '', time: '', direction: 'departure' as const }
  };
  routeStore.setFromSearchForm(searchFormData);

  const isOneWay = searchData.tripType === 'oneway';

  // 更新时间存储
  const dateToSet = isOneWay
    ? searchData.date || searchData.outbound?.date || ''
    : searchData.outbound?.date || '';
  const timeToSet = isOneWay
    ? searchData.time || searchData.outbound?.time || ''
    : searchData.outbound?.time || '';

  timeStore.setTimeData(dateToSet, timeToSet);

  if (searchData.direction) {
    timeStore.setTimeDirection(searchData.direction);
  }

  // ✅ 正确打印当前 routeList
  console.log('存储后的路由状态:', routeStore.routeSelected.routeList);
  console.log('存储后的完整路由数据:', routeStore.getFullRouteData);

  router.push({
    path: '/bus-service-selection'
  });
};

// const handleSearch = async (searchData: SearchFormData): Promise<void> => {
//   // 打印原始搜索数据
//   console.log('原始搜索数据:', searchData, '66')

//   // 存储前打印当前路由状态
//   console.log('存储前的路由状态:', {
//     origin: routeStore.getFullRouteData.origin,
//     destination: routeStore.getFullRouteData.destination,
//     date: routeStore.getFullRouteData.date,
//     time: routeStore.getFullRouteData.time,
//     recipDate: routeStore.getFullRouteData.recipDate,
//     recipTime: routeStore.getFullRouteData.recipTime,
// direction: routeStore.getFullRouteData.arrival
//   })

//   // 映射并存储搜索数据到路由存储
//   if (searchData.departure) {
//     console.log('更新出发地:', searchData.departure)
//     routeStore.updateRouteField('origin', searchData.departure)
//   }
//   if (searchData.destination) {
//     console.log('更新目的地:', searchData.destination)
//     routeStore.updateRouteField('destination', searchData.destination)
//   }
//   if (searchData.date) {
//     console.log('更新日期:', searchData.date)
//     routeStore.updateRouteField('date', searchData.date)
//   }
//   if (searchData.time) {
//     console.log('更新时间:', searchData.time)
//     routeStore.updateRouteField('time', searchData.time)
//   }
//   if (searchData.arrival) {
//     console.log('direction:', searchData.arrival)
//     routeStore.updateRouteField('time', searchData.arrival)
//   }
//   // 处理返程日期（如果有）
//   if (searchData.return?.date) {
//     console.log('更新返程日期:', searchData.return.date)
//     routeStore.updateRouteField('recipDate', searchData.return.date)
//   }
//   // 处理返程时间（如果有）
//   if (searchData.return?.time) {
//     console.log('更新返程时间:', searchData.return.time)
//     routeStore.updateRouteField('recipTime', searchData.return.time)
//   }

//   // 打印timeStore相关数据
//   console.log('设置时间数据:', {
//     date: searchData.date || '',
//     time: searchData.time || '',
//     direction: searchData.direction
//   })
//   timeStore.setTimeData(searchData.date || '', searchData.time || '')
//   if (searchData.direction) {
//     timeStore.setTimeDirection(searchData.direction)
//   }

//   // 存储后打印更新的路由状态
//   console.log('存储后的路由状态:', {
//     origin: routeStore.getFullRouteData.origin,
//     destination: routeStore.getFullRouteData.destination,
//     date: routeStore.getFullRouteData.date,
//     time: routeStore.getFullRouteData.time,
//     recipDate: routeStore.getFullRouteData.recipDate,
//     recipTime: routeStore.getFullRouteData.recipTime,
// direction: routeStore.getFullRouteData.arrival

//   })

//   // 打印导航信息
//   console.log('导航到:', '/bus-service-selection')
//   router.push({
//     path: '/bus-service-selection'
//   })
// }

/* フォーム変更処理 */
const handleFormChange = (newFormData: SearchFormData) => {
  if (isUpdatingForm) return

  if (
    newFormData.waypoints &&
    newFormData.waypoints.length < selectedLocations.value.waypoints.length
  ) {
    selectedLocations.value.waypoints = selectedLocations.value.waypoints.slice(
      0,
      newFormData.waypoints.length
    )
  }

  if (newFormData.tripType !== formData.value.tripType) {
    formData.value.tripType = newFormData.tripType
  }

  if (newFormData.date !== formData.value.date) {
    formData.value.date = newFormData.date
  }

  if (newFormData.time !== formData.value.time) {
    formData.value.time = newFormData.time
  }
}

/* 経由地選択ページへ遷移 */
const handleWaypoint = () => {
  router.push({
    path: '/transit-point',
    query: {
      mode: 'waypoint'
    }
  })
}

/* 出発地選択ページへ遷移 */
const handleDeparture = () => {
  router.push({
    path: '/target-location',
    query: {
      mode: 'departure'
    }
  })
}

/* 目的地選択ページへ遷移 */
const handleDestinationMapClick = () => {
  router.push({
    path: '/origin-location',
    query: {
      mode: 'destination'
    }
  })
}

/* 位置データの更新処理 */
const updateLocationData = (
  location: LocationData | null,
  isDestination = false
) => {
  if (isUpdatingForm) return

  if (!location || !location.name) {
    if (isDestination) {
      Object.assign(formData.value, {
        destination: '',
        destinationId: '',
        destinationType: ''
      })
    } else {
      Object.assign(formData.value, {
        departure: '',
        departureId: '',
        departureType: ''
      })
      selectedLocations.value.departure = null
    }
    return
  }

  const detectedType = LocationTypeDetector.detectLocationType(location)

  if (isDestination) {
    Object.assign(formData.value, {
      destination: location.name,
      destinationId: location.id?.toString() || '',
      destinationType: detectedType
    })
  } else {
    Object.assign(formData.value, {
      departure: location.name,
      departureId: location.id?.toString() || '',
      departureType: detectedType
    })

    if (detectedType) {
      selectedLocations.value.departure = {
        name: location.name,
        id: location.id?.toString() || '',
        type: detectedType as 'prefecture' | 'area' | 'busStop',
        coordinates: location.coordinates || { lat: 0, lng: 0 },
        parentId: location.parentId,
        level: location.level,
        hasChildren: location.hasChildren,
        children: location.children,
        busStopInfo: location.busStopInfo
      }
    }
  }
}

/* 旅行タイプ変更の監視 */
watch(
  () => formData.value.tripType,
  (newValue) => {
    appStore.setSavedTripType(newValue)
  },
  { immediate: true }
)

/* スポット選択の監視 */
watchEffect(() => {
  const spot = selectedSpot.value
  updateLocationData(spot, true)
})

/* 出発地選択の監視 */
watchEffect(() => {
  const originData = originStore.currentOriginData
  updateLocationData(originData, false)
})

/* コンポーネントマウント時の初期化 */
onMounted(() => {
  if (route.query.touristData) {
    try {
      touristData.value = JSON.parse(route.query.touristData as string)
    } catch (error) {
      console.error('touristDataの解析に失敗しました:', error)
    }
  }

  nextTick(() => {
    syncWaypointsToForm()
  })
})

/* コンポーネントアンマウント時の処理 */
onUnmounted(() => {
  isUpdatingForm = true
})
</script>

<template>
  <div class="pageContainer">
    <div class="regionpadding">
      <BaseHeader title="検索条件再設定" :showBack="true" :showRightIcon="false" />
    </div>

    <div class="destinationBox">
      <div class="round">
        <OneWayRoundTripRouteSelect :departure-config="{
          value: formData.departure,
          readonly: true,
          showMapButton: false
        }" :destination-config="{
          value: formData.destination,
          readonly: true,
          showMapButton: false
        }" :waypoint-config="{
            readonly: true,
            showMapButton: false
          }" :initial-form-data="formData" @search="handleSearch" @form-change="handleFormChange"
          @departure-click="handleDeparture" @destination-click="handleDestinationMapClick"
          @waypoint-click="handleWaypoint" :initialTripType="formData.tripType" @trip-type-change="tripChange" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pageContainer {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.round {
  padding: 20px;
  background-color: #fff;
  margin: 20px 20px 0 20px;
  border-radius: 10px;
}

.destination {
  font-size: 14px;
  padding: 16px 20px 0 20px;
}

.destinationBox {
  background-color: #e7f2fa;
  padding-bottom: 20px;
  flex-grow: 1;
}
</style>
