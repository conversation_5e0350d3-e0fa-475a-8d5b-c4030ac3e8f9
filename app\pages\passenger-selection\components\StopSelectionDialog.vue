<script lang="ts" setup>
import { ref, computed } from 'vue'
import StationDetailDialog from '~/components/StationDetailDialog.vue'

interface Stop {
  time: string
  name: string
  id?: string
}

interface Props {
  modelValue: boolean
  selectedValue?: string | null
  title?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'stop-selected', stop: Stop): void
  (e: 'map-select'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  selectedValue: null,
  title: '乗り場一覧'
})

const emit = defineEmits<Emits>()

const dialogOpen = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const showStationDetail = ref(false)
const selectedStationName = ref('')

/* 停留所データ */
const stopsList = ref<Stop[]>([
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop1'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop2'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop3'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop4'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop5'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop6'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop7'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop8'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop9'
  },
  {
    time: '00:00',
    name: 'バス停あああああああああああああああああああ',
    id: 'stop10'
  }
])

const selectStop = (stop: Stop) => {
  emit('stop-selected', stop)
  closeDialog()
}

const selectFromMap = () => {
  emit('map-select')
}

const closeDialog = () => {
  emit('update:modelValue', false)
}

/* 停留所詳細ダイアログを開く */
const openStationDetail = (stop: Stop) => {
  selectedStationName.value = stop.name
  showStationDetail.value = true
}

const closeStationDetail = () => {
  showStationDetail.value = false
}

const closeAllDialogs = () => {
  showStationDetail.value = false
  closeDialog()
}
</script>

<template>
  <v-dialog
    v-model="dialogOpen"
    max-width="375"
    persistent
    class="stop-selection-dialog"
  >
    <v-card class="dialog-card">
      <div class="dialog-header">
        <div class="header-title">{{ title }}</div>
        <v-btn
          variant="outlined"
          size="small"
          class="map-select-button"
          rounded="4"
          @click="selectFromMap"
        >
          地図から選ぶ
        </v-btn>
      </div>

      <div class="stops-list-container">
        <div v-for="(stop, index) in stopsList" :key="index" class="stop-item">
          <div class="stop-time">{{ stop.time }}</div>
          <div class="stop-name" @click="openStationDetail(stop)">
            {{ stop.name }}
          </div>
          <v-btn
            variant="elevated"
            size="small"
            class="select-button"
            @click="selectStop(stop)"
          >
            選ぶ
          </v-btn>
        </div>
      </div>

      <div class="dialog-footer">
        <v-btn
          variant="outlined"
          class="cancel-button"
          rounded="4"
          block
          @click="closeDialog"
        >
          キャンセル
        </v-btn>
      </div>
    </v-card>
  </v-dialog>

  <StationDetailDialog
    v-model="showStationDetail"
    :station-name="selectedStationName"
    @go-back="closeStationDetail"
    @close-all="closeAllDialogs"
  />
</template>

<style scoped>
.stop-selection-dialog {
  z-index: 1000;
}
.dialog-card {
  border-radius: 10px !important;
  overflow: hidden;
  width: 343px;
  height: 580px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 16px 0 16px;
  position: relative;
}

.header-title {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.2;
  color: #000000;
  white-space: nowrap;
}

.map-select-button {
  height: 32px !important;
  padding: 6px 16px !important;
  border: 1px solid #9cbcd4 !important;
  background-color: #ffffff !important;
  line-height: 1.2 !important;
  color: #000000 !important;
}

.map-select-button:hover {
  background-color: #f8f9fa !important;
}

/* 停留所リストコンテナ */
.stops-list-container {
  flex: 1;
  padding: 24px 16px 0 16px;
  overflow-y: auto;
  height: 412px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 停留所アイテム */
.stop-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  width: 311px;
  border-bottom: 1px solid #dfdfdf;
  background-color: #ffffff;
}

.stop-item:last-child {
  border-bottom: none;
}

.stop-time {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2;
  color: #000000;
  min-width: 40px;
}

.stop-name {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.2;
  color: #26499d;
  width: 154px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: underline;
}

.select-button {
  padding: 6px 16px !important;
  background-color: #26499d !important;
  color: #ffffff !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
}

.select-button:hover {
  background-color: #1e3a8a !important;
}

/* フッター */
.dialog-footer {
  padding: 24px 16px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.cancel-button {
  height: 48px !important;
  margin: 0 auto !important;
  padding: 14px 13.38px !important;
  border: 1px solid #9cbcd4 !important;
  background-color: #e7f2fa !important;
  color: #26499d !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
}

.cancel-button:hover {
  background-color: #d4ebf7 !important;
}

/* スクロールバーカスタマイズ */
.stops-list-container::-webkit-scrollbar {
  width: 4px;
}

.stops-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.stops-list-container::-webkit-scrollbar-thumb {
  background: #9cbcd4;
  border-radius: 2px;
}

.stops-list-container::-webkit-scrollbar-thumb:hover {
  background: #7ba1c7;
}

/* Vuetifyのデフォルトスタイルをオーバーライド */
:deep(.v-dialog > .v-overlay__content) {
  margin: 0 !important;
}

:deep(.v-card) {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

:deep(.v-btn) {
  text-transform: none !important;
}

:deep(.v-btn--variant-outlined) {
  border-width: 1px !important;
}

:deep(.v-btn--variant-contained) {
  box-shadow: none !important;
}
</style>
