export interface EventsItem {
  spot_id: number
  name: string
  category: string
  prefecture: string
  address: string
  nearest_bus_stop_id: number
  description: string
  map_info: string
  time: string
  image: string
  latitude: number
  longitude: number
  arr: Array<{
    Guangdi: string
    Tag: string
  }>
}

export interface TabItem {
  name: string
  id: number
}

export interface EventsSpot {
  id: number
  spot_id: number
  name: string
  category: string
  prefecture: string
  address: string
  nearest_bus_stop_id: number
  description: string
  homepage: string
  reminder: string
  venue: string
  images: string[]
}
