<script lang="ts" setup>
import { ref } from 'vue'

interface FilterOptions {
  timeType: string[]
  seatType: string[]
  amenities: string[]
  priceRange: [number, number]
}

interface Emits {
  (e: 'apply-filter', filters: FilterOptions): void
  (e: 'close'): void
}

const emit = defineEmits<Emits>()

const filters = ref<FilterOptions>({
  timeType: [],
  seatType: [],
  amenities: [],
  priceRange: [0, 50000]
})

const timeTypeOptions = [
  { label: '昼行便', value: 'day' },
  { label: '夜行便', value: 'night' }
]

const seatTypeOptions = [
  { label: '3列シート', value: '3row' },
  { label: '4列シート', value: '4row' }
]

const amenityOptions = [
  { label: 'Wi-Fi', value: 'wifi' },
  { label: 'トイレ付', value: 'toilet' },
  { label: '座席指定', value: 'seat_selection' },
  { label: '女性安心', value: 'women_safe' },
  { label: 'コンセント', value: 'power' },
  { label: 'ブランケット', value: 'blanket' }
]

const handleApplyFilter = () => {
  emit('apply-filter', filters.value)
}

const handleClose = () => {
  emit('close')
}

const resetFilters = () => {
  filters.value = {
    timeType: [],
    seatType: [],
    amenities: [],
    priceRange: [0, 50000]
  }
}
</script>

<template>
  <v-card class="filter-dialog" max-width="400">
    <v-card-title class="filter-title">絞り込み条件</v-card-title>

    <v-card-text class="filter-content">
      <!-- 時間帯 -->
      <div class="filter-section">
        <h4 class="filter-section-title">時間帯</h4>
        <v-checkbox-btn
          v-for="option in timeTypeOptions"
          :key="option.value"
          v-model="filters.timeType"
          :value="option.value"
          :label="option.label"
          class="filter-checkbox"
        />
      </div>

      <!-- シートタイプ -->
      <div class="filter-section">
        <h4 class="filter-section-title">シートタイプ</h4>
        <v-checkbox-btn
          v-for="option in seatTypeOptions"
          :key="option.value"
          v-model="filters.seatType"
          :value="option.value"
          :label="option.label"
          class="filter-checkbox"
        />
      </div>

      <!-- 設備 -->
      <div class="filter-section">
        <h4 class="filter-section-title">設備</h4>
        <v-checkbox-btn
          v-for="option in amenityOptions"
          :key="option.value"
          v-model="filters.amenities"
          :value="option.value"
          :label="option.label"
          class="filter-checkbox"
        />
      </div>

      <!-- 料金範囲 -->
      <div class="filter-section">
        <h4 class="filter-section-title">料金範囲</h4>
        <v-range-slider
          v-model="filters.priceRange"
          :min="0"
          :max="50000"
          :step="1000"
          color="#26499D"
          class="price-slider"
        />
        <div class="price-range-display">
          <span>{{ filters.priceRange[0].toLocaleString() }}円</span>
          <span>〜</span>
          <span>{{ filters.priceRange[1].toLocaleString() }}円</span>
        </div>
      </div>
    </v-card-text>

    <v-card-actions class="filter-actions">
      <v-btn variant="outlined" class="reset-btn" @click="resetFilters">
        リセット
      </v-btn>
      <v-btn variant="outlined" class="cancel-btn" @click="handleClose">
        キャンセル
      </v-btn>
      <v-btn class="apply-btn" @click="handleApplyFilter">適用</v-btn>
    </v-card-actions>
  </v-card>
</template>

<style scoped>
.filter-dialog {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #26499d !important;
  text-align: center !important;
  padding: 20px 16px 8px !important;
}

.filter-content {
  padding: 0 16px !important;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 12px;
}

.filter-checkbox {
  margin-bottom: 8px;
}

.price-slider {
  margin: 16px 0;
}

.price-range-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #26499d;
  font-weight: 500;
}

.filter-actions {
  padding: 16px !important;
  gap: 8px !important;
  justify-content: center !important;
}

.reset-btn,
.cancel-btn {
  border: 1px solid #9cbcd4 !important;
  color: #26499d !important;
  background-color: #ffffff !important;
  text-transform: none !important;
  font-weight: 500 !important;
  min-width: 80px !important;
}

.apply-btn {
  background-color: #26499d !important;
  color: #ffffff !important;
  text-transform: none !important;
  font-weight: 500 !important;
  min-width: 80px !important;
}

:deep(.v-checkbox-btn .v-selection-control__input) {
  color: #26499d !important;
}

:deep(.v-slider-thumb) {
  background-color: #26499d !important;
}

:deep(.v-slider-track-fill) {
  background-color: #26499d !important;
}
</style>
