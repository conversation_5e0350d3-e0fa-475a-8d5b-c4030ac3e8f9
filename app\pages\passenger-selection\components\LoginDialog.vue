<script lang="ts" setup>
import { computed } from 'vue'

// プロパティ定義
interface Props {
  modelValue: boolean
}

// イベント定義
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'login'): void
  (e: 'register'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ダイアログの開閉状態を管理
const isOpen = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// ログインボタンクリック処理
const handleLogin = () => {
  emit('login')
  isOpen.value = false
}

// 新規会員登録ボタンクリック処理
const handleRegister = () => {
  emit('register')
  isOpen.value = false
}
</script>
<template>
  <v-dialog
    v-model="isOpen"
    max-width="343"
    class="login-dialog"
    content-class="login-dialog-content"
  >
    <v-card class="login-card">
      <div class="login-content">
        <div class="login-title">ログイン／新規会員登録</div>

        <div class="login-description-frame">
          <div class="login-description">
            予約を進めるためにはログインまたは新規会員登録が必要です。
          </div>
        </div>

        <div class="login-buttons-frame">
          <div class="login-buttons">
            <v-btn class="login-btn primary-btn" @click="handleLogin">
              ログイン
            </v-btn>

            <v-btn class="register-btn secondary-btn" @click="handleRegister">
              新規会員登録
            </v-btn>
          </div>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>
<style scoped>
.login-dialog :deep(.v-overlay__content) {
  margin: 0;
}

.login-card {
  border-radius: 10px;
  width: 343px;
  height: 320px;
  background-color: #ffffff;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  height: 100%;
}

.login-title {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.2;
  color: #000000;
  text-align: left;
  width: 311px;
}

.login-description-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 311px;
  height: 88px;
}

.login-description {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.571;
  color: #000000;
  text-align: left;
  width: 100%;
}

.login-buttons-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 311px;
  position: absolute;
  bottom: 24px;
  left: 16px;
}

.login-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding-top: 24px;
  width: 311px;
}

.login-btn,
.register-btn {
  width: 311px !important;
  height: 48px !important;
  border-radius: 4px !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  text-transform: none !important;
  padding: 14px 13.38px !important;
}

.primary-btn {
  background-color: #ed785f !important;
  color: #ffffff !important;
}

.primary-btn:hover {
  background-color: #e66b50 !important;
}

.secondary-btn {
  background-color: #26499d !important;
  color: #ffffff !important;
}

.secondary-btn:hover {
  background-color: #1e3a7a !important;
}
</style>
