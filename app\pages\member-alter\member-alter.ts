import type {
  ValidationRule,
  ValidationResult,
  UserFormData,
  FieldRules,
  ValidationCheckRule,
  ValidationCheckResult
} from '~/types/member-alter'

export const MEMBER_ALTER_REGEX_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phoneNumber: /^\d{10,11}$/,
  postalCode: /^\d{3}-?\d{4}$/,
  kana: /^[ァ-ヶー\s]+$/,
  password: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  onlyNumbers: /^\d+$/,
  japaneseText: /^[a-zA-Z0-9\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\s\-－]+$/,
  address:
    /^(?=.*[0-9市区町村番地号楼、。・ー-])[\u3040-\u309F\u4E00-\u9FFF0-9a-zA-Z\s。、・ー-]{1,200}$/
    
}

export const createMemberAlterRule = {
  required: (message = 'この項目は必須です'): ValidationRule => ({
    required: true,
    message
  }),

  minLength: (min: number, message?: string): ValidationRule => ({
    minLength: min,
    message: message || `${min}文字以上で入力してください`
  }),

  maxLength: (max: number, message?: string): ValidationRule => ({
    maxLength: max,
    message: message || `${max}文字以内で入力してください`
  }),

  pattern: (pattern: RegExp, message: string): ValidationRule => ({
    pattern,
    message
  }),

  custom: (
    validator: (value: string, formData?: UserFormData) => boolean,
    message: string
  ): ValidationRule => ({
    custom: validator,
    message
  }),

  requiredText: (fieldName: string, maxLength = 50): ValidationRule[] => [
    createMemberAlterRule.required(`${fieldName} を`),
    createMemberAlterRule.maxLength(maxLength)
  ],

  requiredKana: (fieldName: string): ValidationRule[] => [
    createMemberAlterRule.required(`${fieldName} を`),
    createMemberAlterRule.pattern(
      MEMBER_ALTER_REGEX_PATTERNS.kana,
      '正しいカタカナで入力してください'
    ),
    createMemberAlterRule.maxLength(30)
  ]
}

export const MEMBER_ALTER_VALIDATION_RULES: FieldRules = {
  lastName: createMemberAlterRule.requiredText('お名前 (姓)', 20),
  firstName: createMemberAlterRule.requiredText('お名前 (名)', 20),
  lastNameKana: createMemberAlterRule.requiredKana('フリガナ（姓）'),
  firstNameKana: createMemberAlterRule.requiredKana('フリガナ（名）'),

  birthYear: [
    createMemberAlterRule.required('生年を入力してください'),
    createMemberAlterRule.pattern(
      MEMBER_ALTER_REGEX_PATTERNS.onlyNumbers,
      '数字で入力してください'
    ),
    createMemberAlterRule.custom((value: string) => {
      const year = parseInt(value)
      const currentYear = new Date().getFullYear()
      return year >= 1900 && year <= currentYear
    }, '正しい年を入力してください')
  ],

  birthMonth: [
    createMemberAlterRule.required('生月を選択してください'),
    createMemberAlterRule.custom((value: string) => {
      const month = parseInt(value)
      return month >= 1 && month <= 12
    }, '正しい月を選択してください')
  ],

  birthDay: [
    createMemberAlterRule.required('生日を選択してください'),
    createMemberAlterRule.custom((value: string) => {
      const day = parseInt(value)
      return day >= 1 && day <= 31
    }, '正しい日を選択してください')
  ],

  gender: [createMemberAlterRule.required('性別を選択してください')],

  phoneNumber: [
    createMemberAlterRule.required('電話番号は数字のみで入力してください'),
    createMemberAlterRule.pattern(
      MEMBER_ALTER_REGEX_PATTERNS.phoneNumber,
      '正しい電話番号を入力してください（10-11桁の数字）'
    )
  ],

  emailAddress: [
    createMemberAlterRule.required('有効なメールアドレスを入力してください'),
    createMemberAlterRule.pattern(
      MEMBER_ALTER_REGEX_PATTERNS.email,
      '正しいメールアドレス形式で入力してください'
    )
  ],

  confirmEmailAddress: [
    createMemberAlterRule.required('有効なメールアドレスを入力してください'),
    createMemberAlterRule.pattern(
      MEMBER_ALTER_REGEX_PATTERNS.email,
      '正しいメールアドレス形式で入力してください'
    ),
    createMemberAlterRule.custom((value: string, formData?: UserFormData) => {
      return formData ? value === formData.emailAddress : true
    }, 'メールアドレスが一致しません')
  ],

  residence: [
    createMemberAlterRule.required('都道府県を選択してください'),
    createMemberAlterRule.maxLength(100)
  ],

  cityAddressDetail: [
    createMemberAlterRule.required('正しい住所情報を入力してください'),
    createMemberAlterRule.maxLength(200),
    createMemberAlterRule.pattern(
      MEMBER_ALTER_REGEX_PATTERNS.address,
      '正しい住所情報を入力してください'
    )
  ],

  occupation: [createMemberAlterRule.required('職業を選択してください')],

  password: [
    createMemberAlterRule.required('パスワードは必須項目です'),
    createMemberAlterRule.minLength(
      8,
      'パスワードは8文字以上で入力してください'
    ),
    createMemberAlterRule.pattern(
      MEMBER_ALTER_REGEX_PATTERNS.password,
      'パスワードは英数字を含む8文字以上で入力してください'
    )
  ],

  confirmPassword: [
    createMemberAlterRule.required('パスワード（確認）は必須項目です'),
    createMemberAlterRule.custom((value: string, formData?: UserFormData) => {
      return formData ? value === formData.password : true
    }, 'パスワードが一致しません')
  ],

  agreeToTerms: [
    createMemberAlterRule.custom(
      (value: string) => value === 'true',
      '利用規約に同意してください'
    )
  ]
}

/* ** 小関数：必須チェック **/
const checkRequiredMemberAlter = (
  value: string,
  rule: ValidationRule
): string | null => {
  if (rule.required && (!value || value.trim() === '' || value === 'false')) {
    return rule.message
  }
  return null
}

/* ** 小関数：文字長チェック **/
const checkLengthMemberAlter = (
  value: string,
  rule: ValidationRule
): string | null => {
  if (rule.minLength && value.length < rule.minLength) return rule.message
  if (rule.maxLength && value.length > rule.maxLength) return rule.message
  return null
}

/* ** 小関数：パターンチェック（正規表現） **/
const checkPatternMemberAlter = (
  value: string,
  rule: ValidationRule
): string | null => {
  if (rule.pattern && !rule.pattern.test(value)) return rule.message
  return null
}

/* ** 小関数：カスタムチェック **/
const checkCustomMemberAlter = (
  value: string,
  rule: ValidationRule,
  formData?: UserFormData
): string | null => {
  if (rule.custom && !rule.custom(value, formData)) return rule.message
  return null
}

/* ** 単一フィールド検証関数（会員情報変更用） **/
export const validateMemberAlterField = (
  value: string | boolean,
  rules: ValidationRule[],
  formData?: UserFormData
): ValidationResult => {
  const errors: string[] = []
  const stringValue = String(value)

  for (const rule of rules) {
    /* ** 必須チェック **/
    const requiredError = checkRequiredMemberAlter(stringValue, rule)
    if (requiredError) {
      errors.push(requiredError)
      continue
    }

    /* ** 空値の場合は他のチェックをスキップ **/
    if (!stringValue.trim()) continue

    /* ** 長さ・パターン・カスタムチェック **/
    const lengthError = checkLengthMemberAlter(stringValue, rule)
    if (lengthError) errors.push(lengthError)

    const patternError = checkPatternMemberAlter(stringValue, rule)
    if (patternError) errors.push(patternError)

    const customError = checkCustomMemberAlter(stringValue, rule, formData)
    if (customError) errors.push(customError)
  }

  /* ** エラー有無を返す **/
  return { isValid: errors.length === 0, errors }
}

export const validateMemberAlterForm = (
  formData: UserFormData
): { [key: string]: ValidationResult } => {
  const results: { [key: string]: ValidationResult } = {}

  Object.entries(MEMBER_ALTER_VALIDATION_RULES).forEach(([key, rules]) => {
    const fieldKey = key as keyof UserFormData
    const value = formData[fieldKey]
    results[key] = validateMemberAlterField(value ?? '', rules, formData)
  })

  return results
}

export const isMemberAlterFormValid = (validationResults: {
  [key: string]: ValidationResult
}): boolean => {
  return Object.values(validationResults).every((result) => result.isValid)
}

export const getMemberAlterAllErrors = (validationResults: {
  [key: string]: ValidationResult
}): string[] => {
  return Object.values(validationResults).flatMap((result) => result.errors)
}

export const getMemberAlterFirstError = (
  validationResult: ValidationResult
): string => {
  return validationResult.errors[0] || ''
}

export const hasMemberAlterError = (
  validationResult: ValidationResult
): boolean => {
  return !validationResult.isValid
}

export class MemberAlterValidationUtils {
  static readonly verificationCodeRules: ValidationCheckRule[] = [
    {
      required: true,
      message: '認証コードを入力してください'
    },
    {
      minLength: 6,
      maxLength: 6,
      message: '認証コードは6桁で入力してください'
    },
    {
      pattern: /^\d{6}$/,
      message: '認証コードは数字のみで入力してください'
    }
  ]

  static validateSingleRule(
    value: string,
    rule: ValidationCheckRule
  ): ValidationCheckResult {
    if (rule.required && !value.trim()) {
      return { isValid: false, message: rule.message }
    }

    if (rule.minLength && value.length < rule.minLength) {
      return { isValid: false, message: rule.message }
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      return { isValid: false, message: rule.message }
    }

    if (rule.pattern && !rule.pattern.test(value)) {
      return { isValid: false, message: rule.message }
    }

    return { isValid: true, message: '' }
  }

  static validateField(
    value: string,
    rules: ValidationCheckRule[]
  ): ValidationCheckResult {
    for (const rule of rules) {
      const result = this.validateSingleRule(value, rule)
      if (!result.isValid) {
        return result
      }
    }
    return { isValid: true, message: '' }
  }

  static validateVerificationCode(code: string): ValidationCheckResult {
    return this.validateField(code, this.verificationCodeRules)
  }

  static createCountdown(initialSeconds: number = 30) {
    return {
      seconds: initialSeconds,
      isActive: false,
      canResend: true,

      start(callback?: (seconds: number) => void) {
        if (this.isActive) return

        this.isActive = true
        this.canResend = false

        const timer = setInterval(() => {
          this.seconds--
          callback?.(this.seconds)

          if (this.seconds <= 0) {
            clearInterval(timer)
            this.reset()
          }
        }, 1000)

        return timer
      },

      reset() {
        this.seconds = initialSeconds
        this.isActive = false
        this.canResend = true
      }
    }
  }
}
