<script lang="ts" setup>
interface Props {
  modelValue: boolean
  reservationNumber?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ダイアログの表示状態を管理
const isVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 閉じるボタンのクリック処理
const handleClose = () => {
  emit('close')
  isVisible.value = false
}
</script>
<template>
  <v-dialog v-model="isVisible" max-width="343" persistent>
    <v-card class="cancel-completion-dialog">
      <div class="dialog-content">
        <!-- タイトル -->
        <h2 class="dialog-title">便をキャンセル完了</h2>

        <!-- 情報セクション -->
        <div class="info-section">
          <div class="reservation-number">
            予約番号：　{{ reservationNumber || 'XXXXXXXXX' }}
          </div>
          <div class="completion-message">
            便をキャンセル完了しました。
            <br />
            予約情報はマイページの予約履歴からご確認いただけます。
          </div>
        </div>

        <!-- ボタンセクション -->
        <div class="button-section">
          <v-btn
            class="close-btn"
            variant="outlined"
            size="large"
            block
            @click="handleClose"
          >
            閉じる
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>
<style scoped>
.cancel-completion-dialog {
  border-radius: 10px !important;
  background: #ffffff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 320px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  text-align: left;
  margin: 0;
  width: 311px;
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 311px;
}

.reservation-number {
  font-size: 16px;
  line-height: 1.5625;
  color: #000000;
  text-align: left;
  width: 311px;
}

.completion-message {
  font-size: 14px;
  line-height: 1.5714285714285714;
  color: #000000;
  text-align: left;
  width: 311px;
}

/* ボタンセクション */
.button-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  gap: 24px;
  padding: 24px 0 0;
  position: absolute;
  bottom: 24px;
  left: 16px;
  width: 311px;
}

.close-btn {
  width: 311px !important;
  height: 48px !important;
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  color: #26499d !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  border-radius: 4px !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.close-btn:hover {
  background-color: #d6e9f5 !important;
}
</style>
