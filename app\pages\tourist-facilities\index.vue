<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'

/* インターフェース定義 - データ構造の明確化 */
interface TagItem {
  Tourist: string
  Tag: string
}

interface TouristSpot {
  spot_id: number
  name: string
  category: string
  prefecture: string
  address: string
  nearest_bus_stop_id: number
  description: string
  map_info: string
  latitude: number
  longitude: number
  image: string
  arr: TagItem[]
}

interface TabItem {
  name: string
  id: number
}

/* ルーターとリアクティブデータの初期化 */
const router = useRouter()
const keyword = ref('')
const selectedRegion = ref('')
const activeTab = ref('すべて')

/* タブ設定 */
const tabs: TabItem[] = [
  { name: 'すべて', id: 1 },
  { name: '観光地', id: 2 },
  { name: '自然・風景', id: 3 },
  { name: 'レジャー', id: 4 },
  { name: 'その他', id: 6 }
]

/* 地域リスト */
const regions = ['北海道', '東北', '関東', '中部', '関西', '九州']

/* 観光スポットデータ */
const items = ref<TouristSpot[]>([
  {
    spot_id: 1,
    name: '東京タワー',
    category: '観光地',
    prefecture: '東京都',
    address: '東京都港区芝公園4丁目2-8',
    nearest_bus_stop_id: 101,
    description: '美しい景観を楽しめます。',
    map_info: '東京都心部に位置し、周辺に多くの観光スポットがあります。',
    latitude: 35.6586,
    longitude: 139.7454,
    image: 'https://picsum.photos/400/300?random=2',
    arr: [
      {
        Tourist: '観光地',
        Tag: 'タグ'
      }
    ]
  },
  {
    spot_id: 2,
    name: '富士山五合目',
    category: '自然・風景',
    prefecture: '静岡県',
    address: '静岡県富士宮市須走町',
    nearest_bus_stop_id: 102,
    description:
      '日本最高峰である富士山の中腹に位置し、車で到達可能な人気スポット。季節によって異なる風景が楽しめ、登山の起点としても知られています。',
    map_info: '富士山北麓に位置し、富士五湖方面からも眺望できます。',
    latitude: 35.3606,
    longitude: 138.7274,
    image: 'https://picsum.photos/400/300?random=3',
    arr: [
      {
        Tourist: '観光地',
        Tag: '自然'
      }
    ]
  },
  {
    spot_id: 3,
    name: '清水寺',
    category: '寺院・神社',
    prefecture: '京都府',
    address: '京都府京都市東山区清水1-294',
    nearest_bus_stop_id: 103,
    description:
      '京都の代表的な寺院で、赤い楼門や舞台が有名です。春は桜、秋は紅葉が美しく、多くの観光客が訪れます。',
    map_info:
      '京都市東山区に位置し、周辺には清水坂、二年坂などの観光名所が集中しています。',
    latitude: 34.9949,
    longitude: 135.7851,
    image: 'https://picsum.photos/400/300?random=4',
    arr: [
      {
        Tourist: '観光地',
        Tag: '寺院'
      }
    ]
  },
  {
    spot_id: 4,
    name: '北海道大学 旭山動物園',
    category: '動物園・水族館',
    prefecture: '北海道',
    address: '北海道旭川市東旭川町倉沼',
    nearest_bus_stop_id: 104,
    description:
      '北の大地に広がる動物園で、ペンギンのパレードが人気です。冬は雪景色の中で動物たちを観察できる貴重な体験ができます。',
    map_info: '旭川市東部に位置し、市内からバスで約30分で到達できます。',
    latitude: 43.7705,
    longitude: 142.3679,
    image: 'https://picsum.photos/400/300?random=5',
    arr: [
      {
        Tourist: '観光地',
        Tag: '動物'
      }
    ]
  },
  {
    spot_id: 5,
    name: '姫路城',
    category: '歴史・遺跡',
    prefecture: '兵庫県',
    address: '兵庫県姫路市本町68',
    nearest_bus_stop_id: 105,
    description:
      '白鷺城とも呼ばれる日本を代表する城郭で、世界文化遺産に登録されています。美しい白い外観と複雑な構造が特徴です。',
    map_info: '姫路市中心部に位置し、JR姫路駅から徒歩で約30分で到達できます。',
    latitude: 34.8394,
    longitude: 134.6939,
    image: 'https://picsum.photos/400/300?random=6',
    arr: [
      {
        Tourist: '観光地',
        Tag: '歴史'
      }
    ]
  },
  {
    spot_id: 6,
    name: '沖縄美ら海水族館',
    category: '動物園・水族館',
    prefecture: '沖縄県',
    address: '沖縄県国頭郡本部町石川424',
    nearest_bus_stop_id: 106,
    description:
      '世界最大級のエサコミアクアリウムを擁する水族館で、ジンベエザメや多くの熱帯魚を観察できます。沖縄の美しい海を体感できます。',
    map_info: '沖縄本島北部に位置し、海洋博公園内にあります。',
    latitude: 26.685,
    longitude: 127.9247,
    image: 'https://picsum.photos/400/300?random=7',
    arr: [
      {
        Tourist: '観光地',
        Tag: '水族館'
      }
    ]
  },
  {
    spot_id: 7,
    name: '函館山夜景',
    category: '夜景・眺め',
    prefecture: '北海道',
    address: '北海道函館市元町',
    nearest_bus_stop_id: 107,
    description:
      '世界三大夜景の一つとして知られ、360度のパノラマビューが楽しめます。特に冬は雪とライトが美しく調和します。',
    map_info:
      '函館市街地を一望できる場所で、索道で山頂まで登ることができます。',
    latitude: 41.7711,
    longitude: 140.7265,
    image: 'https://picsum.photos/400/300?random=8',
    arr: [
      {
        Tourist: '観光地',
        Tag: '夜景'
      }
    ]
  },
  {
    spot_id: 8,
    name: '奈良公園',
    category: '公園・庭園',
    prefecture: '奈良県',
    address: '奈良県奈良市雑司町406-1',
    nearest_bus_stop_id: 108,
    description:
      '約1,200頭のシカが自由に歩き回る公園で、観光客と親しく接する様子が人気です。東大寺や春日大社などの名所も隣接しています。',
    map_info:
      '奈良市街地東部に広がる大規模な公園で、複数の世界遺産が含まれています。',
    latitude: 34.6851,
    longitude: 135.8394,
    image: 'https://picsum.photos/400/300?random=9',
    arr: [
      {
        Tourist: '観光地',
        Tag: '公園'
      }
    ]
  },
  {
    spot_id: 9,
    name: '青の洞窟',
    category: '海・ビーチ',
    prefecture: '沖縄県',
    address: '沖縄県本部町備瀬',
    nearest_bus_stop_id: 109,
    description:
      '海水中の太陽光が屈折して青く輝く人気のスポットで、シュノーケリングやダイビングを楽しめます。透明度の高い海水が特徴です。',
    map_info: '沖縄本島北部に位置し、青い海水が美しいことで有名です。',
    latitude: 26.7353,
    longitude: 127.9347,
    image: 'https://picsum.photos/400/300?random=10',
    arr: [
      {
        Tourist: '観光地',
        Tag: '海'
      }
    ]
  },
  {
    spot_id: 10,
    name: '浅草寺',
    category: '寺院・神社',
    prefecture: '東京都',
    address: '東京都台東区浅草2-3-1',
    nearest_bus_stop_id: 110,
    description:
      '東京で最も古い寺院で、雷門と仲見世通りが有名です。多くの観光客がおみくじを引いたり、お土産を購入したりします。',
    map_info: '東京メトロ浅草駅から徒歩で約5分の場所に位置しています。',
    latitude: 35.7147,
    longitude: 139.7966,
    image: 'https://picsum.photos/400/300?random=11',
    arr: [
      {
        Tourist: '観光地',
        Tag: '寺院'
      }
    ]
  }
])

/* 詳細ページへの遷移 */
function Tourist(id: number) {
  router.push({
    path: `/tourist-facilities/edit/${id}`
  })
}

/* フィルタリング機能 */
const filteredList = computed<TouristSpot[]>(() => {
  return items.value.filter((item) => {
    const matchKeyword = keyword.value
      ? item.name.includes(keyword.value)
      : true
    const matchRegion = selectedRegion.value
      ? item.prefecture.includes(selectedRegion.value)
      : true
    const matchTab =
      activeTab.value === 'すべて' ? true : item.category === activeTab.value
    return matchKeyword && matchRegion && matchTab
  })
})
</script>

<template>
  <v-container class="touristPage" fluid>
    <div class="regionpadding">
      <BaseHeader
        title="観光地・施設"
        :showBack="true"
        :showRightIcon="false"
      />
    </div>
    <div class="searchBar createPadding">
      <v-text-field
        v-model="keyword"
        density="compact"
        label="キーワードを入力"
        variant="outlined"
        hide-details
        clearable
      />
      <v-select
        v-model="selectedRegion"
        :items="regions"
        density="compact"
        label="地域を選択"
        variant="outlined"
        hide-details
        clearable
      />
    </div>

    <div class="tabs scrollX createPad">
      <button
        v-for="(tab, index) in tabs"
        :key="tab.id"
        class="tabBtn"
        :class="{
          active: activeTab === tab.name,
          lastTab: index === tabs.length - 1
        }"
        @click="activeTab = tab.name"
      >
        {{ tab.name }}
      </button>
    </div>

    <div class="cardGrid createBot">
      <v-card
        v-for="(item, index) in filteredList"
        :key="item.spot_id"
        class="card"
        elevation="1"
      >
        <div class="imageContainer">
          <v-img :src="item.image" height="120px" cover />
          <div class="imageLocation">
            {{ item.prefecture }} {{ item.address }}
          </div>
        </div>

        <div class="cardContentWrapper">
          <v-card-text class="cardText" @click="Tourist(item.spot_id)">
            <h3>{{ item.name }}</h3>
            <p class="clampedText">{{ item.description }}</p>
          </v-card-text>

          <v-card-actions class="cardActions">
            <v-chip rounded variant="outlined" size="small">
              {{ item.arr?.[0]?.Tourist }}
            </v-chip>
            <v-chip rounded variant="outlined" size="small">
              {{ item.arr?.[0]?.Tag }}
            </v-chip>
          </v-card-actions>
        </div>
      </v-card>
    </div>
  </v-container>
</template>

<style lang="scss" scoped>
.touristPage {
  padding: 0px !important;
  background-color: #e7f2fa;
}

.searchBar {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.tabs.scrollX {
  display: flex;
  gap: 0;
  overflow-x: auto;
  white-space: nowrap;
  margin-bottom: 16px;
  padding-bottom: 8px;

  &::-webkit-scrollbar {
    display: none;
  }
}

.tabBtn {
  flex: 0 0 auto;
  background: none;
  border: none;
  padding: 6px 16px;
  font-size: 14px;
  color: #000;
  font-weight: bold;
  position: relative;
  transition: all 0.2s;
  height: 40px;
  text-align: center;

  &:not(.lastTab) {
    border-right: 1px solid #7d7d7d;
  }

  &.active {
    color: #000;
    font-weight: bold;
    background-color: #acd1ed;

    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 15px solid #acd1ed;
    }
  }
}

.cardGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.card {
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;

  h3 {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 4px;
  }

  p {
    font-size: 12px;
    color: #555;
  }
}

.cardContentWrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.cardText {
  padding: 10px 10px 0 10px;
  flex: 1;
}

.imageContainer {
  position: relative;
  height: 120px;
}

.imageLocation {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  z-index: 1;
}

.clampedText {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 3);
}

:deep(.v-input__control) {
  margin-bottom: 12px;
}

:deep(.v-btn__content) {
  color: #000;
}
</style>
