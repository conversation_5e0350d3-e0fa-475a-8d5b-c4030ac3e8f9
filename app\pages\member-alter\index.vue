<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import type { UserFormData, ValidationResult } from '~/types/member-alter'
import {
  GENDER_OPTIONS,
  OCCUPATION_OPTIONS,
  EMAIL_SUBSCRIPTION_OPTIONS,
  RESIDENCE_OPTIONS
} from '~/types/member-alter'
import {
  MEMBER_ALTER_VALIDATION_RULES,
  validateMemberAlterField,
  validateMemberAlterForm,
  isMemberAlterFormValid
} from '~/pages/member-alter/member-alter'
import { useRouter } from 'vue-router'
import { useFormStore } from '@/stores/member-alter'

const router = useRouter()

/* フォームデータの初期化 */
const formData = reactive<UserFormData>({
  lastName: '',
  firstName: '',
  lastNameKana: '',
  firstNameKana: '',
  birthYear: '',
  birthMonth: '',
  birthDay: '',
  gender: '',
  phoneNumber: '',
  emailAddress: '',
  confirmEmailAddress: '',
  residence: null,
  cityAddressDetail: '',
  occupation: null,
  password: '',
  confirmPassword: '',
  emailSubscription: true,
  agreeToTerms: false,
  postalCode: '',
  prefecture: '',
  cityName: '',
  detailAddress: '',
  buildingName: ''
})

/* ダイアログと状態管理 */
const showDialog = ref(false)
const showCompleted = ref(false)
const errorField = ref('XXXXXXXXXXX')
const isSubmitting = ref(false)
const validationResults = reactive<{ [key: string]: ValidationResult }>({})

/* 生年月日エラーメッセージの計算 */
const birthErrorMessage = computed(() => {
  const yearErrors = getFieldErrors('birthYear')
  const monthErrors = getFieldErrors('birthMonth')
  const dayErrors = getFieldErrors('birthDay')

  if (yearErrors.length > 0) return yearErrors[0]
  if (monthErrors.length > 0) return monthErrors[0]
  if (dayErrors.length > 0) return dayErrors[0]

  return ''
})

/* エラーバナー表示判定 */
const showErrorBanner = computed(() => {
  return (
    Object.keys(validationResults).length > 0 &&
    !isMemberAlterFormValid(validationResults)
  )
})

/* 送信可能性の判定 */
const canSubmit = computed(() => {
  const hasValidated = Object.keys(validationResults).length > 0
  return hasValidated && isMemberAlterFormValid(validationResults)
})

/* フィールドエラー取得 */
const getFieldErrors = (fieldName: string): string[] => {
  return validationResults[fieldName]?.errors || []
}

/* 単一フィールドバリデーション */
const validateSingleField = (fieldName: keyof UserFormData) => {
  const value = formData[fieldName]
  const rules = MEMBER_ALTER_VALIDATION_RULES[fieldName]

  if (rules) {
    validationResults[fieldName] = validateMemberAlterField(
      value ?? '',
      rules,
      formData
    )
  }
}

/* フォーム送信処理 */
const handleSubmit = async () => {
  const results = validateMemberAlterForm(formData)
  Object.assign(validationResults, results)

  if (!isMemberAlterFormValid(validationResults)) {
    return
  }

  isSubmitting.value = true

  try {
    const formStore = useFormStore()
    formStore.saveSubmittedData(formData)

    await new Promise((resolve) => setTimeout(resolve, 2000))
    await router.push('/member-alter/member-alteration')
  } catch (error: any) {
    // エラー通知
    alert('データ保存中にエラーが発生しました: ' + (error.message || error))
    console.error(error)  // ログは残す
  } finally {
    isSubmitting.value = false
  }
}


/* トップページ移動 */
const tOPChange = () => {
  showCompleted.value = false
  router.push('/')
}

/* 退会ダイアログ表示 */
const withdra = () => {
  showDialog.value = true
}

/* ダイアログ閉じる */
const meeting = () => {
  showDialog.value = false
}

/* 退会確認処理 */
const confirmChange = () => {
  showDialog.value = false
  showCompleted.value = true
}

/* フォームデータ変更時のバリデーション再実行 */
watch(
  formData,
  () => {
    Object.keys(validationResults).forEach((fieldName) => {
      validateSingleField(fieldName as keyof UserFormData)
    })
  },
  { deep: true }
)
</script>

<template>
  <div class="registrationContainer">
     <div class="regionpadding">
      <BaseHeader
        title="会員情報変更"
        :showBack="true"
        :showRightIcon="false"
      />
    </div>

    <div v-if="showErrorBanner" class="Information">
      <div>会員情報変更</div>
    </div>
    <div v-if="showErrorBanner" class="errorBanner">
      <svg
        width="32"
        height="32"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M16 3L29.856 26H2.144L16 3Z" fill="#D00000" />
        <path
          d="M16 12V18"
          stroke="#FFFFFF"
          stroke-width="2"
          stroke-linecap="round"
        />
        <circle cx="16" cy="22" r="2" fill="#FFFFFF" />
      </svg>
      <span class="text">
        {{ errorField }}に誤りがあります。
        <br />
        再確認してください。
      </span>
    </div>

    <form @submit.prevent="handleSubmit" class="registrationForm">
      <section class="formSection">
        <div class="formGroup">
          <label class="fieldLabel required" for="lastName">お名前</label>
          <div class="nameRow">
            <v-text-field
              id="lastName"
              v-model="formData.lastName"
              placeholder="姓"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('lastName')"
              @blur="validateSingleField('lastName')"
            />
            <v-text-field
              id="firstName"
              v-model="formData.firstName"
              placeholder="名"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('firstName')"
              @blur="validateSingleField('firstName')"
            />
          </div>
        </div>

        <div class="formGroup">
          <label class="fieldLabel required" for="lastNameKana">フリガナ</label>
          <div class="nameRow">
            <v-text-field
              id="lastNameKana"
              v-model="formData.lastNameKana"
              placeholder="セイ"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('lastNameKana')"
              @blur="validateSingleField('lastNameKana')"
            />
            <v-text-field
              id="firstNameKana"
              v-model="formData.firstNameKana"
              placeholder="メイ"
              variant="outlined"
              density="comfortable"
              class="nameField"
              :error-messages="getFieldErrors('firstNameKana')"
              @blur="validateSingleField('firstNameKana')"
            />
          </div>
        </div>
      </section>

      <section class="formSection">
        <label class="fieldLabel required" for="birthYear">生年月日</label>
        <div class="birthContainer">
          <div class="birthRow">
            <v-text-field
              id="birthYear"
              v-model="formData.birthYear"
              placeholder="YYYY"
              variant="plain"
              :error="getFieldErrors('birthYear').length > 0"
              density="comfortable"
              class="birthField"
              @blur="validateSingleField('birthYear')"
            />
            <span class="birthUnit">年</span>
            <v-text-field
              id="birthMonth"
              v-model="formData.birthMonth"
              placeholder="MM"
              variant="plain"
              :error="getFieldErrors('birthMonth').length > 0"
              density="comfortable"
              class="birthField"
              @blur="validateSingleField('birthMonth')"
            />
            <span class="birthUnit">月</span>
            <v-text-field
              id="birthDay"
              v-model="formData.birthDay"
              placeholder="DD"
              variant="plain"
              :error="getFieldErrors('birthDay').length > 0"
              density="comfortable"
              class="birthField"
              @blur="validateSingleField('birthDay')"
            />
            <span class="birthUnit">日</span>
          </div>
          <p v-if="birthErrorMessage" class="errorMessage">
            {{ birthErrorMessage }}
          </p>
          <div>
            生年月日は変更できません。誤って登録した場合は窓口にお問い合わせください。
          </div>
        </div>
      </section>

      <section class="formSection">
        <label class="fieldLabel required" for="gender">性別</label>
        <v-radio-group
          id="gender"
          v-model="formData.gender"
          inline
          class="genderGroup"
          :error-messages="getFieldErrors('gender')"
          @update:model-value="validateSingleField('gender')"
        >
          <v-radio
            v-for="option in GENDER_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
            style="color: #26499d"
          />
        </v-radio-group>
      </section>

      <section class="formSection">
        <div class="formGroup">
          <label class="fieldLabel required" for="residence">居住地</label>
          <v-select
            id="residence"
            v-model="formData.residence"
            :items="RESIDENCE_OPTIONS"
            item-title="label"
            item-value="value"
            placeholder="居住地を選択してください"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('residence')"
            return-object
            @update:model-value="validateSingleField('residence')"
          />
        </div>

        <div class="formGroup">
          <label class="fieldLabel" for="cityAddressDetail">
            市区町村・番地以降
          </label>
          <v-text-field
            id="cityAddressDetail"
            v-model="formData.cityAddressDetail"
            placeholder="市区町村・番地以降を入力"
            variant="outlined"
            density="comfortable"
            @blur="validateSingleField('cityAddressDetail')"
          />

          <div
            v-if="getFieldErrors('cityAddressDetail').length"
            class="errorMessage"
          >
            {{ getFieldErrors('cityAddressDetail')[0] }}
          </div>
        </div>
      </section>

      <section class="formSection">
        <div class="formGroup">
          <label class="fieldLabel required" for="occupation">職業</label>
          <v-select
            id="occupation"
            v-model="formData.occupation"
            :items="OCCUPATION_OPTIONS"
            item-title="label"
            return-object
            item-value="value"
            placeholder="職業を選択してください"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('occupation')"
            @update:model-value="validateSingleField('occupation')"
          />
        </div>
      </section>

      <section class="formSection">
        <div class="formGroup">
          <label class="fieldLabel required" for="phoneNumber">電話番号</label>
          <v-text-field
            id="phoneNumber"
            v-model="formData.phoneNumber"
            placeholder="電話番号を入力"
            variant="outlined"
            density="comfortable"
            :error-messages="getFieldErrors('phoneNumber')"
            @blur="validateSingleField('phoneNumber')"
          />
        </div>

        <div class="formGroup">
          <label class="fieldLabel required" for="emailAddress">
            メールアドレス
          </label>
          <v-text-field
            id="emailAddress"
            v-model="formData.emailAddress"
            placeholder="メールアドレスを入力"
            variant="outlined"
            density="comfortable"
            type="email"
            :error-messages="getFieldErrors('emailAddress')"
            @blur="validateSingleField('emailAddress')"
          />
        </div>

        <div class="formGroup">
          <label class="fieldLabel required" for="confirmEmailAddress">
            メールアドレス（確認用）
          </label>
          <v-text-field
            id="confirmEmailAddress"
            v-model="formData.confirmEmailAddress"
            placeholder="メールアドレスを再入力"
            variant="outlined"
            density="comfortable"
            type="email"
            :error-messages="getFieldErrors('confirmEmailAddress')"
            @blur="validateSingleField('confirmEmailAddress')"
          />
        </div>
      </section>

      <section class="formSection">
        <label class="fieldLabel" for="emailSubscription">
          メールマガジン配信
        </label>
        <v-radio-group
          id="emailSubscription"
          v-model="formData.emailSubscription"
          class="subscriptionGroup"
        >
          <v-radio
            v-for="option in EMAIL_SUBSCRIPTION_OPTIONS"
            :key="String(option.value)"
            :label="option.label"
            :value="option.value"
            style="color: #26499d"
          />
        </v-radio-group>
        <p class="fieldHint">
          利用規約をお読みいただき、「利用規約に同意する」にチェックをしてください
        </p>
      </section>

      <div class="submitContainer">
        <v-btn
          type="submit"
          class="submitBtn"
          color="primary"
          size="large"
          block
          :loading="isSubmitting"
          :disabled="!canSubmit"
        >
          登録内容を確認する
        </v-btn>
      </div>
    </form>

    <div class="withdrawalText flex">
      <p @click="withdra">会員を退会する</p>
    </div>

    <v-dialog v-model="showDialog" max-width="500">
      <template v-slot:default>
        <v-card>
          <v-card-title>会員退会確認</v-card-title>
          <v-card-text>
            退会のお手続きをします。
            すでにお取りいただいた予約の情報は削除されません。よろしければ「退会する」ボタンを押してください。
          </v-card-text>
          <v-card-actions>
            <v-btn color="primary" @click="confirmChange" class="confirmBtn">
              退会する
            </v-btn>
          </v-card-actions>
          <v-card-actions>
            <v-btn color="primary" @click="meeting" class="meetingBtn">
              閉じる
            </v-btn>
          </v-card-actions>
        </v-card>
      </template>
    </v-dialog>

    <v-dialog v-model="showCompleted" max-width="500">
      <template v-slot:default>
        <v-card>
          <v-card-title>会員退会完了</v-card-title>
          <v-card-text>
            会員の退会が完了しました。
            退会完了メールをお送りしましたので、ご確認ください。
          </v-card-text>
          <v-card-actions>
            <v-btn color="primary" @click="tOPChange" class="confirmBtns">
              TOPへ
            </v-btn>
          </v-card-actions>
        </v-card>
      </template>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
.submitContainer :deep(.v-btn.submitBtn) {
  background-color: #26499d !important;
  color: #ffffff !important;
}

.submitContainer :deep(.v-btn.submitBtn:disabled) {
  background-color: #dfdfdf !important;
  color: #7d7d7d !important;
}

$primaryColor: #1976d2;
$errorColor: #e74c3c;
$warningColor: #f39c12;
$textColor: #26499d;
$borderColor: #e0e0e0;
$backgroundColor: #f5f5f5;
$white: #fff;

@mixin flexCenter {
  display: flex;
  align-items: center;
}

@mixin flexBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin buttonReset {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

@mixin requiredAsterisk {
  &::after {
    content: ' *';
    color: $errorColor;
  }
}

.withdrawalText {
  margin: 32px 0;

  p {
    color: #26499d;
    text-decoration: underline;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
    text-decoration-color: #26499d;
    transition: all 0.2s;
  }
}

.registrationContainer {
  margin: 0 auto;
  min-height: 100vh;
  background-color: #fff;
}

.Information {
  margin: 16px 0 24px 20px;
  font-weight: bold;
}

.errorBanner {
  display: flex;
  align-items: center;
  padding: 0 20px;

  .text {
    margin-left: 10px;
    color: #d00000;
  }
}

.registrationForm {
  background-color: $white;
  border-radius: 8px;
  padding: 20px;
}

.formSection {
  margin-bottom: 24px;

  &:last-of-type {
    margin-bottom: 16px;
  }
}

.formGroup {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.fieldLabel {
  display: block;
  font-size: 14px;
  color: $textColor;
  margin-bottom: 8px;

  &.required {
    @include requiredAsterisk;
  }
}

.fieldHint {
  font-size: 12px;
  color: #000;
  margin-top: 4px;
  margin-bottom: 0;
  line-height: 1.4;
}

.nameRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .nameField {
    min-width: 0;
  }
}

.birthContainer {
  .birthRow {
    @include flexCenter;
    gap: 8px;
    flex-wrap: wrap;

    .birthField {
      flex: 1;
      max-width: 35px;
    }

    :deep(.v-field__input) {
      padding: 0 !important;
      text-align: end;
    }

    .birthUnit {
      height: 43px;
      font-size: 14px;
      white-space: nowrap;
    }
  }
}

.errorMessage {
  line-height: 12px;
  word-break: break-word;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  transition-duration: 150ms;
  color: #b00020;
  font-size: 12px;
  padding: 0 16px 16px 0;
}

.genderGroup {
  :deep(.v-selection-control-group) {
    flex-direction: row;
    gap: 24px;
  }
}

.subscriptionGroup {
  :deep(.v-selection-control-group) {
    flex-direction: row;
    gap: 24px;
  }
}

.passwordToggle {
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s;
  color: #26499d;
}

.termsContainer {
  .termsCheckbox {
    :deep(.v-label) {
      font-size: 14px;
      color: #000;
    }
  }
}

.termsHint {
  color: #26499d;
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

.submitContainer {
  margin-top: 32px;

  .submitBtn {
    height: 48px;
  }
}

:deep(.v-text-field) {
  .v-field {
    border-radius: 6px;
  }

  .v-field__input {
    font-size: 14px;
    padding: 12px 16px;
  }
}

.confirmBtn {
  background-color: #ed785f !important;
  color: white !important;
  width: 311px;
  height: 48px;
  border-radius: 8px !important;
  margin: 0 auto;
}

.meetingBtn {
  background-color: #e7f2fa !important;
  color: #26499d !important;
  width: 311px;
  height: 48px;
  border-radius: 8px !important;
  margin: 0 auto;
  border: 1px solid #9cbcd4;
  margin-bottom: 24px;
}

.confirmBtns {
  background-color: #26499d !important;
  color: white !important;
  width: 311px;
  height: 48px;
  border-radius: 8px !important;
  margin: 0 auto;
  margin-bottom: 24px;
}

:deep(.v-select) {
  .v-field {
    border-radius: 6px;
  }
}

:deep(.v-radio-group) {
  .v-selection-control {
    min-height: auto;
  }

  .v-label {
    font-size: 14px;
    color: $textColor;
  }
}

:deep(.v-checkbox) {
  .v-selection-control {
    min-height: auto;
  }
}

:deep(.v-radio-group .v-label) {
  font-size: 14px;
  color: #000;
}
</style>
