<script lang="ts" setup>
interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ダイアログの表示状態を管理
const isVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 確認ボタンのクリック処理
const handleConfirm = () => {
  emit('confirm')
  isVisible.value = false
}

// キャンセルボタンのクリック処理
const handleCancel = () => {
  emit('cancel')
  isVisible.value = false
}
</script>
<template>
  <v-dialog v-model="isVisible" max-width="343" persistent>
    <v-card class="cancel-confirmation-dialog">
      <div class="dialog-content">
        <!-- タイトル -->
        <h2 class="dialog-title">この便の予約をキャンセルしますか？</h2>

        <!-- 説明セクション -->
        <div class="description-section">
          <div class="description-text">
            選択された便の予約をキャンセルします。
            <br />
            キャンセルした便は復元できません。
          </div>
        </div>

        <!-- ボタンセクション -->
        <div class="button-section">
          <v-btn
            class="confirm-btn"
            variant="elevated"
            size="large"
            block
            @click="handleConfirm"
          >
            はい
          </v-btn>

          <v-btn
            class="cancel-btn"
            variant="outlined"
            rounded="4"
            size="large"
            block
            @click="handleCancel"
          >
            いいえ
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>
<style scoped>
.cancel-confirmation-dialog {
  border-radius: 10px !important;
  background: #ffffff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 320px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
  text-align: left;
  margin: 0;
  width: 311px;
}

.description-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 311px;
  height: 88px;
}

.description-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5714285714285714;
  color: #000000;
  text-align: left;
  width: 311px;
}

.button-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  padding: 24px 0 0;
  position: absolute;
  bottom: 24px;
  left: 16px;
  width: 311px;
}

.confirm-btn {
  width: 311px !important;
  height: 48px !important;
  background-color: #ed785f !important;
  color: #ffffff !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  border-radius: 4px !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.cancel-btn {
  width: 311px !important;
  height: 48px !important;
  background-color: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  color: #26499d !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 1.2 !important;
  padding: 14px 13.38px !important;
}
</style>
