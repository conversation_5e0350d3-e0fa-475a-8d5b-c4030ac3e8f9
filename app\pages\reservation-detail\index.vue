<script lang="ts" setup>
import PaymentStatus from './components/PaymentStatus.vue'
import TripCard from './components/TripCard.vue'
import StationDetailDialog from '~/components/StationDetailDialog.vue'
import ChangeCompletionDialog from './components/ChangeCompletionDialog.vue'
import CancelConfirmationDialog from './components/CancelConfirmationDialog.vue'
import CancelCompletionDialog from './components/CancelCompletionDialog.vue'
import CancelAllConfirmationDialog from './components/CancelAllConfirmationDialog.vue'
definePageMeta({
  footerPlaceholderHeight: 90
})

// URLクエリパラメータからstatusを取得
const route = useRoute()
const statusFromQuery = computed(
  () => (route.query.status as string) || '決済待ち'
)
// 設備情報
const amenities = [
  { name: '昼行便', icon: 'mdi-weather-sunny', color: 'orange' },
  { name: '4列シート' },
  { name: 'トイレ付' },
  { name: '座席指定' },
  { name: 'Wi-Fi' },
  { name: '女性安心' },
  { name: 'コンセント', type: 'normal' },
  { name: 'ブランケット', type: 'normal' },
  { name: 'アイマスク', type: 'normal' },
  { name: 'スリッパ', type: 'normal' }
]

// 復路があるかどうかの判定
const hasReturnTrip = ref(true)

// 便詳細データ配列（往路）
const tripDetails = ref([
  {
    tripNumber: '1便目',
    date: '3/21（金）',
    departure: '岡山',
    arrival: '大阪',
    departureTime: '08:00',
    arrivalTime: '13:00',
    departureStation: '岡山駅 西口',
    arrivalStation: '湊町バスターミナル（OCAT）',
    duration: '000時間00分',
    seatNumbers: 'A6、A7、B6、B7',
    passengers: [
      { type: '大人', detail: '男性 1 名、女性 1 名' },
      { type: '子供', detail: '男性 1 名、女性 1 名' }
    ],
    tripInfo: [
      { label: '運行会社', value: '○○バス' },
      { label: '乗車便名', value: '備前ウィング大阪線 岡山→大阪' },
      { label: '車輌番号', value: '1号車' }
    ],
    amenities: amenities,
    isNew: true
  },
  {
    tripNumber: '2便目',
    date: '3/21（金）',
    departure: '大阪',
    arrival: '東京',
    departureTime: '23:05',
    arrivalTime: '07:59',
    departureStation: '湊町バスターミナル（OCAT）',
    arrivalStation: 'バスターミナル東京八重洲',
    duration: '000時間00分',
    seatNumbers: 'B1、B2',
    passengers: [{ type: '大人', detail: '男性 1 名、女性 1 名' }],
    tripInfo: [
      { label: '運行会社', value: '○○バス' },
      { label: '乗車便名', value: '関西・東海ブリッジ号' },
      { label: '車輌番号', value: '1号車' }
    ],
    amenities: amenities,
    isNew: false
  }
])

// 復路便詳細データ配列
const returnTripDetails = ref([
  {
    tripNumber: '3便目',
    date: '3/25（火）',
    departure: '東京',
    arrival: '大阪',
    departureTime: '22:30',
    arrivalTime: '06:45',
    departureStation: 'バスターミナル東京八重洲',
    arrivalStation: '湊町バスターミナル（OCAT）',
    duration: '000時間00分',
    seatNumbers: 'C1、C2',
    passengers: [{ type: '大人', detail: '男性 1 名、女性 1 名' }],
    tripInfo: [
      { label: '運行会社', value: '○○バス' },
      { label: '乗車便名', value: '関西・東海ブリッジ号' },
      { label: '車輌番号', value: '2号車' }
    ],
    amenities: amenities,
    isNew: false
  },
  {
    tripNumber: '4便目',
    date: '3/25（火）',
    departure: '大阪',
    arrival: '岡山',
    departureTime: '14:20',
    arrivalTime: '19:15',
    departureStation: '湊町バスターミナル（OCAT）',
    arrivalStation: '岡山駅 西口',
    duration: '000時間00分',
    seatNumbers: 'D3、D4',
    passengers: [{ type: '大人', detail: '男性 1 名、女性 1 名' }],
    tripInfo: [
      { label: '運行会社', value: '○○バス' },
      { label: '乗車便名', value: '備前ウィング大阪線 大阪→岡山' },
      { label: '車輌番号', value: '2号車' }
    ],
    amenities: amenities,
    isNew: false
  }
])

// ステータスタグのクラスを取得する関数
const getStatusTagClass = (status: string) => {
  switch (status) {
    case '決済済':
      return 'status-paid'
    case '決済待ち':
      return 'status-pending'
    case '乗車済':
      return 'status-completed'
    case 'キャンセル済':
      return 'status-cancelled'
    default:
      return 'status-pending'
  }
}

// 地図ダイアログの表示状態を管理
const showFromToStationDialog = ref(false)

// 停留所詳細ダイアログの表示状態を管理
const showStationDetailDialog = ref(false)
const selectedStationName = ref('')

// 停留所詳細情報ダイアログの表示状態を管理
const showStationInfoDialog = ref(false)

// 地図プレビューのソース
const mapPreviewSrc = ref(
  'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d52522.52878166499!2d135.342561006546!3d34.6380806932309!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6000e9f83737a5e5%3A0x9e031ed1e92eefdf!2zMjAyNeW5tOaXpeacrOWbvemam-WNmuimp-S8muWNlOS8mg!5e0!3m2!1sja!2sjp!4v1744012296326!5m2!1sja!2sjp'
)

// 地図ダイアログを表示する関数
const showMapDialog = () => {
  showFromToStationDialog.value = true
}

// 地図ダイアログを閉じる関数
const closeMapDialog = () => {
  showFromToStationDialog.value = false
}

// 停留所詳細ダイアログを表示する関数
const showStationDialog = (stationName: string) => {
  selectedStationName.value = stationName
  showStationDetailDialog.value = true
}

// 停留所詳細ダイアログを閉じる関数
const closeStationDialog = () => {
  showStationDetailDialog.value = false
  selectedStationName.value = ''
}

// 停留所の詳細ボタンのクリック処理
const handleStationDetail = () => {
  console.log(
    '停留所の詳細ボタンがクリックされました:',
    selectedStationName.value
  )
  // 地図ダイアログを閉じて詳細情報ダイアログを開く
  // showStationDetailDialog.value = false
  showStationInfoDialog.value = true
}

// 便変更完了ダイアログの表示状態を管理
const showChangeCompletionDialog = ref(false)

// 取消確認ダイアログの表示状態を管理
const showCancelConfirmationDialog = ref(false)

// 取消完了ダイアログの表示状態を管理
const showCancelCompletionDialog = ref(false)

// 予約全体取消確認ダイアログの表示状態を管理
const showCancelAllConfirmationDialog = ref(false)

// すべてのダイアログを閉じる処理
const handleCloseAllDialogs = () => {
  showStationDetailDialog.value = false
  showStationInfoDialog.value = false
  selectedStationName.value = ''
}

// 支払い情報データ
const paymentData = ref({
  outboundTrips: [
    {
      tripNumber: '1便目',
      passengers: [
        {
          type: '大人',
          unitPrice: 3500,
          count: 2,
          totalPrice: 7000
        },
        {
          type: '子供',
          unitPrice: 1750,
          count: 2,
          totalPrice: 3500
        }
      ]
    },
    {
      tripNumber: '2便目',
      passengers: [
        {
          type: '大人',
          unitPrice: 4200,
          count: 2,
          totalPrice: 8400
        }
      ]
    }
  ],
  returnTrips: hasReturnTrip.value
    ? [
        {
          tripNumber: '3便目',
          passengers: [
            {
              type: '大人',
              unitPrice: 4200,
              count: 2,
              totalPrice: 8400
            }
          ]
        },
        {
          tripNumber: '4便目',
          passengers: [
            {
              type: '大人',
              unitPrice: 3500,
              count: 2,
              totalPrice: 7000
            }
          ]
        }
      ]
    : [],
  discount: {
    rate: 5,
    label: '5%乗継割引適用中',
    originalTotal: hasReturnTrip.value ? 37800 : 18900,
    discountAmount: hasReturnTrip.value ? 1890 : 945
  },
  finalTotal: hasReturnTrip.value ? 35910 : 17955
})

// 価格をフォーマットする関数
const formatPrice = (price: number) => {
  return price.toLocaleString('ja-JP')
}

// 返回顶部按钮的显示状态
const showBackToTop = ref(false)

// 监听滚动事件
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 返回顶部功能
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// イベントハンドラー
const handleCancelTrip = () => {
  console.log('便のキャンセル確認ダイアログを表示します')
  showCancelConfirmationDialog.value = true
}

const handleChangeTrip = () => {
  console.log('予約変更を開始しました')
}

const handleShowTicket = () => {
  console.log('乗車券を表示します')
}

const handleShowReceipt = () => {
  console.log('領収書を表示します')
}

// 決済を始めるボタンのクリック処理
const handleStartPayment = () => {
  console.log('決済を始めるボタンがクリックされました')
  showChangeCompletionDialog.value = true
}

// 便変更完了ダイアログの決済ボタン処理
const handlePaymentFromDialog = () => {
  console.log('ダイアログから決済を行うボタンがクリックされました')
  // 実際の決済処理をここに実装
}

// 便変更完了ダイアログの予約詳細ボタン処理
const handleGoToDetailFromDialog = () => {
  console.log('ダイアログから予約詳細へボタンがクリックされました')
  // 予約詳細ページへの遷移処理をここに実装
}

// 予約全体をキャンセルボタンのクリック処理
const handleCancelAllReservation = () => {
  console.log('予約全体をキャンセルボタンがクリックされました')
  showCancelAllConfirmationDialog.value = true
}

// 取消確認ダイアログの確認ボタン処理
const handleConfirmCancel = () => {
  console.log('予約のキャンセルが確認されました')
  // 実際のキャンセル処理をここに実装
  // 例：API呼び出し、状態更新など

  // キャンセル完了ダイアログを表示
  showCancelCompletionDialog.value = true
}

// 取消確認ダイアログのキャンセルボタン処理
const handleCancelCancel = () => {
  console.log('予約のキャンセルが取り消されました')
  // 特に処理は不要、ダイアログが閉じるだけ
}

// 取消完了ダイアログの閉じるボタン処理
const handleCloseCancelCompletion = () => {
  console.log('取消完了ダイアログが閉じられました')
  // 必要に応じてページ遷移やリロード処理を実装
}

// 予約全体取消確認ダイアログの確認ボタン処理
const handleConfirmCancelAll = () => {
  console.log('予約全体のキャンセルが確認されました')
  // 実際のキャンセル処理をここに実装
  // 例：API呼び出し、状態更新など

  // キャンセル完了ダイアログを表示
  showCancelCompletionDialog.value = true
}

// 予約全体取消確認ダイアログのキャンセルボタン処理
const handleCancelCancelAll = () => {
  console.log('予約全体のキャンセルが取り消されました')
  // 特に処理は不要、ダイアログが閉じるだけ
}

// 组件挂载时添加滚动监听
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>
<template>
  <!-- ページヘッダー -->
  <div class="page-header">
    <v-btn icon variant="text" color="primary" @click="$router.back()">
      <v-icon>mdi-arrow-left</v-icon>
    </v-btn>
    <h1 class="page-title">予約詳細</h1>
    <div class="header-spacer"></div>
  </div>
  <!-- 来路 -->
  <div class="from-section">
    <div class="reservation-list-item">
      <div class="reservation-header">
        <div class="status-and-number">
          <div class="status-tag" :class="getStatusTagClass(statusFromQuery)">
            <span class="status-text">{{ statusFromQuery }}</span>
          </div>
          <span class="reservation-number">予約番号：000000000000</span>
        </div>
      </div>

      <div class="reservation-content">
        <div class="date-section">
          <span class="boarding-date">3/21（金）乗車</span>
        </div>

        <div class="route-section">
          <span class="departure-city">岡山</span>
          <div class="arrow-icon">
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path
                d="M4 2L8 6L4 10"
                stroke="#9CBCD4"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <span class="arrival-city">大阪</span>
          <div class="arrow-icon">
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path
                d="M4 2L8 6L4 10"
                stroke="#9CBCD4"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <span class="final-city">東京</span>
        </div>
      </div>
    </div>

    <div class="reservation-detail">
      <PaymentStatus :status="statusFromQuery" />

      <!-- 往路 -->
      <div class="from-section">
        <h3 class="section-title-route">往路</h3>
        <template v-for="(trip, index) in tripDetails" :key="index">
          <!-- 便詳細カード -->
          <TripCard
            :id="`trip-detail-${index}`"
            :trip-number="trip.tripNumber"
            :trip-data="trip"
            :is-new="trip.isNew"
            :status="statusFromQuery"
            @cancel-trip="handleCancelTrip"
            @change-trip="handleChangeTrip"
            @show-ticket="handleShowTicket"
            @show-station="showStationDialog"
          />

          <!-- バス停案内（最後の便以外に表示） -->
          <div v-if="index < tripDetails.length - 1" class="bus-stop-guide">
            <v-icon color="primary" size="24">mdi-map-marker</v-icon>
            <span class="guide-text">次のバス停へはこちら</span>
            <v-btn
              variant="text"
              color="primary"
              size="small"
              class="map-link"
              @click="showMapDialog"
            >
              地図で表示
            </v-btn>
          </div>
        </template>
      </div>

      <!-- 復路 -->
      <div v-if="hasReturnTrip" class="to-section">
        <h3 class="section-title-route">復路</h3>
        <template
          v-for="(trip, index) in returnTripDetails"
          :key="`return-${index}`"
        >
          <!-- 便詳細カード -->
          <TripCard
            :id="`return-trip-detail-${index}`"
            :trip-number="trip.tripNumber"
            :trip-data="trip"
            :is-new="trip.isNew"
            :status="statusFromQuery"
            @cancel-trip="handleCancelTrip"
            @change-trip="handleChangeTrip"
            @show-ticket="handleShowTicket"
            @show-station="showStationDialog"
          />

          <!-- バス停案内（最後の便以外に表示） -->
          <div
            v-if="index < returnTripDetails.length - 1"
            class="bus-stop-guide"
          >
            <v-icon color="primary" size="24">mdi-map-marker</v-icon>
            <span class="guide-text">次のバス停へはこちら</span>
            <v-btn
              variant="text"
              color="primary"
              size="small"
              class="map-link"
              @click="showMapDialog"
            >
              地図で表示
            </v-btn>
          </div>
        </template>
      </div>
      <!-- 目的地までの参考ルート -->
      <div class="destination-route">
        <h3 class="section-title-title">目的地までの参考ルート</h3>
        <v-card class="route-card">
          <v-card-text class="route-content">
            <div class="arrival-info">
              <span class="label">到着地</span>
              <v-btn
                class="to-btn"
                variant="text"
                color="primary"
                size="small"
                @click="showMapDialog"
              >
                バスターミナル東京八重洲
              </v-btn>
            </div>

            <div class="route-line">
              <div class="line"></div>
              <div class="route-guide">
                <div class="guide-item">
                  <v-icon color="primary" size="24">mdi-map-marker</v-icon>
                  <span class="guide-text">目的地へはこちら</span>
                </div>
                <v-btn
                  class="map-btn"
                  variant="text"
                  color="primary"
                  size="small"
                  @click="showMapDialog"
                >
                  地図で表示
                </v-btn>
              </div>
            </div>

            <div class="destination-info">
              <span class="label">目的地</span>
              <span class="destination">皇居外苑</span>
            </div>
          </v-card-text>
        </v-card>
      </div>
    </div>
  </div>
  <!-- 復路 -->
  <div class="to-section"></div>
  <!-- お支払い情報 -->
  <div class="payment-info">
    <h3 class="section-title">お支払い情報</h3>
    <v-card class="payment-card">
      <v-card-text class="payment-content">
        <h4 class="order-title">注文内容</h4>

        <div class="trip-orders">
          <!-- 往路便情報を表示 -->
          <div class="route-section-payment">
            <h5 class="route-title">往路</h5>
            <div
              v-for="(trip, index) in paymentData.outboundTrips"
              :key="trip.tripNumber"
              class="trip-order"
            >
              <a :href="`#trip-detail-${index}`" class="bus-link">
                {{ trip.tripNumber }}
              </a>
              <div class="passenger-prices">
                <div
                  v-for="passenger in trip.passengers"
                  :key="passenger.type"
                  class="price-row"
                >
                  <span class="passenger-type">{{ passenger.type }}</span>
                  <span class="price-detail">
                    {{ formatPrice(passenger.unitPrice) }} 円 x
                    {{ passenger.count }}人 =
                    {{ formatPrice(passenger.totalPrice) }} 円
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 復路便情報を表示（復路がある場合のみ） -->
          <div
            v-if="hasReturnTrip && paymentData.returnTrips.length > 0"
            class="route-section-payment"
          >
            <h5 class="route-title">復路</h5>
            <div
              v-for="(trip, index) in paymentData.returnTrips"
              :key="trip.tripNumber"
              class="trip-order"
            >
              <a :href="`#return-trip-detail-${index}`" class="bus-link">
                {{ trip.tripNumber }}
              </a>
              <div class="passenger-prices">
                <div
                  v-for="passenger in trip.passengers"
                  :key="passenger.type"
                  class="price-row"
                >
                  <span class="passenger-type">{{ passenger.type }}</span>
                  <span class="price-detail">
                    {{ formatPrice(passenger.unitPrice) }} 円 x
                    {{ passenger.count }}人 =
                    {{ formatPrice(passenger.totalPrice) }} 円
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <v-divider class="my-4" />

        <!-- 割引・合計 -->
        <div class="total-section">
          <v-chip
            color="error"
            variant="tonal"
            size="small"
            class="discount-chip"
          >
            {{ paymentData.discount.label }}
          </v-chip>

          <div class="discount-price">
            <span class="original-price">
              {{ formatPrice(paymentData.discount.originalTotal) }}円
            </span>
          </div>

          <div class="total-price">
            <div class="total-label">
              <span class="total-text">総計</span>
            </div>
            <span class="total-amount">
              {{ formatPrice(paymentData.finalTotal) }}
            </span>
            <span class="currency">円</span>
            <span class="tax-included">（税込）</span>
          </div>
        </div>
        <!-- ステータスに応じたボタン表示 -->
        <div
          v-if="statusFromQuery !== '決済待ち'"
          class="payment-completed-actions"
        >
          <v-btn
            variant="outlined"
            color="primary"
            size="large"
            block
            class="receipt-btn"
            rounded="5"
            @click="handleShowReceipt"
          >
            {{
              statusFromQuery === '決済済'
                ? '領収書を表示'
                : statusFromQuery === '乗車済'
                ? '領収書ダウンロード'
                : statusFromQuery === 'キャンセル済'
                ? '払い戻し明細書ダウンロード'
                : '領収書を表示'
            }}
          </v-btn>
        </div>
      </v-card-text>
    </v-card>
  </div>
  <!-- 予約者情報 -->
  <div class="booker-info">
    <h3 class="section-title">予約者情報</h3>
    <v-card class="booker-card">
      <v-card-text class="booker-content">
        <div class="info-row">
          <span class="info-label">予約者</span>
          <span class="info-value">リョウビ　タロウ</span>
        </div>
        <div class="info-row">
          <span class="info-label">電話番号</span>
          <span class="info-value phone">XXXX-XXXX-XXXX</span>
        </div>
        <div class="info-row">
          <span class="info-label">メールアドレス</span>
          <span class="info-value email">XXXXXXX @example .com</span>
        </div>
      </v-card-text>
    </v-card>
    <!-- 予約全体をキャンセルボタン -->
    <v-btn
      color="error"
      variant="elevated"
      size="large"
      block
      class="cancel-all-btn"
      @click="handleCancelAllReservation"
    >
      予約全体をキャンセル
    </v-btn>
  </div>
  <!-- 返回顶部按钮 -->
  <div v-show="showBackToTop" class="back-to-top-btn" @click="scrollToTop">
    <div class="back-to-top-icon">
      <div class="icon-line"></div>
      <div class="icon-arrow">
        <div class="arrow-stem"></div>
      </div>
    </div>
  </div>

  <!-- 固定底部支払いトリガーエリア -->
  <div class="payment-trigger-area">
    <div class="payment-summary">
      <div class="discount-info">
        <v-chip
          color="error"
          variant="tonal"
          size="small"
          class="discount-chip-bottom"
        >
          {{ paymentData.discount.label }}
        </v-chip>
      </div>
      <div class="total-price-info">
        <span class="total-label-bottom">総計</span>
        <div class="price-container">
          <span class="total-amount-bottom">
            {{ formatPrice(paymentData.finalTotal) }}
          </span>
          <span class="currency-bottom">円</span>
        </div>
      </div>
    </div>
    <v-btn
      variant="elevated"
      size="large"
      class="payment-start-btn"
      rounded="7"
      @click="handleStartPayment"
    >
      決済を始める
    </v-btn>
  </div>

  <!-- 地図表示ダイアログ -->
  <v-dialog v-model="showFromToStationDialog" max-width="343" persistent>
    <v-card class="station-detail-dialog">
      <div class="station-dialog-content">
        <!-- ヘッダー -->
        <div class="station-header">
          <div class="station-title">次のバス停までの経路</div>
          <div class="station-text station-text-top">
            <span class="station-name">XXXXXXXXXXXX</span>
            <v-icon color="#7D7D7D" size="20">mdi-arrow-right</v-icon>
            <span class="station-name">XXXXXXXXXXXX</span>
          </div>
        </div>

        <!-- 地図エリア -->
        <div class="station-map-area-ft">
          <iframe
            v-if="mapPreviewSrc"
            :src="mapPreviewSrc"
            height="440"
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"
            style="border: 0"
            title="地図プレビュー"
            width="100%"
          />
        </div>

        <!-- 閉じるボタン -->
        <div class="station-dialog-buttons">
          <v-btn class="close-station-btn" @click="closeMapDialog" block>
            閉じる
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>

  <!-- 停留所詳細ダイアログ -->
  <v-dialog v-model="showStationDetailDialog" max-width="343" persistent>
    <v-card class="station-detail-dialog">
      <div class="station-dialog-content">
        <!-- ヘッダー -->
        <div class="station-header">
          <div class="station-title">停留所の場所</div>
          <v-btn
            class="station-detail-btn"
            @click="handleStationDetail"
            size="small"
          >
            停留所の詳細
          </v-btn>
        </div>

        <!-- 地図エリア -->
        <div class="station-map-area">
          <iframe
            v-if="mapPreviewSrc"
            :src="mapPreviewSrc"
            height="440"
            loading="lazy"
            referrerpolicy="no-referrer-when-downgrade"
            style="border: 0"
            title="地図プレビュー"
            width="100%"
          />
        </div>

        <!-- 閉じるボタン -->
        <div class="station-dialog-buttons">
          <v-btn class="close-station-btn" @click="closeStationDialog" block>
            閉じる
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>

  <!-- 停留所詳細情報ダイアログ -->
  <StationDetailDialog
    v-model="showStationInfoDialog"
    :station-name="selectedStationName"
    @close-all="handleCloseAllDialogs"
  />

  <!-- 便変更完了ダイアログ -->
  <ChangeCompletionDialog
    v-model="showChangeCompletionDialog"
    reservation-number="000000000000"
    payment-deadline="2024年8月5日（月）まで"
    @payment="handlePaymentFromDialog"
    @go-to-detail="handleGoToDetailFromDialog"
  />

  <!-- 取消確認ダイアログ -->
  <CancelConfirmationDialog
    v-model="showCancelConfirmationDialog"
    @confirm="handleConfirmCancel"
    @cancel="handleCancelCancel"
  />

  <!-- 取消完了ダイアログ -->
  <CancelCompletionDialog
    v-model="showCancelCompletionDialog"
    reservation-number="000000000000"
    @close="handleCloseCancelCompletion"
  />

  <!-- 予約全体取消確認ダイアログ -->
  <CancelAllConfirmationDialog
    v-model="showCancelAllConfirmationDialog"
    @confirm="handleConfirmCancelAll"
    @cancel="handleCancelCancelAll"
  />
</template>
<style scoped>
.reservation-detail {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 375px;
  margin: 10px auto;
  background-color: #ffffff;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
}
.reservation-list-item {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-bottom: 1px solid #dfdfdf;
  gap: 8px;
  margin: 0 auto;
  padding: 30px;
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-and-number {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  border-radius: 25px;
  gap: 10px;
  min-width: 58px;
  height: 24px;
}

.status-paid {
  background-color: #26499d;
  border: 1px solid #26499d;
}

.status-paid .status-text {
  color: #ffffff;
}

.status-pending {
  background-color: #d00000;
  border: 1px solid #d00000;
}

.status-pending .status-text {
  color: #ffffff;
}

.status-completed {
  background-color: #e7f2fa;
  border: 1px solid #26499d;
}

.status-completed .status-text {
  color: #26499d;
}

.status-cancelled {
  background-color: #ffdfdf;
  border: 1px solid #d00000;
}

.status-cancelled .status-text {
  color: #d00000;
}

.status-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  text-align: center;
}

.reservation-number {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #000000;
}

.reservation-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-section {
  display: flex;
  align-items: center;
}

.boarding-date {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.375;
  color: #000000;
}

.route-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.departure-city,
.arrival-city,
.final-city {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #000000;
}

.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
  margin: 0;
}

.header-spacer {
  width: 40px;
}

.reservation-detail > *:not(.page-header) {
  margin: 0 17px;
}

.reservation-detail > .page-header {
  margin: 0;
}

.bus-stop-guide {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 24px;
  margin: 0 auto !important;
}

.guide-text {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  white-space: nowrap;
  padding: 10px 0;
}

.map-link {
  font-size: 14px;
  font-weight: 500;
  text-decoration: underline;
}

.station-link {
  font-size: 14px;
  font-weight: 500;
  text-decoration: underline;
}
.section-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
  width: 341px;
  margin: 0 17px;
}
.section-title-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.section-title-route {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
  margin: 0 0 16px 0;
}

.to-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 32px;
}

.route-section-payment {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.route-title {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  margin: 0 0 8px 0;
  padding-left: 8px;
  border-left: 3px solid #26499d;
}
.destination-route {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.route-card {
  border: 1px solid #9cbcd4 !important;
  border-radius: 10px !important;
  width: 343px;
  margin: 0 auto;
  box-shadow: none;
}

.route-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px !important;
}

.arrival-info,
.destination-info {
  display: flex;
  align-items: center;
  gap: 25px;
}

.label {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.destination {
  font-size: 14px;
  color: #000000;
}

.route-line {
  position: relative;
  height: 49px;
}

.line {
  position: absolute;
  left: 31px;
  top: 0;
  width: 0;
  height: 30px;
  border-left: 2px solid #9cbcd4;
}

.route-guide {
  position: absolute;
  left: 79px;
  top: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.guide-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 375px;
  margin: 10px auto;
}

.payment-card {
  border: 1px solid #9cbcd4 !important;
  border-radius: 10px !important;
  width: 341px;
  box-shadow: none;
  margin: 0 17px;
}

.payment-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px !important;
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  margin: 0;
}

.trip-orders {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trip-order {
  display: flex;
  align-items: center;
  gap: 32px;
  width: 302px;
}

.passenger-prices {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-row {
  display: flex;
  align-items: center;
  width: 229px;
  height: 22px;
}

.passenger-type {
  white-space: nowrap;
  font-size: 14px;
  color: #000000;
  width: 28px;
}

.price-detail {
  margin-left: 32px;
  font-size: 14px;
  color: #000000;
  width: 169px;
}

.total-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  width: 302px;
}

.discount-chip {
  background-color: #ffe1e1 !important;
  color: #d00000 !important;
  font-size: 14px;
  font-weight: 500;
  border-radius: 19px;
}

.discount-price {
  width: 104px;
  height: 22px;
}

.original-price {
  font-size: 14px;
  color: #7d7d7d;
  text-decoration: line-through;
}

.total-price {
  display: flex;
  align-items: flex-end;
  gap: 2px;
}

.total-label {
  display: flex;
  align-items: center;
  width: 48px;
  height: 29px;
}

.total-text {
  font-size: 14px;
  color: #232323;
  margin-top: 8px;
}

.total-amount {
  font-size: 24px;
  font-weight: 700;
  color: #232323;
  line-height: 1.2;
}

.currency {
  font-size: 14px;
  color: #232323;
}

.tax-included {
  font-size: 14px;
  color: #000000;
}

/* 予約者情報 */
.booker-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 375px;
  margin: 10px auto;
}

.booker-card {
  border: 1px solid #9cbcd4 !important;
  border-radius: 10px !important;
  width: 341px;
  box-shadow: none;
  margin: 0 17px;
}

.booker-content {
  padding: 20px !important;
}

.info-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

.info-value {
  font-size: 14px;
  color: #000000;
  width: 151px;
}

.info-value.phone,
.info-value.email {
  color: #232323;
}

.payment-completed-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.receipt-btn {
  height: 48px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  border: 1px solid #9cbcd4 !important;
  color: #000000 !important;
}

.cancel-all-btn {
  height: 50px;
  border-radius: 35px;
  font-size: 16px;
  font-weight: 400;
  min-width: 90% !important;
  margin: 0 auto 30px !important;
  background-color: #ed785f !important;
}

/* 固定底部支払いトリガーエリア */
.payment-trigger-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 92px;
  background-color: #ffffff;
  border-top: 1px solid #cccbca;
  box-shadow: 0px -3px 10px 0px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 12px;
  margin: 0 auto;
  z-index: 1000;
}

.payment-summary {
  position: relative;
  width: 206px;
  height: 60px;
}

.discount-info {
  position: absolute;
  top: 0.5px;
  right: 0;
}

.discount-chip-bottom {
  background-color: #ffe1e1 !important;
  color: #d00000 !important;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.67;
  border-radius: 19px;
  padding: 2px 8px;
  height: auto;
  min-height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.total-price-info {
  position: absolute;
  top: 27px;
  left: 0;
  width: 190px;
  height: 29px;
}

.total-label-bottom {
  position: absolute;
  top: 8px;
  left: 0;
  width: 28px;
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.57;
  color: #232323;
}

.price-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.total-amount-bottom {
  position: absolute;
  top: 1px;
  right: 14px;
  width: 115px;
  height: 29px;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  color: #232323;
  text-align: right;
}

.currency-bottom {
  position: absolute;
  top: 8px;
  right: 0;
  width: 14px;
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.57;
  color: #232323;
}

.payment-start-btn {
  width: 129px;
  height: 60px;
  background-color: #ed785f !important;
  color: #ffffff !important;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2;
  border-radius: 4px;
  text-transform: none;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 14px 13.38px;
}

.station-detail-dialog {
  border-radius: 10px !important;
  background: #fff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  overflow: visible !important;
}

.station-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 24px 16px;
  width: 343px;
  height: 580px;
}

.station-header {
  width: 100%;
  position: relative;
}

.station-title {
  font: 400 20px/1.2 sans-serif;
  color: #000;
}

.station-map-area-ft {
  width: 311px;
  height: 375px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.station-dialog-buttons {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  width: 311px;
  position: absolute;
  bottom: 24px;
  left: 16px;
}

.close-station-btn {
  width: 311px !important;
  height: 48px !important;
  background: #e7f2fa !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font: 400 16px/1.2 sans-serif !important;
  color: #26499d !important;
  text-transform: none !important;
  box-shadow: none !important;
  padding: 14px 13.38px !important;
}

.close-station-btn:hover {
  background: #d6e9f5 !important;
}

.station-text {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 311px;
  height: 24px;
}

.station-text .station-name {
  font: 400 20px/1.2 sans-serif;
  color: #000;
  width: 137px;
  height: 24px;
  display: flex;
  align-items: center;
}

.station-text-top {
  margin-top: 15px;
}

.station-detail-btn {
  position: absolute;
  right: 0;
  top: -4px;
  width: auto !important;
  height: 32px !important;
  background: #fff !important;
  border: 1px solid #9cbcd4 !important;
  border-radius: 4px !important;
  font: 400 14px/1.2 sans-serif !important;
  color: #000 !important;
  padding: 6px 16px !important;
  min-width: auto !important;
}

.station-detail-btn:hover {
  background: #f5f5f5 !important;
}

.station-map-area {
  width: 311px;
  height: 412px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

/* 返回顶部按钮样式 */
.back-to-top-btn {
  position: fixed;
  bottom: 120px;
  right: 0;
  width: 45px;
  height: 45px;
  background: #26499d;
  border: 1px solid #9cbcd4;
  border-right: none;
  border-radius: 5px 0 0 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.back-to-top-btn:hover {
  background: #1e3a7a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.back-to-top-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}

.back-to-top-icon {
  position: relative;
  width: 23.33px;
  height: 26.25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon-line {
  width: 20px;
  height: 3px;
  background: #fff;
  border-radius: 1.5px;
  margin-bottom: 4px;
}

.icon-arrow {
  position: relative;
  width: 16px;
  height: 16px;
}

.icon-arrow::before,
.icon-arrow::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 10px;
  background: #fff;
  border-radius: 1.5px;
}

.icon-arrow::before {
  transform: rotate(45deg);
  left: 3px;
  top: 2px;
}

.icon-arrow::after {
  transform: rotate(-45deg);
  right: 3px;
  top: 2px;
}

.icon-arrow .arrow-stem {
  position: absolute;
  width: 3px;
  height: 12px;
  background: #fff;
  border-radius: 1.5px;
  left: 50%;
  top: 6px;
  transform: translateX(-50%);
}
</style>
