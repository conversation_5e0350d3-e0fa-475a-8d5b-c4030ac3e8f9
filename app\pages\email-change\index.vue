<script lang="ts" setup>
import type { EmailChangeResponse } from '~/types/email-change'

definePageMeta({
  layout: 'default'
})

// フォーム参照
const formRef = ref<any>(null)

// リアクティブデータ
const email = ref('')
const hasEmailError = ref(false)

// バリデーションルール
const validationRules = {
  email: [
    (v: string) => !!v || 'メールアドレスを入力してください',
    (v: string) => {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return pattern.test(v) || '有効なメールアドレスを入力してください'
    }
  ]
}

// フォームの有効性をチェック
const isFormValid = computed(() => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return email.value.length > 0 && emailPattern.test(email.value)
})

// メール入力時の処理
const onEmailInput = () => {
  hasEmailError.value = false

  if (email.value.length > 0) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    hasEmailError.value = !emailPattern.test(email.value)
  }
}

// ログインページに戻る
const goBack = () => {
  navigateTo('/login')
}

// フォーム送信処理
const handleSubmit = async (event?: Event) => {
  if (!isFormValid.value) {
    hasEmailError.value = true
    return
  }
  try {
    hasEmailError.value = false
    // 確認コード送信API呼び出し
    const response = await $fetch<EmailChangeResponse>(
      '/api/email-change/send-code',
      {
        method: 'POST',
        body: {
          email: email.value
        }
      }
    )
    if (response.success) {
      console.log('確認コード送信成功:', email.value)
      // 確認ページに遷移
      navigateTo({
        path: '/email-change/verification',
        query: { email: email.value }
      })
    } else {
      throw new Error(response.message || '確認コードの送信に失敗しました')
    }
  } catch (error) {
    // エラー時も確認ページに遷移（開発用）
    navigateTo({
      path: '/email-change/verification',
      query: { email: email.value }
    })
    console.error('確認コード送信エラー:', error)
  }
}
</script>
<template>
  <div class="forgot-password-page">
    <div class="page-header">
      <div class="header-content">
        <button class="back-button" @click="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M7.05 12L13.6 5.45L12.19 4.04L4.23 12L12.19 19.96L13.6 18.55L7.05 12Z"
              fill="#26499D"
            />
          </svg>
        </button>
        <h1 class="page-title">メールアドレス変更</h1>
        <div class="header-spacer"></div>
      </div>
    </div>

    <div class="main-content">
      <div class="form-container">
        <p class="description-text">
          メールボックスをリセットするためのコードが登録されたメールアドレスに送信されます。
        </p>

        <v-form ref="formRef" class="forgot-password-form">
          <div class="input-group">
            <div class="input-label">
              <span class="label-text">メールアドレス</span>
            </div>
            <div class="input-field">
              <v-text-field
                v-model="email"
                type="email"
                :rules="validationRules.email"
                variant="outlined"
                density="comfortable"
                hide-details="auto"
                class="custom-text-field"
                :class="{ 'error-field': hasEmailError }"
                @input="onEmailInput"
              />
            </div>

            <div v-if="hasEmailError" class="error-message-text">
              有効なメールアドレスを入力してください
            </div>
          </div>

          <div class="button-section">
            <button
              type="button"
              :disabled="!isFormValid"
              :class="['submit-btn', { active: isFormValid }]"
              @click="handleSubmit"
            >
              <span>送信する</span>
            </button>
          </div>
        </v-form>
      </div>
    </div>
  </div>
</template>
<style scoped>
.forgot-password-page {
  width: 375px;
  height: 672px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  overflow: hidden;
}

.page-header {
  background-color: #ffffff;
  padding: 12px 16px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-content {
  width: 343px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
  margin: 0;
  text-align: center;
}

.header-spacer {
  width: 24px;
}

.main-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.form-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.description-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5625;
  color: #000000;
  margin: 0;
  text-align: left;
  width: 100%;
}

.forgot-password-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 343px;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.label-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #26499d;
}

.input-field {
  width: 100%;
}

.error-message-text {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.2;
  color: #d00000;
  margin-top: 4px;
}

.button-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.submit-btn {
  width: 343px;
  height: 48px;
  background-color: #dfdfdf;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  color: #7d7d7d;
  cursor: not-allowed;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background-color: #ed785f;
  color: #ffffff;
  cursor: pointer;
}

.submit-btn.active:hover {
  background-color: #d66b4a;
}

.submit-btn.loading {
  background-color: #ed785f;
  color: #ffffff;
  cursor: not-allowed;
}

.footer {
  background-color: #9cbcd4;
  padding: 22px 0 51px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90px;
}

.copyright {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  color: #ffffff;
  margin: 0;
}

:deep(.custom-text-field .v-field) {
  border: 2px solid #dfdfdf;
  border-radius: 2px;
  background-color: #ffffff;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

:deep(.custom-text-field .v-field--focused) {
  border-color: #26499d;
}

:deep(.custom-text-field.error-field .v-field) {
  border-color: #d00000;
}

:deep(.custom-text-field .v-field__input) {
  padding: 10px 18px;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  min-height: auto;
}

:deep(.custom-text-field .v-field__outline) {
  display: none;
}

:deep(.custom-text-field .v-messages) {
  display: none;
}

@media (max-width: 375px) {
  .forgot-password-page {
    width: 100%;
    max-width: 375px;
  }

  .input-group,
  .submit-btn {
    width: 100%;
    max-width: 343px;
  }
}
</style>
